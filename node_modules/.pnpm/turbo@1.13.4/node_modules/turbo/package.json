{"name": "turbo", "version": "1.13.4", "description": "Turborepo is a high-performance build system for JavaScript and TypeScript codebases.", "repository": "https://github.com/vercel/turbo", "bugs": "https://github.com/vercel/turbo/issues", "homepage": "https://turbo.build/repo", "license": "MPL-2.0", "main": "./bin/turbo", "bin": {"turbo": "./bin/turbo"}, "files": ["bin"], "optionalDependencies": {"turbo-darwin-64": "1.13.4", "turbo-darwin-arm64": "1.13.4", "turbo-linux-64": "1.13.4", "turbo-linux-arm64": "1.13.4", "turbo-windows-64": "1.13.4", "turbo-windows-arm64": "1.13.4"}, "scripts": {"postversion": "node bump-version.js"}}