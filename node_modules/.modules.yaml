hoistPattern:
  - '*'
hoistedDependencies:
  /turbo-darwin-64/1.13.4:
    turbo-darwin-64: private
  /turbo-darwin-arm64/1.13.4:
    turbo-darwin-arm64: private
  /turbo-linux-64/1.13.4:
    turbo-linux-64: private
  /turbo-linux-arm64/1.13.4:
    turbo-linux-arm64: private
  /turbo-windows-64/1.13.4:
    turbo-windows-64: private
  /turbo-windows-arm64/1.13.4:
    turbo-windows-arm64: private
  /undici-types/6.21.0:
    undici-types: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.15.0
pendingBuilds: []
prunedAt: Sat, 28 Jun 2025 07:23:26 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - /turbo-darwin-64/1.13.4
  - /turbo-linux-64/1.13.4
  - /turbo-linux-arm64/1.13.4
  - /turbo-windows-64/1.13.4
  - /turbo-windows-arm64/1.13.4
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
