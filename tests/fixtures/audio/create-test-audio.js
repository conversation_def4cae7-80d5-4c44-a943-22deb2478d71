/**
 * Creates a test audio file for E2E testing
 * This would normally be a pre-recorded WAV file saying "I have a severe headache rated 8 out of 10"
 * For testing purposes, we create a mock file structure
 */

const fs = require('fs');
const path = require('path');

// Create a minimal WAV file structure for testing
// In real scenario, this would be an actual recording
function createMockWAVFile() {
  // WAV file header (44 bytes) + minimal audio data
  const header = Buffer.alloc(44);
  
  // RIFF header
  header.write('RIFF', 0);
  header.writeUInt32LE(36, 4); // File size - 8
  header.write('WAVE', 8);
  
  // Format chunk
  header.write('fmt ', 12);
  header.writeUInt32LE(16, 16); // Chunk size
  header.writeUInt16LE(1, 20); // Audio format (PCM)
  header.writeUInt16LE(1, 22); // Number of channels
  header.writeUInt32LE(16000, 24); // Sample rate
  header.writeUInt32LE(32000, 28); // Byte rate
  header.writeUInt16LE(2, 32); // Block align
  header.writeUInt16LE(16, 34); // Bits per sample
  
  // Data chunk
  header.write('data', 36);
  header.writeUInt32LE(0, 40); // Data size
  
  // Create 3 seconds of mock audio data (silence)
  const sampleRate = 16000;
  const duration = 3; // seconds
  const audioData = Buffer.alloc(sampleRate * duration * 2); // 16-bit samples
  
  // Update data size in header
  header.writeUInt32LE(audioData.length, 40);
  header.writeUInt32LE(audioData.length + 36, 4);
  
  return Buffer.concat([header, audioData]);
}

// Create the test audio file
const audioFilePath = path.join(__dirname, 'test-symptom-recording.wav');
const mockWAVData = createMockWAVFile();

fs.writeFileSync(audioFilePath, mockWAVData);

console.log(`✅ Created mock test audio file: ${audioFilePath}`);
console.log(`📊 File size: ${mockWAVData.length} bytes`);
console.log(`⏱️  Duration: 3 seconds (simulated)`);
console.log(`🎤 Content: "I have a severe headache rated 8 out of 10" (simulated)`);

// Create metadata file
const metadataPath = path.join(__dirname, 'test-symptom-recording.json');
const metadata = {
  filename: 'test-symptom-recording.wav',
  duration: 3.0,
  sampleRate: 16000,
  channels: 1,
  bitDepth: 16,
  simulatedTranscription: 'I have a severe headache rated 8 out of 10',
  expectedEntities: [
    {
      text: 'severe headache',
      type: 'symptom',
      confidence: 0.94
    },
    {
      text: '8 out of 10',
      type: 'severity_scale', 
      confidence: 0.89
    }
  ],
  createdAt: new Date().toISOString(),
  purpose: 'E2E testing for SymptomOS audio recording flow'
};

fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));

console.log(`📋 Created metadata file: ${metadataPath}`);
console.log('🎯 Test audio fixtures ready for Playwright E2E tests');