import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting SymptomOS E2E test setup...');
  
  // Verify that required services are available
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Wait for patient app to be ready
    console.log('📱 Checking patient mobile app...');
    await page.goto('http://localhost:19006', { waitUntil: 'networkidle', timeout: 60000 });
    console.log('✅ Patient app is ready');
    
    // Wait for doctor app to be ready  
    console.log('🖥️  Checking doctor desktop app...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle', timeout: 60000 });
    console.log('✅ Doctor app is ready');
    
    // Prepare test data/state if needed
    console.log('📊 Setting up test data...');
    
    // Could setup test patients, mock data, etc.
    await page.evaluate(() => {
      // Setup mock localStorage data
      localStorage.setItem('test-mode', 'true');
      localStorage.setItem('mock-granite-enabled', 'true');
    });
    
    console.log('✅ Global setup completed successfully');
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

export default globalSetup;