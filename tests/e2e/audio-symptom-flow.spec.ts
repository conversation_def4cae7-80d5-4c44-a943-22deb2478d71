import { test, expect, Page } from '@playwright/test';
import path from 'path';

// Test configuration
const TEST_CONFIG = {
  patientAppUrl: 'http://localhost:19006', // Expo dev server
  doctorAppUrl: 'http://localhost:3000',   // Electron app web interface
  audioFixturePath: path.join(__dirname, '../fixtures/audio/test-symptom-recording.wav'),
  testTimeout: 30000,
  nodeAppearanceTimeout: 5000,
};

// Mock audio file - 3 second recording saying "I have a severe headache rated 8 out of 10"
const createMockAudioFile = async () => {
  // In a real implementation, this would be a pre-recorded WAV file
  // For testing, we'll create a mock file or use MediaRecorder API simulation
  return {
    name: 'test-symptom-recording.wav',
    mimeType: 'audio/wav',
    buffer: Buffer.from('mock-audio-data'), // Mock data for testing
  };
};

test.describe('Audio Symptom Recording E2E Flow', () => {
  let patientPage: Page;
  let doctorPage: Page;

  test.beforeEach(async ({ browser }) => {
    // Set up both apps
    const patientContext = await browser.newContext({
      permissions: ['microphone'], // Grant microphone permission
      viewport: { width: 375, height: 667 }, // Mobile viewport for patient app
    });
    
    const doctorContext = await browser.newContext({
      viewport: { width: 1920, height: 1080 }, // Desktop viewport for doctor app
    });

    patientPage = await patientContext.newPage();
    doctorPage = await doctorContext.newPage();

    // Mock the audio recording functionality
    await patientPage.addInitScript(() => {
      // Mock MediaRecorder API
      (window as any).MediaRecorder = class MockMediaRecorder {
        state = 'inactive';
        ondataavailable: ((event: any) => void) | null = null;
        onstop: (() => void) | null = null;
        
        constructor(stream: MediaStream, options?: any) {
          console.log('Mock MediaRecorder created');
        }
        
        start() {
          this.state = 'recording';
          console.log('Mock recording started');
          
          // Simulate recording data after 3 seconds
          setTimeout(() => {
            if (this.ondataavailable) {
              const mockBlob = new Blob(['mock-audio-data'], { type: 'audio/webm' });
              this.ondataavailable({ data: mockBlob });
            }
          }, 3000);
        }
        
        stop() {
          this.state = 'inactive';
          console.log('Mock recording stopped');
          if (this.onstop) {
            this.onstop();
          }
        }
        
        pause() {
          this.state = 'paused';
        }
        
        resume() {
          this.state = 'recording';
        }
      };

      // Mock getUserMedia
      if (navigator.mediaDevices) {
        navigator.mediaDevices.getUserMedia = async (constraints) => {
          console.log('Mock getUserMedia called', constraints);
          return new MediaStream();
        };
      }
    });

    // Mock the Granite Gateway API responses
    await patientPage.route('**/v1/asr/**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          transcription: 'I have a severe headache rated 8 out of 10',
          confidence: 0.94,
          duration_ms: 3200,
          language_detected: 'en-US',
          processing_time_ms: 245,
        }),
      });
    });

    await patientPage.route('**/v1/parse/text**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          entities: [
            {
              text: 'severe headache',
              entity_type: 'symptom',
              start: 9,
              end: 24,
              confidence: 0.94,
              attributes: {
                severity: 8,
                body_location: 'head',
              },
            },
            {
              text: '8 out of 10',
              entity_type: 'severity_scale',
              start: 31,
              end: 42,
              confidence: 0.89,
              attributes: {
                scale_type: 'numeric_rating_scale',
                score: 8,
                max_score: 10,
              },
            },
          ],
          entity_count: 2,
          processing_time_ms: 156,
          text_length: 43,
          confidence_threshold: 0.7,
        }),
      });
    });

    // Mock the doctor app API to receive timeline updates
    await doctorPage.route('**/api/patients/*/timeline**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          timeline: [
            {
              id: 'new-symptom-001',
              type: 'symptom',
              label: 'Severe Headache',
              timestamp: new Date().toISOString(),
              severity: 8,
              confidence: 0.94,
              metadata: {
                transcription: 'I have a severe headache rated 8 out of 10',
                voiceRecordingId: 'rec_' + Date.now(),
                extractedEntities: [
                  { text: 'severe headache', type: 'symptom', confidence: 0.94 },
                  { text: '8 out of 10', type: 'severity_scale', confidence: 0.89 },
                ],
              },
            },
          ],
        }),
      });
    });
  });

  test.afterEach(async () => {
    await patientPage.close();
    await doctorPage.close();
  });

  test('should record symptom and show in doctor timeline', async () => {
    // Step 1: Navigate to patient app recording screen
    await patientPage.goto(TEST_CONFIG.patientAppUrl);
    await expect(patientPage).toHaveTitle(/SymptomOS/);
    
    // Navigate to record screen (assuming it's accessible from home)
    await patientPage.click('[data-testid="record-symptoms-button"]', { timeout: 10000 });
    
    // Wait for recording interface to load
    await expect(patientPage.locator('[data-testid="record-button"]')).toBeVisible();
    
    // Step 2: Start recording
    await patientPage.click('[data-testid="record-button"]');
    
    // Verify recording started
    await expect(patientPage.locator('[data-testid="recording-indicator"]')).toBeVisible();
    await expect(patientPage.locator('[data-testid="record-button"]')).toHaveClass(/recording/);
    
    // Wait for the mock 3-second recording
    await patientPage.waitForTimeout(3500);
    
    // Step 3: Stop recording
    await patientPage.click('[data-testid="record-button"]');
    
    // Verify recording stopped and processing begins
    await expect(patientPage.locator('[data-testid="recording-indicator"]')).not.toBeVisible();
    await expect(patientPage.locator('[data-testid="processing-indicator"]')).toBeVisible();
    
    // Wait for processing to complete
    await expect(patientPage.locator('[data-testid="success-message"]')).toBeVisible({ timeout: 10000 });
    
    // Verify success message shows symptom logged
    const successMessage = await patientPage.locator('[data-testid="success-message"]').textContent();
    expect(successMessage).toContain('Symptom logged');
    expect(successMessage).toContain('2 entities'); // Based on our mock response
    
    // Step 4: Navigate to doctor app
    await doctorPage.goto(TEST_CONFIG.doctorAppUrl);
    await expect(doctorPage).toHaveTitle(/Doctor Dashboard/);
    
    // Navigate to patient detail page (assuming patient ID is 1)
    await doctorPage.goto(`${TEST_CONFIG.doctorAppUrl}/patients/1`);
    
    // Ensure we're on the timeline tab
    await doctorPage.click('[data-testid="timeline-tab"]');
    
    // Step 5: Verify new node appears in timeline within 5 seconds
    const timelineRings = doctorPage.locator('[data-testid="timeline-rings"]');
    await expect(timelineRings).toBeVisible();
    
    // Look for the new symptom node
    const newSymptomNode = doctorPage.locator('[data-testid="ring-node"][data-type="symptom"]').last();
    await expect(newSymptomNode).toBeVisible({ timeout: TEST_CONFIG.nodeAppearanceTimeout });
    
    // Verify the node represents a severe headache
    await expect(newSymptomNode).toHaveAttribute('data-label', /headache/i);
    await expect(newSymptomNode).toHaveAttribute('data-severity', '8');
    
    // Step 6: Click on the new node to open details
    await newSymptomNode.click();
    
    // Verify detail drawer opens
    const detailDrawer = doctorPage.locator('[data-testid="node-detail-drawer"]');
    await expect(detailDrawer).toBeVisible();
    
    // Verify the drawer shows the transcription
    await expect(detailDrawer.locator('[data-testid="transcription"]')).toContainText(
      'I have a severe headache rated 8 out of 10'
    );
    
    // Verify extracted entities are shown
    await expect(detailDrawer.locator('[data-testid="extracted-entities"]')).toBeVisible();
    await expect(detailDrawer.locator('[data-entity="symptom"]')).toContainText('severe headache');
    await expect(detailDrawer.locator('[data-entity="severity_scale"]')).toContainText('8 out of 10');
    
    // Verify confidence scores are displayed
    await expect(detailDrawer.locator('[data-testid="confidence-score"]')).toContainText('94%');
  });

  test('should handle recording errors gracefully', async () => {
    // Mock a failed ASR response
    await patientPage.route('**/v1/asr/**', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'ASR service unavailable',
        }),
      });
    });

    await patientPage.goto(TEST_CONFIG.patientAppUrl);
    await patientPage.click('[data-testid="record-symptoms-button"]');
    
    // Start and stop recording
    await patientPage.click('[data-testid="record-button"]');
    await patientPage.waitForTimeout(3500);
    await patientPage.click('[data-testid="record-button"]');
    
    // Verify error handling
    await expect(patientPage.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(patientPage.locator('[data-testid="error-message"]')).toContainText('Processing failed');
    
    // Should show fallback demo mode
    await expect(patientPage.locator('[data-testid="demo-mode-indicator"]')).toBeVisible();
  });

  test('should show appropriate UI states during recording', async () => {
    await patientPage.goto(TEST_CONFIG.patientAppUrl);
    await patientPage.click('[data-testid="record-symptoms-button"]');
    
    // Initial state
    await expect(patientPage.locator('[data-testid="record-button"]')).toHaveClass(/primary/);
    await expect(patientPage.locator('[data-testid="record-button"]')).not.toHaveClass(/recording/);
    
    // Recording state
    await patientPage.click('[data-testid="record-button"]');
    await expect(patientPage.locator('[data-testid="record-button"]')).toHaveClass(/recording/);
    await expect(patientPage.locator('[data-testid="duration-timer"]')).toBeVisible();
    
    // Processing state
    await patientPage.waitForTimeout(3500);
    await patientPage.click('[data-testid="record-button"]');
    await expect(patientPage.locator('[data-testid="processing-spinner"]')).toBeVisible();
    
    // Completed state
    await expect(patientPage.locator('[data-testid="success-message"]')).toBeVisible({ timeout: 10000 });
  });

  test('should maintain data consistency between apps', async () => {
    // Record symptom in patient app
    await patientPage.goto(TEST_CONFIG.patientAppUrl);
    await patientPage.click('[data-testid="record-symptoms-button"]');
    await patientPage.click('[data-testid="record-button"]');
    await patientPage.waitForTimeout(3500);
    await patientPage.click('[data-testid="record-button"]');
    
    // Wait for success
    await expect(patientPage.locator('[data-testid="success-message"]')).toBeVisible();
    
    // Check doctor app timeline
    await doctorPage.goto(`${TEST_CONFIG.doctorAppUrl}/patients/1`);
    await doctorPage.click('[data-testid="timeline-tab"]');
    
    const symptomNode = doctorPage.locator('[data-testid="ring-node"][data-type="symptom"]').last();
    await expect(symptomNode).toBeVisible();
    
    // Click to open details
    await symptomNode.click();
    const drawer = doctorPage.locator('[data-testid="node-detail-drawer"]');
    
    // Verify data consistency
    await expect(drawer.locator('[data-testid="transcription"]')).toContainText(
      'I have a severe headache rated 8 out of 10'
    );
    await expect(drawer.locator('[data-testid="severity-display"]')).toContainText('8/10');
    await expect(drawer.locator('[data-testid="confidence-score"]')).toContainText('94%');
    await expect(drawer.locator('[data-testid="timestamp"]')).toBeVisible();
  });

  test('should handle concurrent recordings appropriately', async () => {
    await patientPage.goto(TEST_CONFIG.patientAppUrl);
    await patientPage.click('[data-testid="record-symptoms-button"]');
    
    // Start first recording
    await patientPage.click('[data-testid="record-button"]');
    await expect(patientPage.locator('[data-testid="recording-indicator"]')).toBeVisible();
    
    // Try to start another recording - should be disabled
    await expect(patientPage.locator('[data-testid="record-button"]')).toBeDisabled();
    
    // Stop recording
    await patientPage.waitForTimeout(3500);
    await patientPage.click('[data-testid="record-button"]');
    
    // Should be able to start new recording after completion
    await expect(patientPage.locator('[data-testid="success-message"]')).toBeVisible();
    await patientPage.click('[data-testid="record-another-button"]');
    await expect(patientPage.locator('[data-testid="record-button"]')).toBeEnabled();
  });
});

// Performance test
test.describe('Performance Tests', () => {
  test('should complete audio processing within performance thresholds', async ({ page }) => {
    await page.goto(TEST_CONFIG.patientAppUrl);
    
    const startTime = Date.now();
    
    // Complete full recording flow
    await page.click('[data-testid="record-symptoms-button"]');
    await page.click('[data-testid="record-button"]');
    await page.waitForTimeout(3000); // 3 second recording
    await page.click('[data-testid="record-button"]');
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    
    const totalTime = Date.now() - startTime;
    
    // Should complete within 15 seconds (3s recording + max 12s processing)
    expect(totalTime).toBeLessThan(15000);
  });
});

// Accessibility test
test.describe('Accessibility Tests', () => {
  test('should be keyboard navigable', async ({ page }) => {
    await page.goto(TEST_CONFIG.patientAppUrl);
    await page.click('[data-testid="record-symptoms-button"]');
    
    // Tab to record button
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid="record-button"]')).toBeFocused();
    
    // Space to activate
    await page.keyboard.press('Space');
    await expect(page.locator('[data-testid="recording-indicator"]')).toBeVisible();
    
    // Stop with Space
    await page.waitForTimeout(3500);
    await page.keyboard.press('Space');
    await expect(page.locator('[data-testid="processing-indicator"]')).toBeVisible();
  });
});