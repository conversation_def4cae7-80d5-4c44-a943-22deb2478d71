"""
Basic tests for CI validation

Simple tests that validate the Granite pipeline structure without external dependencies.
"""

import os
import sys


def test_repository_structure():
    """Test that repository has expected structure"""
    assert os.path.exists("services"), "Services directory should exist"
    assert os.path.exists("tests"), "Tests directory should exist"
    assert os.path.exists("packages"), "Packages directory should exist"


def test_granite_agent_directories():
    """Test that Granite agent directories exist"""
    expected_agents = [
        "asr-agent",
        "parser-service",  # Updated from parser-agent
        "embed-agent", 
        "summary-agent",
        "guardian-agent",
        "vision-agent",
        "validator-service"
    ]
    
    for agent in expected_agents:
        agent_path = os.path.join("services", agent)
        assert os.path.exists(agent_path), f"{agent} directory should exist"


def test_configuration_files():
    """Test that configuration files exist"""
    config_files = [
        "docker-compose.granite.yml",
        "GRANITE_PIPELINE.md"
    ]
    
    for config_file in config_files:
        assert os.path.exists(config_file), f"{config_file} should exist"
    
    # pytest.ini may be in root or service directories
    pytest_locations = ["pytest.ini", "services/parser-service/pytest.ini"]
    pytest_found = any(os.path.exists(loc) for loc in pytest_locations)
    assert pytest_found, "pytest.ini should exist in root or service directory"


def test_python_environment():
    """Test Python environment is working"""
    assert sys.version_info >= (3, 8), "Python 3.8+ required"
    
    # Test basic imports work
    import json
    import base64
    import time
    
    # Test basic functionality
    test_data = {"test": "data"}
    json_str = json.dumps(test_data)
    assert json.loads(json_str) == test_data
    
    # Test base64 encoding
    test_bytes = b"test data"
    encoded = base64.b64encode(test_bytes).decode('utf-8')
    decoded = base64.b64decode(encoded)
    assert decoded == test_bytes


def test_granite_pipeline_documentation():
    """Test that Granite pipeline documentation exists"""
    assert os.path.exists("GRANITE_PIPELINE.md"), "Granite pipeline documentation should exist"
    
    with open("GRANITE_PIPELINE.md", "r") as f:
        content = f.read()
        
    # Check for key sections
    assert "Granite Agent Pipeline" in content
    assert "granite-speech-3-3-8b" in content
    assert "granite-3-3-8b-instruct" in content
    assert "granite-embedding-107m-multilingual" in content
    assert "granite-guardian-3-8b" in content
    assert "granite-vision-3.2-2b" in content


def test_docker_compose_configuration():
    """Test Docker Compose configuration exists"""
    assert os.path.exists("docker-compose.granite.yml"), "Docker Compose file should exist"
    
    with open("docker-compose.granite.yml", "r") as f:
        content = f.read()
        
    # Check for key services
    assert "redis:" in content
    assert "asr-agent:" in content
    # Check for either parser-agent or parser-service
    assert ("parser-agent:" in content or "parser-service:" in content), "Parser service should be configured"
    assert "guardian-agent:" in content


def test_agent_main_files():
    """Test that agent main files exist"""
    agents_with_main = [
        "asr-agent",
        "parser-service",  # Updated from parser-agent
        "embed-agent", 
        "summary-agent",
        "guardian-agent",
        "vision-agent",
        "validator-service"
    ]
    
    for agent in agents_with_main:
        main_file = os.path.join("services", agent, "app", "main.py")
        if os.path.exists(main_file):
            # If main.py exists, check it has basic structure
            with open(main_file, "r") as f:
                content = f.read()
                assert "FastAPI" in content or "app" in content


def test_onion_graph_package():
    """Test OnionGraph package structure"""
    ogce_path = os.path.join("packages", "ogcontextengine")
    assert os.path.exists(ogce_path), "OnionGraph package should exist"
    
    # Check for key files
    pyproject_path = os.path.join(ogce_path, "pyproject.toml")
    if os.path.exists(pyproject_path):
        with open(pyproject_path, "r") as f:
            content = f.read()
            assert "ogcontextengine" in content


def test_ci_configuration():
    """Test CI configuration is valid"""
    ci_file = os.path.join(".github", "workflows", "ci.yml")
    assert os.path.exists(ci_file), "CI configuration should exist"
    
    with open(ci_file, "r") as f:
        content = f.read()
        
    # Check for key CI elements
    assert "granite-agent-tests" in content
    assert "python" in content.lower()
    assert "pytest" in content


if __name__ == "__main__":
    import pytest
    pytest.main([__file__, "-v"])
