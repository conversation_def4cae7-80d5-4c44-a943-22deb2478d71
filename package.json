{"name": "symptomos", "version": "0.1.0", "private": true, "description": "SymptomOS - Real-time, voice-first symptom tracking and medical memory system", "keywords": ["medical", "symptom-tracking", "voice-ai", "granite", "healthcare"], "author": "SymptomOS Team", "license": "MIT", "workspaces": ["apps/*", "packages/*", "services/*"], "packageManager": "pnpm@8.15.0", "engines": {"node": ">=20.0.0", "pnpm": ">=8.0.0"}, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean", "type-check": "turbo run type-check", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "docker:dev": "docker compose -f infra/docker/dev.compose.yml up --build", "docker:down": "docker compose -f infra/docker/dev.compose.yml down", "ibm-deploy": "make ibm-deploy", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:report": "playwright show-report"}, "devDependencies": {"@types/node": "^20.11.0", "prettier": "^3.2.0", "turbo": "^1.12.0", "typescript": "^5.3.0", "@playwright/test": "^1.40.0", "playwright": "^1.40.0"}, "turbo": {"pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "build/**", ".next/**", "out/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"]}, "test": {"dependsOn": ["^build"]}, "type-check": {"dependsOn": ["^build"]}, "clean": {"cache": false}}}}