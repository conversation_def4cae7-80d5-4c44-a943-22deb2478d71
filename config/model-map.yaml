# Granite Model → Agent Mapping Configuration
# Each IBM Granite model is tied to exactly one job in the SymptomOS pipeline
# Agent-per-task philosophy with safety guardrails

agents:
  asr-agent:
    model_id: "ibm/granite-speech-3-3-8b"
    purpose: "Speech-to-text conversion with medical domain optimization"
    runtime_hints:
      sample_rate: 16000  # 16 kHz
      domain: "medical"
      chunk_window: 15    # seconds
      max_latency: 2.0    # seconds
    fallback:
      model: "whisper-tiny"
      local: true
    cost_tier: "high"
    
  parser-agent:
    model_id: "ibm/granite-3-3-8b-instruct"
    purpose: "Intent/entity extraction with function calling"
    runtime_hints:
      temperature: 0.0
      top_p: 0.1
      max_tokens: 1024
      response_format: "json"
      schema_enforced: true
    tools:
      - "insert_nodes"
      - "extract_entities"
      - "classify_intent"
    cost_tier: "medium"
    
  embed-agent:
    model_id: "ibm/granite-embedding-107m-multilingual"
    purpose: "Vector embeddings for similarity search and trend detection"
    runtime_hints:
      cache_strategy: "node-hash"
      reuse_for_summaries: true
      batch_size: 32
    cost_tier: "low"  # $0.10 / M tokens
    
  summary-agent:
    model_id: "ibm/granite-3-3-8b-instruct"  # Same as parser for cold-start efficiency
    purpose: "Medical summaries and doctor chat with 30-day context"
    runtime_hints:
      temperature: 0.3
      top_p: 0.9
      max_tokens: 2048
      context_window: 131072  # 131k tokens for 30-day recap
      context_strategy: "sliding_window"
    tools:
      - "make_summary"
      - "answer_query"
      - "generate_report"
    cost_tier: "medium"
    
  guardian-agent:
    model_id: "ibm/granite-guardian-3-8b"
    purpose: "Safety and policy checking for all generated content"
    runtime_hints:
      temperature: 0.0  # Deterministic safety decisions
      response_format: "binary"
      rationale_required: true
    validation_rules:
      - "no_phi_leakage"
      - "medical_accuracy"
      - "harmful_content"
      - "policy_compliance"
    cost_tier: "critical"  # Always runs
    
  vision-agent:
    model_id: "ibm/granite-vision-3.2-2b"
    purpose: "Image analysis, OCR, and medical image captioning"
    runtime_hints:
      max_image_size: "2048x2048"
      supported_formats: ["jpg", "png", "tiff"]
      tasks: ["caption", "ocr", "label", "medical_analysis"]
    tools:
      - "insert_image_node"
      - "extract_text"
      - "analyze_medical_image"
    cost_tier: "high"
    optional: true
    
  dev-agent:
    model_id: "ibm/granite-3b-code-instruct"
    purpose: "Code generation for UI stubs (development only)"
    runtime_hints:
      temperature: 0.2
      max_tokens: 4096
      languages: ["typescript", "react", "tailwind"]
    environment: "development"
    production_enabled: false

# Global Configuration
global:
  retry_policy:
    max_retries: 3
    backoff_strategy: "exponential_jitter"
    timeout_seconds: 30
    
  security:
    phi_stripping: true
    at_rest_encryption: "ibm_key_protect"
    audit_logging: true
    
  fallback_strategy:
    asr_fallback: "whisper-tiny-local"
    parser_fallback: "regex_heuristics"
    embed_fallback: "sentence-transformers-local"
    
  cost_management:
    daily_token_limit: 1000000  # 1M tokens per day
    alert_threshold: 0.8        # Alert at 80% usage
    emergency_stop: 0.95        # Stop at 95% usage

# Message Bus Configuration
message_bus:
  type: "redis_streams"
  connection:
    host: "${REDIS_HOST:-localhost}"
    port: "${REDIS_PORT:-6379}"
    db: 0
  streams:
    audio_input: "audio:stream"
    image_input: "image:stream"
    parsed_entities: "entities:stream"
    embeddings: "embeddings:stream"
    summaries: "summaries:stream"
    safety_checks: "safety:stream"
    violations: "violations:stream"
  
# Service Discovery
services:
  asr-agent:
    port: 8010
    health_check: "/healthz"
    
  parser-agent:
    port: 8011
    health_check: "/healthz"
    
  embed-agent:
    port: 8012
    health_check: "/healthz"
    
  summary-agent:
    port: 8013
    health_check: "/healthz"
    
  guardian-agent:
    port: 8014
    health_check: "/healthz"
    
  vision-agent:
    port: 8015
    health_check: "/healthz"
    
  validator-service:
    port: 8020
    health_check: "/healthz"

# Testing Profiles
testing:
  profiles:
    unit:
      models: "mock"
      redis: "fake"
      
    e2e_offline:
      models: "local_fallback"
      redis: "embedded"
      
    e2e_cloud_happy:
      models: "granite_cloud"
      redis: "cloud"
      
    safety_red_team:
      models: "granite_cloud"
      redis: "cloud"
      focus: "security_validation"
