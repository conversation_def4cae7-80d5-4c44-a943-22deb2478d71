["tests/test_basic.py::test_agent_main_files", "tests/test_basic.py::test_ci_configuration", "tests/test_basic.py::test_configuration_files", "tests/test_basic.py::test_docker_compose_configuration", "tests/test_basic.py::test_granite_agent_directories", "tests/test_basic.py::test_granite_pipeline_documentation", "tests/test_basic.py::test_onion_graph_package", "tests/test_basic.py::test_python_environment", "tests/test_basic.py::test_repository_structure", "tests/test_granite_pipeline.py::TestE2ECloudHappy::test_complete_medical_workflow_mock", "tests/test_granite_pipeline.py::TestE2ECloudHappy::test_vision_medical_analysis_mock", "tests/test_granite_pipeline.py::TestE2EOffline::test_audio_to_graph_pipeline_mock", "tests/test_granite_pipeline.py::TestGranitePipeline::test_agent_configuration", "tests/test_granite_pipeline.py::TestGranitePipeline::test_pipeline_structure", "tests/test_granite_pipeline.py::TestPerformanceAndReliability::test_agent_health_monitoring_mock", "tests/test_granite_pipeline.py::TestPerformanceAndReliability::test_concurrent_requests_mock", "tests/test_granite_pipeline.py::TestSafetyRedTeam::test_emergency_stop_functionality_mock", "tests/test_granite_pipeline.py::TestSafetyRedTeam::test_injection_attempts_mock", "tests/test_granite_pipeline.py::TestSafetyRedTeam::test_phi_detection_mock", "tests/test_granite_pipeline.py::TestUnitTests::test_embedding_cache_strategy_mock", "tests/test_granite_pipeline.py::TestUnitTests::test_guardian_safety_check_mock", "tests/test_granite_pipeline.py::TestUnitTests::test_parser_entity_extraction_mock"]