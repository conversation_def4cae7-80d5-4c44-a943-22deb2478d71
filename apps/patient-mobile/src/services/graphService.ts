/**
 * Graph Service API Client
 * 
 * Handles communication with the OGCE graph service for creating and managing
 * medical nodes and relationships in the OnionGraphContextEngine.
 */

export interface GraphNode {
  id: string;
  node_type: string;
  properties: Record<string, any>;
  created_at?: string;
  updated_at?: string;
  curvature?: number;
}

export interface GraphEdge {
  id: string;
  source_id: string;
  target_id: string;
  relationship_type: string;
  properties: Record<string, any>;
  weight?: number;
  created_at?: string;
}

export interface CreateNodeRequest {
  node_type: string;
  properties: Record<string, any>;
  patient_id?: string;
  timestamp?: string;
}

export interface CreateNodeResponse {
  success: boolean;
  node_id: string;
  message: string;
  processing_time_ms: number;
}

export interface CreateImageNodeRequest {
  patient_id: string;
  image_uri: string;
  image_base64?: string;
  vision_analysis: {
    caption: string;
    ocr: string;
    labels: string[];
    medical_analysis?: any;
    safety_score: number;
    model_used: string;
  };
  metadata?: Record<string, any>;
}

export interface PatientGraphResponse {
  patient_id: string;
  nodes: GraphNode[];
  edges: GraphEdge[];
  node_count: number;
  edge_count: number;
  processing_time_ms: number;
}

export class GraphServiceError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public response?: any
  ) {
    super(message);
    this.name = 'GraphServiceError';
  }
}

export class GraphService {
  private baseUrl: string;
  private authToken: string;

  constructor(baseUrl: string = 'http://localhost:8030', authToken: string = 'sk-symptomos-patient-app') {
    this.baseUrl = baseUrl;
    this.authToken = authToken;
  }

  /**
   * Create a new node in the graph
   */
  async createNode(request: CreateNodeRequest): Promise<CreateNodeResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/nodes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: JSON.stringify({
          node_type: request.node_type,
          properties: request.properties,
          timestamp: request.timestamp || new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new GraphServiceError(
          `Failed to create node: ${response.status} ${response.statusText}`,
          response.status,
          errorData
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof GraphServiceError) {
        throw error;
      }
      throw new GraphServiceError(
        `Network error creating node: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Create an IMAGE node from vision analysis results
   */
  async createImageNode(request: CreateImageNodeRequest): Promise<CreateNodeResponse> {
    const nodeProperties = {
      patient_id: request.patient_id,
      entity_type: 'image',
      image_uri: request.image_uri,
      caption: request.vision_analysis.caption,
      ocr_text: request.vision_analysis.ocr,
      labels: request.vision_analysis.labels,
      safety_score: request.vision_analysis.safety_score,
      model_used: request.vision_analysis.model_used,
      medical_analysis: request.vision_analysis.medical_analysis,
      timestamp: new Date().toISOString(),
      source: 'patient_mobile_app',
      ...request.metadata,
    };

    // If image has medical analysis, include additional properties
    if (request.vision_analysis.medical_analysis) {
      nodeProperties.medical_findings = request.vision_analysis.medical_analysis.findings;
      nodeProperties.medical_recommendations = request.vision_analysis.medical_analysis.recommendations;
      nodeProperties.urgency_level = request.vision_analysis.medical_analysis.urgency_level;
      nodeProperties.medical_confidence = request.vision_analysis.medical_analysis.confidence;
    }

    return this.createNode({
      node_type: 'image',
      properties: nodeProperties,
      patient_id: request.patient_id,
    });
  }

  /**
   * Get patient graph data
   */
  async getPatientGraph(patientId: string): Promise<PatientGraphResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/graph/patient/${patientId}`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new GraphServiceError(
          `Failed to get patient graph: ${response.status} ${response.statusText}`,
          response.status,
          errorData
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof GraphServiceError) {
        throw error;
      }
      throw new GraphServiceError(
        `Network error getting patient graph: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Add a medical entity (symptom, medication, etc.)
   */
  async addMedicalEntity(
    entityType: string,
    entityData: Record<string, any>,
    patientId?: string
  ): Promise<CreateNodeResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/graph/entity`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: JSON.stringify({
          entity_type: entityType,
          entity_data: entityData,
          patient_id: patientId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new GraphServiceError(
          `Failed to add medical entity: ${response.status} ${response.statusText}`,
          response.status,
          errorData
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof GraphServiceError) {
        throw error;
      }
      throw new GraphServiceError(
        `Network error adding medical entity: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Check service health
   */
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/healthz`);
      
      if (!response.ok) {
        throw new GraphServiceError(`Health check failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof GraphServiceError) {
        throw error;
      }
      throw new GraphServiceError(
        `Health check network error: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Create a complete medical image entry with analysis
   */
  async createMedicalImageEntry(
    patientId: string,
    imageUri: string,
    visionAnalysis: CreateImageNodeRequest['vision_analysis'],
    additionalMetadata?: Record<string, any>
  ): Promise<{
    imageNode: CreateNodeResponse;
    relatedNodes: CreateNodeResponse[];
  }> {
    const results = {
      imageNode: await this.createImageNode({
        patient_id: patientId,
        image_uri: imageUri,
        vision_analysis: visionAnalysis,
        metadata: additionalMetadata,
      }),
      relatedNodes: [] as CreateNodeResponse[],
    };

    // If OCR text contains medical information, create additional nodes
    if (visionAnalysis.ocr && visionAnalysis.ocr.trim()) {
      try {
        // Create a note node for the OCR text
        const ocrNode = await this.addMedicalEntity('note', {
          content: visionAnalysis.ocr,
          source: 'image_ocr',
          related_image_node: results.imageNode.node_id,
          timestamp: new Date().toISOString(),
        }, patientId);
        
        results.relatedNodes.push(ocrNode);
      } catch (error) {
        console.warn('Failed to create OCR note node:', error);
      }
    }

    // If medical analysis found specific findings, create symptom nodes
    if (visionAnalysis.medical_analysis?.findings) {
      for (const finding of visionAnalysis.medical_analysis.findings) {
        try {
          const symptomNode = await this.addMedicalEntity('symptom', {
            description: finding,
            source: 'image_analysis',
            confidence: visionAnalysis.medical_analysis.confidence,
            related_image_node: results.imageNode.node_id,
            timestamp: new Date().toISOString(),
          }, patientId);
          
          results.relatedNodes.push(symptomNode);
        } catch (error) {
          console.warn('Failed to create symptom node from finding:', error);
        }
      }
    }

    return results;
  }
}

// Default service instance
export const graphService = new GraphService();
