/**
 * Vision Service API Client
 * 
 * Handles communication with the vision-agent service for image analysis,
 * OCR, and medical image processing using IBM Granite models.
 */

export interface VisionRequest {
  image_b64: string;
  tasks: ('caption' | 'ocr' | 'labels' | 'medical_analysis')[];
  medical_context?: string;
  patient_id?: string;
}

export interface VisionResponse {
  caption: string;
  ocr: string;
  labels: string[];
  medical_analysis?: {
    findings: string[];
    recommendations: string[];
    urgency_level: 'low' | 'medium' | 'high';
    confidence: number;
  };
  safety_score: number;
  processing_time: number;
  model_used: string;
  image_metadata: {
    width: number;
    height: number;
    format: string;
    size_bytes: number;
  };
  tasks_completed: string[];
}

export interface OCRRequest {
  image_b64: string;
  language?: string;
  document_type?: 'prescription' | 'lab_result' | 'medical_report' | 'general';
  medical_context?: string;
}

export interface OCRResponse {
  text: string;
  confidence: number;
  text_blocks: Array<{
    text: string;
    confidence: number;
    bounding_box: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  }>;
  detected_entities: Array<{
    text: string;
    type: string;
    confidence: number;
  }>;
  document_type: string;
  language: string;
  processing_time: number;
}

export interface UploadAnalysisRequest {
  file: File | Blob;
  tasks?: string[];
  medical_context?: string;
}

export class VisionServiceError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public response?: any
  ) {
    super(message);
    this.name = 'VisionServiceError';
  }
}

export class VisionService {
  private baseUrl: string;
  private authToken: string;

  constructor(baseUrl: string = 'http://localhost:8015', authToken: string = 'sk-symptomos-patient-app') {
    this.baseUrl = baseUrl;
    this.authToken = authToken;
  }

  /**
   * Analyze an image using base64 encoded data
   */
  async analyzeImage(request: VisionRequest): Promise<VisionResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/vision`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new VisionServiceError(
          `Vision analysis failed: ${response.status} ${response.statusText}`,
          response.status,
          errorData
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof VisionServiceError) {
        throw error;
      }
      throw new VisionServiceError(
        `Network error during vision analysis: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Perform OCR on an image
   */
  async extractText(request: OCRRequest): Promise<OCRResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/ocr`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new VisionServiceError(
          `OCR extraction failed: ${response.status} ${response.statusText}`,
          response.status,
          errorData
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof VisionServiceError) {
        throw error;
      }
      throw new VisionServiceError(
        `Network error during OCR extraction: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Upload and analyze an image file
   */
  async uploadAndAnalyze(request: UploadAnalysisRequest): Promise<VisionResponse> {
    try {
      const formData = new FormData();
      formData.append('file', request.file as any);
      
      if (request.tasks) {
        request.tasks.forEach(task => formData.append('tasks', task));
      }
      
      if (request.medical_context) {
        formData.append('medical_context', request.medical_context);
      }

      const response = await fetch(`${this.baseUrl}/v1/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new VisionServiceError(
          `File upload analysis failed: ${response.status} ${response.statusText}`,
          response.status,
          errorData
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof VisionServiceError) {
        throw error;
      }
      throw new VisionServiceError(
        `Network error during file upload: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Check service health
   */
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/healthz`);
      
      if (!response.ok) {
        throw new VisionServiceError(`Health check failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof VisionServiceError) {
        throw error;
      }
      throw new VisionServiceError(
        `Health check network error: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Analyze medical image with enhanced medical context
   */
  async analyzeMedicalImage(
    imageBase64: string,
    patientId?: string,
    medicalContext?: string
  ): Promise<VisionResponse> {
    const request: VisionRequest = {
      image_b64: imageBase64,
      tasks: ['caption', 'ocr', 'medical_analysis'],
      medical_context: medicalContext || 'Patient uploaded medical image for symptom tracking',
      patient_id: patientId,
    };

    return this.analyzeImage(request);
  }

  /**
   * Extract text from medical documents (prescriptions, lab results, etc.)
   */
  async extractMedicalText(
    imageBase64: string,
    documentType: OCRRequest['document_type'] = 'general'
  ): Promise<OCRResponse> {
    const request: OCRRequest = {
      image_b64: imageBase64,
      language: 'en',
      document_type: documentType,
      medical_context: 'Medical document text extraction',
    };

    return this.extractText(request);
  }
}

// Default service instance
export const visionService = new VisionService();

// Utility functions
export const convertImageToBase64 = async (imageUri: string): Promise<string> => {
  try {
    const response = await fetch(imageUri);
    const blob = await response.blob();
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = (reader.result as string).split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    throw new VisionServiceError(
      `Failed to convert image to base64: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};

export const validateImageFormat = (imageUri: string): boolean => {
  const supportedFormats = ['.jpg', '.jpeg', '.png', '.tiff', '.bmp'];
  const lowerUri = imageUri.toLowerCase();
  return supportedFormats.some(format => lowerUri.includes(format));
};
