import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  Modal,
  Dimensions,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
import { visionService, VisionResponse } from '../services/visionService';
import { graphService } from '../services/graphService';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface UploadSheetProps {
  visible: boolean;
  onClose: () => void;
  onImageSelected: (imageUri: string, imageBase64: string) => void;
  onAnalysisComplete?: (result: VisionAnalysisResult) => void;
  onImageNodeCreated?: (nodeId: string, success: boolean) => void;
  patientId?: string;
  loading?: boolean;
}

export interface VisionAnalysisResult {
  caption: string;
  ocr: string;
  labels: string[];
  medical_analysis?: any;
  safety_score: number;
  processing_time: number;
  model_used: string;
  image_metadata: {
    width: number;
    height: number;
    format: string;
    size_bytes: number;
  };
}

export const UploadSheet: React.FC<UploadSheetProps> = ({
  visible,
  onClose,
  onImageSelected,
  onAnalysisComplete,
  onImageNodeCreated,
  patientId = 'patient_001', // Default patient ID for demo
  loading = false,
}) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [imageBase64, setImageBase64] = useState<string>('');
  const [analysisResult, setAnalysisResult] = useState<VisionAnalysisResult | null>(null);
  const [analyzing, setAnalyzing] = useState(false);

  const requestPermissions = async () => {
    const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
    const { status: mediaLibraryStatus } = await MediaLibrary.requestPermissionsAsync();
    
    if (cameraStatus !== 'granted' || mediaLibraryStatus !== 'granted') {
      Alert.alert(
        'Permissions Required',
        'Camera and photo library access are required to upload medical images.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  const pickImageFromLibrary = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        setSelectedImage(asset.uri);
        setImageBase64(asset.base64 || '');
        onImageSelected(asset.uri, asset.base64 || '');
      }
    } catch (error) {
      console.error('Error picking image from library:', error);
      Alert.alert('Error', 'Failed to select image from library');
    }
  };

  const takePhoto = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        setSelectedImage(asset.uri);
        setImageBase64(asset.base64 || '');
        onImageSelected(asset.uri, asset.base64 || '');
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const analyzeImage = async () => {
    if (!imageBase64) {
      Alert.alert('Error', 'No image selected for analysis');
      return;
    }

    setAnalyzing(true);
    try {
      // Use vision service to analyze the image
      const result = await visionService.analyzeMedicalImage(
        imageBase64,
        patientId,
        'Patient uploaded medical image for symptom tracking'
      );

      setAnalysisResult(result);
      onAnalysisComplete?.(result);

    } catch (error) {
      console.error('Vision analysis error:', error);
      Alert.alert(
        'Analysis Failed',
        `Unable to analyze the image: ${error instanceof Error ? error.message : 'Unknown error'}`,
        [{ text: 'OK' }]
      );
    } finally {
      setAnalyzing(false);
    }
  };

  const createImageNode = async () => {
    if (!analysisResult || !selectedImage) {
      Alert.alert('Error', 'No analysis result available');
      return;
    }

    try {
      const result = await graphService.createMedicalImageEntry(
        patientId,
        selectedImage,
        {
          caption: analysisResult.caption,
          ocr: analysisResult.ocr,
          labels: analysisResult.labels,
          medical_analysis: analysisResult.medical_analysis,
          safety_score: analysisResult.safety_score,
          model_used: analysisResult.model_used,
        },
        {
          upload_timestamp: new Date().toISOString(),
          source_app: 'patient_mobile',
          image_metadata: analysisResult.image_metadata,
        }
      );

      console.log('Image node created:', result);
      onImageNodeCreated?.(result.imageNode.node_id, true);

      Alert.alert(
        'Success! 🎉',
        `Medical image added to your timeline. ${result.relatedNodes.length > 0 ? `Created ${result.relatedNodes.length + 1} related entries.` : ''}`,
        [{ text: 'OK', onPress: handleClose }]
      );

    } catch (error) {
      console.error('Failed to create image node:', error);
      onImageNodeCreated?.('', false);

      Alert.alert(
        'Save Failed',
        `Unable to save the image to your timeline: ${error instanceof Error ? error.message : 'Unknown error'}`,
        [
          { text: 'Try Again', onPress: createImageNode },
          { text: 'Cancel', style: 'cancel' }
        ]
      );
    }
  };

  const resetSheet = () => {
    setSelectedImage(null);
    setImageBase64('');
    setAnalysisResult(null);
    setAnalyzing(false);
  };

  const handleClose = () => {
    resetSheet();
    onClose();
  };

  const handleConfirm = () => {
    if (analysisResult) {
      // Image has been analyzed, proceed with creating IMAGE node
      createImageNode();
    } else if (selectedImage) {
      // Analyze the image first
      analyzeImage();
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#666" />
          </TouchableOpacity>
          <Text style={styles.title}>Upload Medical Image</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Image Selection */}
          {!selectedImage ? (
            <View style={styles.selectionContainer}>
              <Text style={styles.subtitle}>Choose how to add your image</Text>
              
              <TouchableOpacity style={styles.optionButton} onPress={takePhoto}>
                <Ionicons name="camera" size={32} color="#2563eb" />
                <Text style={styles.optionText}>Take Photo</Text>
                <Text style={styles.optionSubtext}>Use camera to capture</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.optionButton} onPress={pickImageFromLibrary}>
                <Ionicons name="images" size={32} color="#2563eb" />
                <Text style={styles.optionText}>Choose from Library</Text>
                <Text style={styles.optionSubtext}>Select existing photo</Text>
              </TouchableOpacity>

              <View style={styles.infoBox}>
                <Ionicons name="information-circle" size={20} color="#2563eb" />
                <Text style={styles.infoText}>
                  Upload medical images like test results, medication labels, or symptoms for AI analysis
                </Text>
              </View>
            </View>
          ) : (
            <View style={styles.previewContainer}>
              {/* Image Preview */}
              <View style={styles.imageContainer}>
                <Image source={{ uri: selectedImage }} style={styles.previewImage} />
                <TouchableOpacity style={styles.changeImageButton} onPress={resetSheet}>
                  <Ionicons name="pencil" size={16} color="#fff" />
                </TouchableOpacity>
              </View>

              {/* Analysis Results */}
              {analyzing && (
                <View style={styles.analyzingContainer}>
                  <ActivityIndicator size="large" color="#2563eb" />
                  <Text style={styles.analyzingText}>Analyzing image with IBM Granite...</Text>
                </View>
              )}

              {analysisResult && (
                <View style={styles.resultsContainer}>
                  <Text style={styles.resultsTitle}>Analysis Results</Text>
                  
                  {analysisResult.caption && (
                    <View style={styles.resultItem}>
                      <Text style={styles.resultLabel}>Description:</Text>
                      <Text style={styles.resultText}>{analysisResult.caption}</Text>
                    </View>
                  )}

                  {analysisResult.ocr && (
                    <View style={styles.resultItem}>
                      <Text style={styles.resultLabel}>Text Found:</Text>
                      <Text style={styles.resultText}>{analysisResult.ocr}</Text>
                    </View>
                  )}

                  <View style={styles.resultItem}>
                    <Text style={styles.resultLabel}>Safety Score:</Text>
                    <Text style={[styles.resultText, { color: analysisResult.safety_score > 0.8 ? '#16a34a' : '#dc2626' }]}>
                      {(analysisResult.safety_score * 100).toFixed(1)}%
                    </Text>
                  </View>
                </View>
              )}
            </View>
          )}
        </ScrollView>

        {/* Action Buttons */}
        {selectedImage && (
          <View style={styles.actionContainer}>
            <TouchableOpacity 
              style={[styles.actionButton, styles.secondaryButton]} 
              onPress={resetSheet}
            >
              <Text style={styles.secondaryButtonText}>Choose Different</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.actionButton, styles.primaryButton, (analyzing || loading) && styles.disabledButton]} 
              onPress={handleConfirm}
              disabled={analyzing || loading}
            >
              {analyzing || loading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.primaryButtonText}>
                  {analysisResult ? 'Add to Timeline' : 'Analyze Image'}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  closeButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  selectionContainer: {
    paddingTop: 32,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 24,
    textAlign: 'center',
  },
  optionButton: {
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  optionText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginTop: 12,
  },
  optionSubtext: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: '#eff6ff',
    borderRadius: 8,
    padding: 16,
    marginTop: 24,
    alignItems: 'flex-start',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#1e40af',
    marginLeft: 12,
    lineHeight: 20,
  },
  previewContainer: {
    paddingTop: 24,
  },
  imageContainer: {
    position: 'relative',
    alignItems: 'center',
    marginBottom: 24,
  },
  previewImage: {
    width: screenWidth - 40,
    height: (screenWidth - 40) * 0.75,
    borderRadius: 12,
    backgroundColor: '#f3f4f6',
  },
  changeImageButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 20,
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  analyzingContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  analyzingText: {
    fontSize: 16,
    color: '#6b7280',
    marginTop: 16,
  },
  resultsContainer: {
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  resultItem: {
    marginBottom: 12,
  },
  resultLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 4,
  },
  resultText: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  actionContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 40,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryButton: {
    backgroundColor: '#2563eb',
  },
  secondaryButton: {
    backgroundColor: '#f3f4f6',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  disabledButton: {
    backgroundColor: '#9ca3af',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
});
