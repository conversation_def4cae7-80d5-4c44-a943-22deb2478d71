import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

interface SettingItemProps {
  icon: keyof typeof Ionicons.glyphMap;
  title: string;
  subtitle?: string;
  onPress?: () => void;
  rightElement?: React.ReactNode;
  showChevron?: boolean;
}

const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  rightElement,
  showChevron = true,
}) => (
  <TouchableOpacity
    style={styles.settingItem}
    onPress={onPress}
    disabled={!onPress}
  >
    <View style={styles.settingItemLeft}>
      <View style={styles.settingIcon}>
        <Ionicons name={icon} size={20} color="#3b82f6" />
      </View>
      <View style={styles.settingText}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
    </View>
    <View style={styles.settingItemRight}>
      {rightElement}
      {showChevron && onPress && (
        <Ionicons name="chevron-forward" size={16} color="#9ca3af" />
      )}
    </View>
  </TouchableOpacity>
);

const SettingsScreen: React.FC = () => {
  const [notifications, setNotifications] = useState(true);
  const [voiceRecording, setVoiceRecording] = useState(true);
  const [dataSync, setDataSync] = useState(true);
  const [biometrics, setBiometrics] = useState(false);

  const handleProfile = () => {
    Alert.alert('Profile', 'Profile management coming soon!');
  };

  const handlePrivacy = () => {
    Alert.alert('Privacy', 'Privacy settings coming soon!');
  };

  const handleDataExport = () => {
    Alert.alert(
      'Export Data',
      'This will export your health data in a secure format.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Export', onPress: () => console.log('Exporting data...') },
      ]
    );
  };

  const handleSupport = () => {
    Alert.alert('Support', 'Contact support coming soon!');
  };

  const handleAbout = () => {
    Alert.alert(
      'About SymptomOS',
      'Version 0.1.0\n\nSymptomOS is a voice-first symptom tracking system powered by IBM Granite AI models and OnionGraphContextEngine.\n\nBuilt with ❤️ for better healthcare outcomes.'
    );
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Sign Out', style: 'destructive', onPress: () => console.log('Signing out...') },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Profile</Text>
          <View style={styles.settingsGroup}>
            <SettingItem
              icon="person"
              title="Personal Information"
              subtitle="Manage your profile and medical info"
              onPress={handleProfile}
            />
            <SettingItem
              icon="shield-checkmark"
              title="Privacy & Security"
              subtitle="Control your data and privacy settings"
              onPress={handlePrivacy}
            />
          </View>
        </View>

        {/* App Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>App Settings</Text>
          <View style={styles.settingsGroup}>
            <SettingItem
              icon="notifications"
              title="Notifications"
              subtitle="Reminders and health alerts"
              rightElement={
                <Switch
                  value={notifications}
                  onValueChange={setNotifications}
                  trackColor={{ false: '#e2e8f0', true: '#3b82f6' }}
                  thumbColor={notifications ? '#ffffff' : '#f4f3f4'}
                />
              }
              showChevron={false}
            />
            <SettingItem
              icon="mic"
              title="Voice Recording"
              subtitle="Enable voice symptom capture"
              rightElement={
                <Switch
                  value={voiceRecording}
                  onValueChange={setVoiceRecording}
                  trackColor={{ false: '#e2e8f0', true: '#3b82f6' }}
                  thumbColor={voiceRecording ? '#ffffff' : '#f4f3f4'}
                />
              }
              showChevron={false}
            />
            <SettingItem
              icon="sync"
              title="Data Synchronization"
              subtitle="Sync with cloud and other devices"
              rightElement={
                <Switch
                  value={dataSync}
                  onValueChange={setDataSync}
                  trackColor={{ false: '#e2e8f0', true: '#3b82f6' }}
                  thumbColor={dataSync ? '#ffffff' : '#f4f3f4'}
                />
              }
              showChevron={false}
            />
            <SettingItem
              icon="finger-print"
              title="Biometric Authentication"
              subtitle="Use fingerprint or face ID"
              rightElement={
                <Switch
                  value={biometrics}
                  onValueChange={setBiometrics}
                  trackColor={{ false: '#e2e8f0', true: '#3b82f6' }}
                  thumbColor={biometrics ? '#ffffff' : '#f4f3f4'}
                />
              }
              showChevron={false}
            />
          </View>
        </View>

        {/* Data Management */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Management</Text>
          <View style={styles.settingsGroup}>
            <SettingItem
              icon="download"
              title="Export Data"
              subtitle="Download your health data"
              onPress={handleDataExport}
            />
            <SettingItem
              icon="cloud"
              title="Backup & Restore"
              subtitle="Manage your data backups"
              onPress={() => Alert.alert('Backup', 'Backup management coming soon!')}
            />
          </View>
        </View>

        {/* Support */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          <View style={styles.settingsGroup}>
            <SettingItem
              icon="help-circle"
              title="Help & FAQ"
              subtitle="Get help and find answers"
              onPress={handleSupport}
            />
            <SettingItem
              icon="mail"
              title="Contact Support"
              subtitle="Get in touch with our team"
              onPress={handleSupport}
            />
            <SettingItem
              icon="information-circle"
              title="About"
              subtitle="App version and information"
              onPress={handleAbout}
            />
          </View>
        </View>

        {/* Sign Out */}
        <View style={styles.section}>
          <View style={styles.settingsGroup}>
            <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
              <Ionicons name="log-out" size={20} color="#ef4444" />
              <Text style={styles.signOutText}>Sign Out</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            SymptomOS v0.1.0
          </Text>
          <Text style={styles.footerSubtext}>
            Powered by IBM Granite & OnionGraphContextEngine
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 16,
    marginHorizontal: 16,
  },
  settingsGroup: {
    backgroundColor: 'white',
    marginHorizontal: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 32,
    height: 32,
    borderRadius: 8,
    backgroundColor: '#eff6ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1e293b',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#64748b',
  },
  settingItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
  },
  signOutText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#ef4444',
    marginLeft: 8,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
  },
  footerText: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 4,
  },
  footerSubtext: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'center',
  },
});

export default SettingsScreen;
