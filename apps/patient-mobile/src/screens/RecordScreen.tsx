import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import { UploadSheet, VisionAnalysisResult } from '../components/UploadSheet';

const { width } = Dimensions.get('window');

interface RecordingState {
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  uri: string | null;
}

const RecordScreen: React.FC = () => {
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [recordingState, setRecordingState] = useState<RecordingState>({
    isRecording: false,
    isPaused: false,
    duration: 0,
    uri: null,
  });
  const [permissionResponse, requestPermission] = Audio.usePermissions();
  const [pulseAnim] = useState(new Animated.Value(1));
  const [uploadSheetVisible, setUploadSheetVisible] = useState(false);

  useEffect(() => {
    if (recordingState.isRecording) {
      // Start pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      pulseAnim.setValue(1);
    }
  }, [recordingState.isRecording]);

  const startRecording = async () => {
    try {
      if (permissionResponse?.status !== 'granted') {
        console.log('Requesting permission..');
        await requestPermission();
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      console.log('Starting recording..');
      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      setRecording(recording);
      setRecordingState(prev => ({ ...prev, isRecording: true }));
      console.log('Recording started');
    } catch (err) {
      console.error('Failed to start recording', err);
      Alert.alert('Error', 'Failed to start recording. Please try again.');
    }
  };

  const stopRecording = async () => {
    console.log('Stopping recording..');
    if (!recording) return;

    setRecordingState(prev => ({ ...prev, isRecording: false }));
    await recording.stopAndUnloadAsync();
    await Audio.setAudioModeAsync({
      allowsRecordingIOS: false,
    });
    const uri = recording.getURI();
    setRecordingState(prev => ({ ...prev, uri }));
    setRecording(null);
    console.log('Recording stopped and stored at', uri);

    // TODO: Send to parser service
    Alert.alert(
      'Recording Complete',
      'Your symptom has been recorded. Processing with IBM Granite...',
      [
        { text: 'OK', onPress: () => processRecording(uri) }
      ]
    );
  };

  const processRecording = async (uri: string | null) => {
    if (!uri) return;
    
    try {
      console.log('Processing recording:', uri);
      
      // Step 1: Convert audio to form data for Granite Gateway
      const formData = new FormData();
      formData.append('audio_file', {
        uri,
        type: 'audio/m4a',
        name: 'symptom_recording.m4a',
      } as any);
      formData.append('language_code', 'en-US');
      formData.append('enable_medical_terms', 'true');
      
      // Step 2: Call Granite Gateway ASR
      const asrResponse = await fetch('http://localhost:8080/v1/asr/', {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': 'Bearer sk-symptomos-patient-app',
          'Content-Type': 'multipart/form-data',
        },
      });
      
      if (!asrResponse.ok) {
        throw new Error(`ASR failed: ${asrResponse.status}`);
      }
      
      const asrData = await asrResponse.json();
      const transcription = asrData.transcription || '';
      const confidence = asrData.confidence || 0;
      
      if (!transcription.trim()) {
        throw new Error('No speech detected in recording');
      }
      
      console.log('Transcription:', transcription, 'Confidence:', confidence);
      
      // Step 3: Parse transcription for medical entities
      const parseResponse = await fetch('http://localhost:8000/v1/parse/text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer sk-symptomos-patient-app',
        },
        body: JSON.stringify({
          text: transcription,
          extract_symptoms: true,
          extract_medications: true,
          extract_temporal: true,
          extract_severity: true,
        }),
      });
      
      if (!parseResponse.ok) {
        throw new Error(`Parsing failed: ${parseResponse.status}`);
      }
      
      const parseData = await parseResponse.json();
      
      if (parseData.success && parseData.entity_count > 0) {
        Alert.alert(
          'Symptom Logged Successfully! 🎉',
          `Found ${parseData.entity_count} medical entities:\n\n"${transcription}"\n\nConfidence: ${Math.round(confidence * 100)}%`,
          [
            { text: 'View Timeline', onPress: () => console.log('Navigate to timeline') },
            { text: 'Record Another', onPress: resetRecording },
          ]
        );
      } else {
        Alert.alert(
          'No Symptoms Detected',
          `Transcription: "${transcription}"\n\nTry describing your symptoms more specifically.`,
          [
            { text: 'Try Again', onPress: resetRecording },
            { text: 'Manual Entry', onPress: () => console.log('Manual entry') },
          ]
        );
      }
      
    } catch (error) {
      console.error('Processing error:', error);
      Alert.alert(
        'Processing Failed',
        `Unable to process your recording: ${error instanceof Error ? error.message : 'Unknown error'}\n\nUsing mock response for demo.`,
        [
          { text: 'OK', onPress: () => {
            // Show mock success for demo
            Alert.alert(
              'Symptom Logged (Demo Mode) 🎉', 
              'Mock processing: Found symptoms - headache severity 7/10',
              [
                { text: 'View Timeline', onPress: () => console.log('Navigate to timeline') },
                { text: 'Record Another', onPress: resetRecording },
              ]
            );
          }},
        ]
      );
    }
  };

  const resetRecording = () => {
    setRecordingState({
      isRecording: false,
      isPaused: false,
      duration: 0,
      uri: null,
    });
  };

  const formatDuration = (duration: number) => {
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleUploadImage = () => {
    setUploadSheetVisible(true);
  };

  const handleImageSelected = (imageUri: string, imageBase64: string) => {
    console.log('Image selected:', { uri: imageUri, base64Length: imageBase64.length });
  };

  const handleAnalysisComplete = (result: VisionAnalysisResult) => {
    console.log('Vision analysis complete:', result);
  };

  const handleImageNodeCreated = (nodeId: string, success: boolean) => {
    if (success) {
      console.log('Image node created successfully:', nodeId);
    } else {
      console.log('Failed to create image node');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Record Symptom</Text>
          <Text style={styles.subtitle}>
            Describe your symptoms naturally. I'll help you track them.
          </Text>
        </View>

        {/* Recording Visualization */}
        <View style={styles.recordingArea}>
          <Animated.View
            style={[
              styles.recordButton,
              {
                transform: [{ scale: pulseAnim }],
                backgroundColor: recordingState.isRecording ? '#ef4444' : '#3b82f6',
              },
            ]}
          >
            <TouchableOpacity
              style={styles.recordButtonInner}
              onPress={recordingState.isRecording ? stopRecording : startRecording}
              disabled={!permissionResponse?.granted}
            >
              <Ionicons
                name={recordingState.isRecording ? 'stop' : 'mic'}
                size={48}
                color="white"
              />
            </TouchableOpacity>
          </Animated.View>

          {recordingState.isRecording && (
            <View style={styles.recordingIndicator}>
              <View style={styles.recordingDot} />
              <Text style={styles.recordingText}>Recording...</Text>
            </View>
          )}

          <Text style={styles.duration}>
            {formatDuration(recordingState.duration)}
          </Text>
        </View>

        {/* Alternative Input Methods */}
        <View style={styles.alternativeInputs}>
          <Text style={styles.alternativeTitle}>Or upload a medical image</Text>
          <TouchableOpacity style={styles.uploadButton} onPress={handleUploadImage}>
            <Ionicons name="camera" size={24} color="#2563eb" />
            <Text style={styles.uploadButtonText}>Upload Image</Text>
            <Text style={styles.uploadButtonSubtext}>Photos, test results, prescriptions</Text>
          </TouchableOpacity>
        </View>

        {/* Instructions */}
        <View style={styles.instructions}>
          <View style={styles.instructionItem}>
            <Ionicons name="mic-outline" size={20} color="#3b82f6" />
            <Text style={styles.instructionText}>
              Tap the microphone to start recording
            </Text>
          </View>
          <View style={styles.instructionItem}>
            <Ionicons name="chatbubble-outline" size={20} color="#3b82f6" />
            <Text style={styles.instructionText}>
              Speak naturally about your symptoms
            </Text>
          </View>
          <View style={styles.instructionItem}>
            <Ionicons name="analytics-outline" size={20} color="#3b82f6" />
            <Text style={styles.instructionText}>
              AI will extract medical information
            </Text>
          </View>
        </View>

        {/* Quick Examples */}
        <View style={styles.examples}>
          <Text style={styles.examplesTitle}>Example phrases:</Text>
          <Text style={styles.exampleText}>
            "I have a headache, intensity 7 out of 10"
          </Text>
          <Text style={styles.exampleText}>
            "Took 500mg ibuprofen at 2 PM"
          </Text>
          <Text style={styles.exampleText}>
            "Feeling nauseous after eating lunch"
          </Text>
        </View>
      </View>

      {/* Upload Sheet Modal */}
      <UploadSheet
        visible={uploadSheetVisible}
        onClose={() => setUploadSheetVisible(false)}
        onImageSelected={handleImageSelected}
        onAnalysisComplete={handleAnalysisComplete}
        onImageNodeCreated={handleImageNodeCreated}
        patientId="patient_001"
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  header: {
    alignItems: 'center',
    paddingTop: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
  },
  recordingArea: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  recordButton: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  recordButtonInner: {
    width: '100%',
    height: '100%',
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 24,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ef4444',
    marginRight: 8,
  },
  recordingText: {
    fontSize: 16,
    color: '#ef4444',
    fontWeight: '500',
  },
  duration: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1e293b',
    marginTop: 16,
    fontFamily: 'monospace',
  },
  instructions: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  instructionText: {
    fontSize: 14,
    color: '#64748b',
    marginLeft: 12,
    flex: 1,
  },
  examples: {
    backgroundColor: '#f1f5f9',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  examplesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#475569',
    marginBottom: 8,
  },
  exampleText: {
    fontSize: 13,
    color: '#64748b',
    fontStyle: 'italic',
    marginBottom: 4,
  },
  alternativeInputs: {
    marginVertical: 24,
  },
  alternativeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    textAlign: 'center',
    marginBottom: 16,
  },
  uploadButton: {
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#e5e7eb',
    borderStyle: 'dashed',
  },
  uploadButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2563eb',
    marginTop: 8,
  },
  uploadButtonSubtext: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
});

export default RecordScreen;
