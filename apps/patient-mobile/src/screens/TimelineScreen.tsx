import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { format, isToday, isYesterday, startOfDay } from 'date-fns';

interface TimelineEvent {
  id: string;
  type: 'symptom' | 'medication' | 'meal' | 'note';
  title: string;
  description: string;
  timestamp: Date;
  severity?: number;
  metadata?: Record<string, any>;
}

// Mock data for demonstration
const mockEvents: TimelineEvent[] = [
  {
    id: '1',
    type: 'symptom',
    title: 'Headache',
    description: 'Mild headache, intensity 4/10, started after lunch',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    severity: 4,
  },
  {
    id: '2',
    type: 'medication',
    title: 'Ibuprofen',
    description: '400mg taken for headache relief',
    timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
  },
  {
    id: '3',
    type: 'meal',
    title: 'Lunch',
    description: 'Chicken salad with tomatoes and onions',
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
  },
  {
    id: '4',
    type: 'symptom',
    title: 'Fatigue',
    description: 'Feeling tired, energy level 3/10',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
    severity: 7,
  },
];

const getEventIcon = (type: string) => {
  switch (type) {
    case 'symptom':
      return 'medical';
    case 'medication':
      return 'medical-outline';
    case 'meal':
      return 'restaurant';
    case 'note':
      return 'document-text';
    default:
      return 'ellipse';
  }
};

const getEventColor = (type: string) => {
  switch (type) {
    case 'symptom':
      return '#ef4444';
    case 'medication':
      return '#3b82f6';
    case 'meal':
      return '#10b981';
    case 'note':
      return '#8b5cf6';
    default:
      return '#6b7280';
  }
};

const getSeverityColor = (severity?: number) => {
  if (!severity) return '#6b7280';
  if (severity <= 3) return '#10b981';
  if (severity <= 6) return '#f59e0b';
  return '#ef4444';
};

const formatEventTime = (timestamp: Date) => {
  if (isToday(timestamp)) {
    return format(timestamp, 'h:mm a');
  } else if (isYesterday(timestamp)) {
    return `Yesterday, ${format(timestamp, 'h:mm a')}`;
  } else {
    return format(timestamp, 'MMM d, h:mm a');
  }
};

const TimelineEvent: React.FC<{ event: TimelineEvent; isLast: boolean }> = ({
  event,
  isLast,
}) => {
  return (
    <View style={styles.eventContainer}>
      <View style={styles.eventTimeline}>
        <View
          style={[
            styles.eventDot,
            { backgroundColor: getEventColor(event.type) },
          ]}
        >
          <Ionicons
            name={getEventIcon(event.type)}
            size={12}
            color="white"
          />
        </View>
        {!isLast && <View style={styles.eventLine} />}
      </View>
      
      <View style={styles.eventContent}>
        <View style={styles.eventHeader}>
          <Text style={styles.eventTitle}>{event.title}</Text>
          <Text style={styles.eventTime}>{formatEventTime(event.timestamp)}</Text>
        </View>
        
        <Text style={styles.eventDescription}>{event.description}</Text>
        
        {event.severity && (
          <View style={styles.severityContainer}>
            <Text style={styles.severityLabel}>Severity:</Text>
            <View
              style={[
                styles.severityBadge,
                { backgroundColor: getSeverityColor(event.severity) },
              ]}
            >
              <Text style={styles.severityText}>{event.severity}/10</Text>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

const TimelineScreen: React.FC = () => {
  const [filter, setFilter] = useState<'all' | 'symptom' | 'medication' | 'meal'>('all');
  
  const filteredEvents = mockEvents.filter(event => 
    filter === 'all' || event.type === filter
  );

  const groupedEvents = filteredEvents.reduce((groups, event) => {
    const dateKey = format(startOfDay(event.timestamp), 'yyyy-MM-dd');
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    groups[dateKey].push(event);
    return groups;
  }, {} as Record<string, TimelineEvent[]>);

  const renderFilterButton = (filterType: typeof filter, label: string, icon: keyof typeof Ionicons.glyphMap) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        filter === filterType && styles.filterButtonActive,
      ]}
      onPress={() => setFilter(filterType)}
    >
      <Ionicons
        name={icon}
        size={16}
        color={filter === filterType ? 'white' : '#64748b'}
      />
      <Text
        style={[
          styles.filterButtonText,
          filter === filterType && styles.filterButtonTextActive,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Filters */}
      <View style={styles.filtersContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {renderFilterButton('all', 'All', 'list')}
          {renderFilterButton('symptom', 'Symptoms', 'medical')}
          {renderFilterButton('medication', 'Medications', 'medical-outline')}
          {renderFilterButton('meal', 'Meals', 'restaurant')}
        </ScrollView>
      </View>

      {/* Timeline */}
      <ScrollView style={styles.timeline} showsVerticalScrollIndicator={false}>
        {Object.entries(groupedEvents).map(([dateKey, events]) => {
          const date = new Date(dateKey);
          const dateLabel = isToday(date)
            ? 'Today'
            : isYesterday(date)
            ? 'Yesterday'
            : format(date, 'EEEE, MMMM d');

          return (
            <View key={dateKey} style={styles.dayGroup}>
              <Text style={styles.dayLabel}>{dateLabel}</Text>
              {events.map((event, index) => (
                <TimelineEvent
                  key={event.id}
                  event={event}
                  isLast={index === events.length - 1}
                />
              ))}
            </View>
          );
        })}

        {filteredEvents.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="time-outline" size={48} color="#9ca3af" />
            <Text style={styles.emptyStateText}>No events found</Text>
            <Text style={styles.emptyStateSubtext}>
              {filter === 'all'
                ? 'Start recording symptoms to see your timeline'
                : `No ${filter}s recorded yet`}
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  filtersContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#f1f5f9',
  },
  filterButtonActive: {
    backgroundColor: '#3b82f6',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#64748b',
    marginLeft: 4,
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: 'white',
  },
  timeline: {
    flex: 1,
    paddingHorizontal: 16,
  },
  dayGroup: {
    marginBottom: 24,
  },
  dayLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 16,
    marginTop: 16,
  },
  eventContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  eventTimeline: {
    alignItems: 'center',
    marginRight: 16,
  },
  eventDot: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  eventLine: {
    width: 2,
    flex: 1,
    backgroundColor: '#e2e8f0',
    marginTop: 8,
  },
  eventContent: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
  },
  eventTime: {
    fontSize: 12,
    color: '#64748b',
  },
  eventDescription: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
  severityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  severityLabel: {
    fontSize: 12,
    color: '#64748b',
    marginRight: 8,
  },
  severityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  severityText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6b7280',
    marginTop: 12,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#9ca3af',
    marginTop: 4,
    textAlign: 'center',
  },
});

export default TimelineScreen;
