{"name": "patient-mobile", "version": "0.1.0", "description": "SymptomOS Patient Mobile App - Voice-first symptom tracking", "main": "index.ts", "scripts": {"start": "expo start", "dev": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo export", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "test": "jest", "lint": "eslint . --ext .ts,.tsx", "type-check": "tsc --noEmit", "clean": "rm -rf .expo dist"}, "dependencies": {"expo": "~53.0.13", "expo-status-bar": "~2.2.3", "expo-av": "~15.0.1", "expo-permissions": "~15.0.0", "expo-notifications": "~0.30.0", "expo-secure-store": "~14.0.0", "expo-sqlite": "~15.0.0", "expo-file-system": "~18.0.4", "react": "19.0.0", "react-native": "0.79.4", "react-native-reanimated": "~3.16.1", "react-native-gesture-handler": "~2.20.2", "react-native-safe-area-context": "4.14.0", "react-native-screens": "4.1.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "react-native-svg": "15.9.0", "react-native-vector-icons": "^10.0.3", "date-fns": "^3.2.0", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "ui-kit": "workspace:*"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "@types/react-native-vector-icons": "^6.4.18", "typescript": "~5.8.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint-config-expo": "^7.0.0", "jest": "^29.7.0", "@testing-library/react-native": "^12.4.2", "@testing-library/jest-native": "^5.4.3"}, "private": true}