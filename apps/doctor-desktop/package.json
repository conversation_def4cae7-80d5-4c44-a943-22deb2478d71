{"name": "doctor-desktop", "productName": "SymptomOS Doctor Dashboard", "version": "0.1.0", "description": "SymptomOS Doctor Desktop App - Medical dashboard for healthcare professionals", "main": ".vite/build/main.js", "scripts": {"start": "electron-forge start", "dev": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "build": "electron-forge package", "lint": "eslint --ext .ts,.tsx .", "test": "jest", "type-check": "tsc --noEmit", "clean": "rm -rf out .vite"}, "keywords": ["medical", "healthcare", "electron", "desktop", "symptomos"], "author": "SymptomOS Team", "license": "MIT", "dependencies": {"electron-squirrel-startup": "^1.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "date-fns": "^3.2.0", "recharts": "^2.10.0", "lucide-react": "^0.263.0", "clsx": "^2.1.0", "ui-kit": "workspace:*"}, "devDependencies": {"@electron-forge/cli": "^7.2.0", "@electron-forge/maker-deb": "^7.2.0", "@electron-forge/maker-rpm": "^7.2.0", "@electron-forge/maker-squirrel": "^7.2.0", "@electron-forge/maker-zip": "^7.2.0", "@electron-forge/plugin-auto-unpack-natives": "^7.2.0", "@electron-forge/plugin-vite": "^7.2.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "autoprefixer": "^10.4.17", "electron": "28.2.0", "eslint": "^8.0.1", "eslint-plugin-import": "^2.25.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "postcss": "^8.4.33", "tailwindcss": "^3.4.0", "ts-node": "^10.0.0", "typescript": "~4.5.4", "vite": "^5.0.12"}}