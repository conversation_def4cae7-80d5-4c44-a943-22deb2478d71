import React from 'react';
import { NavLink } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Users, 
  BarChart3, 
  Settings, 
  Menu,
  Activity,
  Brain,
  Stethoscope
} from 'lucide-react';
import clsx from 'clsx';

interface SidebarProps {
  collapsed: boolean;
  onToggle: () => void;
}

interface NavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  collapsed: boolean;
}

const NavItem: React.FC<NavItemProps> = ({ to, icon, label, collapsed }) => (
  <NavLink
    to={to}
    className={({ isActive }) =>
      clsx(
        'flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 rounded-lg mx-2',
        {
          'bg-blue-100 text-blue-600 border-r-2 border-blue-600': isActive,
          'justify-center': collapsed,
        }
      )
    }
  >
    <span className="flex-shrink-0">{icon}</span>
    {!collapsed && <span className="ml-3 font-medium">{label}</span>}
  </NavLink>
);

const Sidebar: React.FC<SidebarProps> = ({ collapsed, onToggle }) => {
  return (
    <div
      className={clsx(
        'fixed left-0 top-0 h-full bg-white border-r border-gray-200 transition-all duration-300 z-30',
        {
          'w-64': !collapsed,
          'w-16': collapsed,
        }
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {!collapsed && (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Stethoscope className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900">SymptomOS</h1>
              <p className="text-xs text-gray-500">Doctor Dashboard</p>
            </div>
          </div>
        )}
        <button
          onClick={onToggle}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
        >
          <Menu className="w-5 h-5 text-gray-600" />
        </button>
      </div>

      {/* Navigation */}
      <nav className="mt-6 space-y-2">
        <NavItem
          to="/dashboard"
          icon={<LayoutDashboard className="w-5 h-5" />}
          label="Dashboard"
          collapsed={collapsed}
        />
        <NavItem
          to="/patients"
          icon={<Users className="w-5 h-5" />}
          label="Patients"
          collapsed={collapsed}
        />
        <NavItem
          to="/analytics"
          icon={<BarChart3 className="w-5 h-5" />}
          label="Analytics"
          collapsed={collapsed}
        />
        <NavItem
          to="/settings"
          icon={<Settings className="w-5 h-5" />}
          label="Settings"
          collapsed={collapsed}
        />
      </nav>

      {/* AI Status */}
      {!collapsed && (
        <div className="absolute bottom-4 left-4 right-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Brain className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">
                IBM Granite AI
              </span>
            </div>
            <div className="flex items-center space-x-1 mt-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-green-600">Online & Ready</span>
            </div>
          </div>
        </div>
      )}

      {/* Collapsed AI Indicator */}
      {collapsed && (
        <div className="absolute bottom-4 left-2 right-2">
          <div className="bg-green-50 border border-green-200 rounded-lg p-2 flex justify-center">
            <Brain className="w-4 h-4 text-green-600" />
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
