import React, { useState, useEffect } from 'react';
import { RingNode, RingCluster, type RingNodeData, Card, useGraphSocket, type GraphUpdate } from 'ui-kit';
import { Calendar, Clock, User, FileText, X, AlertCircle, Loader2, Wifi, WifiOff } from 'lucide-react';

export interface TimelineRingsProps {
  patientId: string;
  mockGraphData?: any;
  /** OGCE Graph service URL. Defaults to http://localhost:8030 */
  graphServiceUrl?: string;
  /** Authentication token for API calls */
  authToken?: string;
  /** Whether to use mock data instead of API calls */
  useMockData?: boolean;
  /** Whether to enable real-time WebSocket updates */
  enableRealTimeUpdates?: boolean;
  /** WebSocket server URL for real-time updates */
  websocketUrl?: string;
}

interface NodeDetailDrawer {
  isOpen: boolean;
  nodeData: RingNodeData | null;
}

// API response types
interface GraphNode {
  id: string;
  node_type: string;
  properties: Record<string, any>;
  created_at?: string;
  updated_at?: string;
  curvature?: number;
}

interface GraphEdge {
  id: string;
  source_id: string;
  target_id: string;
  relationship_type: string;
  properties: Record<string, any>;
  weight?: number;
  created_at?: string;
}

interface PatientGraphResponse {
  patient_id: string;
  nodes: GraphNode[];
  edges: GraphEdge[];
  node_count: number;
  edge_count: number;
  processing_time_ms: number;
}

// Mock graph data structure
const mockTimelineData: RingNodeData[] = [
  {
    id: '1',
    type: 'symptom',
    label: 'Headache',
    timestamp: new Date('2023-12-15T14:30:00'),
    severity: 8,
    confidence: 0.94,
    metadata: {
      duration: '3 hours',
      location: 'temporal',
      onset: 'sudden',
      quality: 'throbbing',
      voiceRecordingId: 'rec_001',
      transcription: 'I have a severe headache, it feels like throbbing pain in my temples, probably 8 out of 10',
      extractedEntities: [
        { text: 'severe headache', type: 'symptom', confidence: 0.94 },
        { text: 'throbbing pain', type: 'symptom_quality', confidence: 0.87 },
        { text: 'temples', type: 'body_location', confidence: 0.91 },
        { text: '8 out of 10', type: 'severity_scale', confidence: 0.89 }
      ]
    },
  },
  {
    id: '2',
    type: 'medication',
    label: 'Ibuprofen',
    timestamp: new Date('2023-12-15T15:00:00'),
    confidence: 0.96,
    metadata: {
      dosage: '400mg',
      route: 'oral',
      frequency: 'as needed',
      indication: 'headache',
      voiceRecordingId: 'rec_002',
      transcription: 'I took 400 milligrams of ibuprofen for the headache pain',
      extractedEntities: [
        { text: 'ibuprofen', type: 'medication', confidence: 0.96 },
        { text: '400 milligrams', type: 'dosage', confidence: 0.93 },
        { text: 'headache pain', type: 'indication', confidence: 0.88 }
      ]
    },
  },
  {
    id: '3',
    type: 'event',
    label: 'Rest Period',
    timestamp: new Date('2023-12-15T16:00:00'),
    confidence: 0.82,
    metadata: {
      activity: 'lying down in dark room',
      duration: '1 hour',
      voiceRecordingId: 'rec_003',
      transcription: 'I went to lie down in a dark room for about an hour',
      extractedEntities: [
        { text: 'lie down', type: 'activity', confidence: 0.85 },
        { text: 'dark room', type: 'environment', confidence: 0.79 },
        { text: 'one hour', type: 'duration', confidence: 0.82 }
      ]
    },
  },
  {
    id: '4',
    type: 'symptom',
    label: 'Nausea',
    timestamp: new Date('2023-12-15T17:30:00'),
    severity: 4,
    confidence: 0.78,
    metadata: {
      onset: 'gradual',
      triggers: ['movement', 'light'],
      voiceRecordingId: 'rec_004',
      transcription: 'Started feeling nauseous when I moved around, especially with bright lights',
      extractedEntities: [
        { text: 'nauseous', type: 'symptom', confidence: 0.78 },
        { text: 'moved around', type: 'trigger', confidence: 0.71 },
        { text: 'bright lights', type: 'trigger', confidence: 0.74 }
      ]
    },
  },
  {
    id: '5',
    type: 'test',
    label: 'Blood Pressure',
    timestamp: new Date('2023-12-15T18:00:00'),
    confidence: 0.91,
    metadata: {
      systolic: 145,
      diastolic: 90,
      unit: 'mmHg',
      device: 'home monitor',
      notes: 'Elevated reading during headache episode'
    },
  },
  {
    id: '6',
    type: 'medication',
    label: 'Acetaminophen',
    timestamp: new Date('2023-12-15T19:30:00'),
    confidence: 0.89,
    metadata: {
      dosage: '500mg',
      route: 'oral',
      reason: 'continued headache',
      voiceRecordingId: 'rec_005',
      transcription: 'The ibuprofen didnt help much so I took 500mg of acetaminophen',
      extractedEntities: [
        { text: 'acetaminophen', type: 'medication', confidence: 0.89 },
        { text: '500mg', type: 'dosage', confidence: 0.92 },
        { text: 'continued headache', type: 'indication', confidence: 0.85 }
      ]
    },
  },
  {
    id: '7',
    type: 'diagnosis',
    label: 'Migraine',
    timestamp: new Date('2023-12-15T20:00:00'),
    confidence: 0.73,
    metadata: {
      evidenceSupporting: [
        'Severe unilateral headache',
        'Nausea',
        'Light sensitivity',
        'Duration > 2 hours'
      ],
      aiConfidence: 0.73,
      suggestedBy: 'AI Pattern Recognition',
      requiresVerification: true
    },
  }
];

// Transform API graph data to RingNodeData format
const transformGraphDataToRingNodes = (graphData: PatientGraphResponse): RingNodeData[] => {
  return graphData.nodes.map((node: GraphNode): RingNodeData => {
    const properties = node.properties || {};

    // Extract common properties
    const timestamp = properties.timestamp || properties.created_at || node.created_at;
    const parsedTimestamp = timestamp ? new Date(timestamp) : new Date();

    // Map node types to RingNodeData types
    const nodeTypeMap: Record<string, RingNodeData['type']> = {
      'symptom': 'symptom',
      'medication': 'medication',
      'event': 'event',
      'test': 'test',
      'diagnosis': 'diagnosis',
      'vital_sign': 'event',
      'meal': 'event',
      'note': 'event',
      'image': 'event'
    };

    const ringNodeType = nodeTypeMap[node.node_type] || 'event';

    // Extract label from properties
    const label = properties.label ||
                  properties.name ||
                  properties.description ||
                  properties.entity_type ||
                  node.node_type;

    // Extract severity and confidence
    const severity = properties.severity || properties.intensity || undefined;
    const confidence = properties.confidence || properties.ai_confidence || undefined;

    return {
      id: node.id,
      type: ringNodeType,
      label: String(label),
      timestamp: parsedTimestamp,
      severity: typeof severity === 'number' ? severity : undefined,
      confidence: typeof confidence === 'number' ? confidence : undefined,
      metadata: {
        ...properties,
        node_type: node.node_type,
        curvature: node.curvature,
        created_at: node.created_at,
        updated_at: node.updated_at
      }
    };
  }).sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime()); // Sort by timestamp
};

const TimelineRings: React.FC<TimelineRingsProps> = ({
  patientId,
  mockGraphData = mockTimelineData,
  graphServiceUrl = 'http://localhost:8030',
  authToken = 'sk-symptomos-doctor-desktop',
  useMockData = false,
  enableRealTimeUpdates = true,
  websocketUrl = 'ws://localhost:8030'
}) => {
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [drawer, setDrawer] = useState<NodeDetailDrawer>({ isOpen: false, nodeData: null });
  const [graphData, setGraphData] = useState<RingNodeData[]>(useMockData ? mockGraphData : []);
  const [loading, setLoading] = useState(!useMockData);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // WebSocket connection for real-time updates
  const {
    connectionStatus,
    lastUpdate,
    updates,
    connectionStats,
    error: wsError,
    reconnect,
    ping,
    clearUpdates
  } = useGraphSocket({
    patientId: enableRealTimeUpdates && !useMockData ? patientId : undefined,
    serverUrl: websocketUrl,
    authToken,
    autoReconnect: true,
    debug: true
  });

  // Handle real-time graph updates
  useEffect(() => {
    if (!enableRealTimeUpdates || useMockData || !lastUpdate) return;

    const update = lastUpdate;
    console.log('Received real-time update:', update);

    // Handle different types of updates
    switch (update.type) {
      case 'node_added':
        if (update.data && update.patient_id === patientId) {
          // Transform the new node data and add it to the graph
          const newNode = transformSingleNodeToRingNode(update.data);
          if (newNode) {
            setGraphData(prev => {
              const exists = prev.find(n => n.id === newNode.id);
              if (exists) return prev; // Avoid duplicates

              const updated = [...prev, newNode].sort((a, b) =>
                a.timestamp.getTime() - b.timestamp.getTime()
              );
              return updated;
            });
            setLastUpdated(new Date());
          }
        }
        break;

      case 'node_updated':
        if (update.data && update.patient_id === patientId) {
          const updatedNode = transformSingleNodeToRingNode(update.data);
          if (updatedNode) {
            setGraphData(prev => prev.map(node =>
              node.id === updatedNode.id ? updatedNode : node
            ));
            setLastUpdated(new Date());
          }
        }
        break;

      case 'edge_added':
        // For edges, we might want to refetch the full graph to get updated relationships
        // or implement more sophisticated edge handling
        console.log('Edge added, consider refetching graph for updated relationships');
        break;

      default:
        // Handle other update types (heartbeat, connection_established, etc.)
        break;
    }
  }, [lastUpdate, enableRealTimeUpdates, useMockData, patientId]);

  // Transform a single node from WebSocket update
  const transformSingleNodeToRingNode = (nodeData: any): RingNodeData | null => {
    try {
      const properties = nodeData.properties || {};
      const timestamp = properties.timestamp || nodeData.timestamp || new Date().toISOString();
      const parsedTimestamp = new Date(timestamp);

      const nodeTypeMap: Record<string, RingNodeData['type']> = {
        'symptom': 'symptom',
        'medication': 'medication',
        'event': 'event',
        'test': 'test',
        'diagnosis': 'diagnosis',
        'vital_sign': 'event',
        'meal': 'event',
        'note': 'event',
        'image': 'event'
      };

      const ringNodeType = nodeTypeMap[nodeData.node_type] || 'event';
      const label = properties.label || properties.name || properties.description || nodeData.node_type;

      return {
        id: nodeData.node_id,
        type: ringNodeType,
        label: String(label),
        timestamp: parsedTimestamp,
        severity: typeof properties.severity === 'number' ? properties.severity : undefined,
        confidence: typeof properties.confidence === 'number' ? properties.confidence : undefined,
        metadata: {
          ...properties,
          node_type: nodeData.node_type,
          realtime_update: true
        }
      };
    } catch (err) {
      console.error('Failed to transform WebSocket node data:', err);
      return null;
    }
  };

  // Fetch patient graph data from API
  const fetchPatientGraph = async () => {
    if (useMockData) {
      setGraphData(mockGraphData);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${graphServiceUrl}/v1/graph/patient/${patientId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch patient graph: ${response.status} ${response.statusText}`);
      }

      const apiData: PatientGraphResponse = await response.json();
      const transformedData = transformGraphDataToRingNodes(apiData);

      setGraphData(transformedData);
      setLastUpdated(new Date());

      console.log(`Loaded ${transformedData.length} nodes for patient ${patientId}`);

    } catch (err) {
      console.error('Failed to fetch patient graph:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      // Fallback to mock data on error
      setGraphData(mockGraphData);
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on mount and when patientId changes
  useEffect(() => {
    fetchPatientGraph();
  }, [patientId, useMockData, graphServiceUrl, authToken]);

  const handleNodeClick = (node: RingNodeData) => {
    setSelectedNodeId(node.id);
    setDrawer({ isOpen: true, nodeData: node });
  };

  const closeDrawer = () => {
    setDrawer({ isOpen: false, nodeData: null });
    setSelectedNodeId(null);
  };

  const formatTimestamp = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  const renderNodeDetails = (node: RingNodeData) => {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-xl font-semibold text-gray-900 capitalize">
              {node.type}: {node.label}
            </h3>
            <div className="flex items-center space-x-2 mt-1 text-sm text-gray-500">
              <Clock className="w-4 h-4" />
              <span>{formatTimestamp(node.timestamp)}</span>
            </div>
          </div>
          <button
            onClick={closeDrawer}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        {/* Confidence Badge */}
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700">AI Confidence:</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            node.confidence > 0.8 ? 'bg-green-100 text-green-800' :
            node.confidence > 0.6 ? 'bg-yellow-100 text-yellow-800' :
            'bg-red-100 text-red-800'
          }`}>
            {Math.round(node.confidence * 100)}%
          </span>
        </div>

        {/* Severity (for symptoms) */}
        {node.severity && (
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-700">Severity:</span>
            <div className="flex items-center space-x-1">
              {Array.from({ length: 10 }, (_, i) => (
                <div
                  key={i}
                  className={`w-3 h-3 rounded-full ${
                    i < node.severity! ? 'bg-red-500' : 'bg-gray-200'
                  }`}
                />
              ))}
              <span className="ml-2 text-sm font-medium text-gray-900">
                {node.severity}/10
              </span>
            </div>
          </div>
        )}

        {/* Voice Recording Section */}
        {node.metadata?.voiceRecordingId && node.metadata?.transcription && (
          <Card>
            <Card.Header title="Voice Recording Analysis" />
            <Card.Content>
              <div className="space-y-3">
                <div>
                  <span className="text-sm font-medium text-gray-700">Transcription:</span>
                  <p className="mt-1 text-sm text-gray-600 italic bg-gray-50 p-3 rounded">
                    "{node.metadata.transcription}"
                  </p>
                </div>
                
                {node.metadata.extractedEntities && (
                  <div>
                    <span className="text-sm font-medium text-gray-700">Extracted Entities:</span>
                    <div className="mt-2 space-y-2">
                      {node.metadata.extractedEntities.map((entity: any, index: number) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-blue-50 rounded">
                          <div>
                            <span className="text-sm font-medium text-blue-900">"{entity.text}"</span>
                            <span className="ml-2 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                              {entity.type.replace('_', ' ')}
                            </span>
                          </div>
                          <span className="text-xs text-blue-700">
                            {Math.round(entity.confidence * 100)}%
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </Card.Content>
          </Card>
        )}

        {/* Metadata */}
        <Card>
          <Card.Header title="Additional Details" />
          <Card.Content>
            <div className="space-y-2">
              {Object.entries(node.metadata || {}).map(([key, value]) => {
                // Skip already displayed fields
                if (['voiceRecordingId', 'transcription', 'extractedEntities'].includes(key)) {
                  return null;
                }
                
                return (
                  <div key={key} className="flex justify-between py-1">
                    <span className="text-sm font-medium text-gray-700 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}:
                    </span>
                    <span className="text-sm text-gray-600">
                      {Array.isArray(value) ? value.join(', ') : 
                       typeof value === 'object' ? JSON.stringify(value) : 
                       String(value)}
                    </span>
                  </div>
                );
              })}
            </div>
          </Card.Content>
        </Card>

        {/* AI Insights (for diagnoses) */}
        {node.type === 'diagnosis' && node.metadata?.evidenceSupporting && (
          <Card>
            <Card.Header title="Supporting Evidence" />
            <Card.Content>
              <ul className="space-y-1">
                {node.metadata.evidenceSupporting.map((evidence: string, index: number) => (
                  <li key={index} className="text-sm text-gray-600 flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                    {evidence}
                  </li>
                ))}
              </ul>
              {node.metadata.requiresVerification && (
                <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                  <p className="text-xs text-yellow-800">
                    ⚠️ This AI-generated diagnosis requires clinical verification
                  </p>
                </div>
              )}
            </Card.Content>
          </Card>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Timeline Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Patient Timeline</h2>
          <p className="text-sm text-gray-600">
            Interactive visualization of symptoms, medications, and events
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Calendar className="w-4 h-4" />
            <span>{lastUpdated ? lastUpdated.toLocaleDateString() : 'Loading...'}</span>
          </div>

          {/* WebSocket Status Indicator */}
          {enableRealTimeUpdates && !useMockData && (
            <div className="flex items-center space-x-2">
              <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                connectionStatus === 'connected'
                  ? 'bg-green-50 text-green-600'
                  : connectionStatus === 'connecting'
                  ? 'bg-yellow-50 text-yellow-600'
                  : 'bg-red-50 text-red-600'
              }`}>
                {connectionStatus === 'connected' ? (
                  <Wifi className="w-3 h-3" />
                ) : connectionStatus === 'connecting' ? (
                  <Loader2 className="w-3 h-3 animate-spin" />
                ) : (
                  <WifiOff className="w-3 h-3" />
                )}
                <span className="capitalize">{connectionStatus}</span>
              </div>
              {connectionStats && (
                <span className="text-xs text-gray-400">
                  {connectionStats.total_connections} clients
                </span>
              )}
            </div>
          )}

          {!useMockData && (
            <button
              onClick={fetchPatientGraph}
              disabled={loading}
              className="flex items-center space-x-1 px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 disabled:opacity-50"
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Calendar className="w-4 h-4" />
              )}
              <span>Refresh</span>
            </button>
          )}
        </div>
      </div>

      {/* Timeline Rings */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        {loading && (
          <div className="text-center py-12">
            <Loader2 className="w-8 h-8 mx-auto mb-4 text-blue-500 animate-spin" />
            <p className="text-gray-600">Loading patient timeline...</p>
          </div>
        )}

        {error && (
          <div className="text-center py-12">
            <AlertCircle className="w-8 h-8 mx-auto mb-4 text-red-500" />
            <p className="text-red-600 mb-2">Failed to load timeline data</p>
            <p className="text-sm text-gray-500 mb-4">{error}</p>
            <button
              onClick={fetchPatientGraph}
              className="px-4 py-2 bg-red-50 text-red-600 rounded-lg hover:bg-red-100"
            >
              Try Again
            </button>
          </div>
        )}

        {!loading && !error && (
          <>
            <RingCluster
              nodes={graphData}
              selectedId={selectedNodeId}
              onNodeClick={handleNodeClick}
              layout="horizontal"
              spacing="normal"
              className="min-h-24 justify-start flex-wrap"
            />

            {graphData.length === 0 && (
              <div className="text-center py-12 text-gray-500">
                <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No timeline data available for this patient</p>
                {!useMockData && (
                  <p className="text-sm mt-2">
                    This patient may not have any recorded medical events yet.
                  </p>
                )}
              </div>
            )}
          </>
        )}
      </div>

      {/* Legend */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-medium text-gray-700">Legend</h4>
          <span className="text-xs text-gray-500">
            {graphData.length} total events
            {lastUpdated && ` • Updated ${lastUpdated.toLocaleTimeString()}`}
            {enableRealTimeUpdates && !useMockData && (
              <span className="ml-2 px-2 py-0.5 bg-green-100 text-green-600 rounded-full">
                Live
              </span>
            )}
          </span>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-3 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-500 rounded-full" />
            <span>Symptoms ({graphData.filter(n => n.type === 'symptom').length})</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-blue-500 rounded-full" />
            <span>Medications ({graphData.filter(n => n.type === 'medication').length})</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-green-500 rounded-full" />
            <span>Events ({graphData.filter(n => n.type === 'event').length})</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-purple-500 rounded-full" />
            <span>Tests ({graphData.filter(n => n.type === 'test').length})</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-orange-500 rounded-full" />
            <span>Diagnoses ({graphData.filter(n => n.type === 'diagnosis').length})</span>
          </div>
        </div>
      </div>

      {/* Detail Drawer */}
      {drawer.isOpen && drawer.nodeData && (
        <div className="fixed inset-0 z-50 overflow-hidden">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={closeDrawer} />
          <div className="absolute right-0 top-0 h-full w-full max-w-2xl bg-white shadow-xl">
            <div className="h-full overflow-y-auto p-6">
              {renderNodeDetails(drawer.nodeData)}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TimelineRings;