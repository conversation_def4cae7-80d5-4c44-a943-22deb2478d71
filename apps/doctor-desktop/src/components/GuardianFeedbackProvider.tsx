import React, { createContext, useContext, useState, useEffect } from 'react';
import { 
  useGuardianFeedback, 
  GuardianToast, 
  GuardianAlert,
  type GuardianFeedback,
  type UseGuardianFeedbackReturn 
} from 'ui-kit';

interface GuardianFeedbackContextType extends UseGuardianFeedbackReturn {
  showToast: (feedback: GuardianFeedback) => void;
  showAlert: (feedback: GuardianFeedback) => void;
  toastFeedback: GuardianFeedback[];
  alertFeedback: GuardianFeedback[];
}

const GuardianFeedbackContext = createContext<GuardianFeedbackContextType | null>(null);

export const useGuardianFeedbackContext = () => {
  const context = useContext(GuardianFeedbackContext);
  if (!context) {
    throw new Error('useGuardianFeedbackContext must be used within GuardianFeedbackProvider');
  }
  return context;
};

interface GuardianFeedbackProviderProps {
  children: React.ReactNode;
  patientId?: string;
  guardianServiceUrl?: string;
  authToken?: string;
}

export const GuardianFeedbackProvider: React.FC<GuardianFeedbackProviderProps> = ({
  children,
  patientId,
  guardianServiceUrl = 'http://localhost:8014',
  authToken = 'sk-symptomos-doctor-desktop'
}) => {
  const guardianFeedback = useGuardianFeedback({
    guardianServiceUrl,
    authToken,
    patientId,
    autoFetch: true,
    fetchInterval: 30000
  });

  const [toastFeedback, setToastFeedback] = useState<GuardianFeedback[]>([]);
  const [alertFeedback, setAlertFeedback] = useState<GuardianFeedback[]>([]);

  const showToast = (feedback: GuardianFeedback) => {
    setToastFeedback(prev => [feedback, ...prev.slice(0, 4)]); // Keep max 5 toasts
  };

  const showAlert = (feedback: GuardianFeedback) => {
    setAlertFeedback(prev => [feedback, ...prev.slice(0, 9)]); // Keep max 10 alerts
  };

  const dismissToast = (feedbackId: string) => {
    setToastFeedback(prev => prev.filter(item => item.feedback_id !== feedbackId));
    guardianFeedback.dismissFeedback(feedbackId);
  };

  const dismissAlert = (feedbackId: string) => {
    setAlertFeedback(prev => prev.filter(item => item.feedback_id !== feedbackId));
    guardianFeedback.dismissFeedback(feedbackId);
  };

  const acknowledgeToast = (feedbackId: string) => {
    guardianFeedback.acknowledgeFeedback(feedbackId);
    // Keep toast visible for a moment after acknowledgment
    setTimeout(() => dismissToast(feedbackId), 1000);
  };

  const acknowledgeAlert = (feedbackId: string) => {
    guardianFeedback.acknowledgeFeedback(feedbackId);
    // Keep alert visible for a moment after acknowledgment
    setTimeout(() => dismissAlert(feedbackId), 1000);
  };

  // Auto-categorize new feedback items
  useEffect(() => {
    guardianFeedback.feedbackItems.forEach(feedback => {
      // Check if already shown
      const inToasts = toastFeedback.some(item => item.feedback_id === feedback.feedback_id);
      const inAlerts = alertFeedback.some(item => item.feedback_id === feedback.feedback_id);
      
      if (!inToasts && !inAlerts) {
        // Critical and high severity items go to both toast and alert
        if (feedback.severity === 'critical' || feedback.severity === 'high') {
          showToast(feedback);
          showAlert(feedback);
        } 
        // Medium severity goes to toast only
        else if (feedback.severity === 'medium') {
          showToast(feedback);
        }
        // Low severity goes to alert panel only (less intrusive)
        else {
          showAlert(feedback);
        }
      }
    });
  }, [guardianFeedback.feedbackItems]);

  const contextValue: GuardianFeedbackContextType = {
    ...guardianFeedback,
    showToast,
    showAlert,
    toastFeedback,
    alertFeedback
  };

  return (
    <GuardianFeedbackContext.Provider value={contextValue}>
      {children}
      
      {/* Toast Container */}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        {toastFeedback.map(feedback => (
          <GuardianToast
            key={feedback.feedback_id}
            feedback={feedback}
            onDismiss={dismissToast}
            onAcknowledge={acknowledgeToast}
            position="top-right"
          />
        ))}
      </div>
    </GuardianFeedbackContext.Provider>
  );
};

// Guardian Alert Panel Component
export const GuardianAlertPanel: React.FC<{
  className?: string;
  maxHeight?: string;
}> = ({ 
  className = '',
  maxHeight = 'max-h-96'
}) => {
  const { alertFeedback, dismissFeedback, acknowledgeFeedback, hasCriticalAlerts, hasUnacknowledgedAlerts } = useGuardianFeedbackContext();

  if (alertFeedback.length === 0) {
    return null;
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-semibold text-gray-900">Guardian Alerts</h3>
          {hasCriticalAlerts && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
              Critical
            </span>
          )}
          {hasUnacknowledgedAlerts && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
              Requires Attention
            </span>
          )}
        </div>
        <span className="text-sm text-gray-500">
          {alertFeedback.length} alert{alertFeedback.length !== 1 ? 's' : ''}
        </span>
      </div>
      
      <div className={`${maxHeight} overflow-y-auto`}>
        <div className="p-4 space-y-4">
          {alertFeedback.map(feedback => (
            <GuardianAlert
              key={feedback.feedback_id}
              feedback={feedback}
              onDismiss={dismissFeedback}
              onAcknowledge={acknowledgeFeedback}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

// Guardian Status Indicator Component
export const GuardianStatusIndicator: React.FC<{
  className?: string;
}> = ({ className = '' }) => {
  const { 
    hasCriticalAlerts, 
    hasUnacknowledgedAlerts, 
    feedbackItems, 
    loading, 
    error 
  } = useGuardianFeedbackContext();

  const getStatusConfig = () => {
    if (error) {
      return {
        color: 'text-red-600',
        bgColor: 'bg-red-100',
        status: 'Error',
        description: 'Guardian service unavailable'
      };
    }
    
    if (loading) {
      return {
        color: 'text-blue-600',
        bgColor: 'bg-blue-100',
        status: 'Checking',
        description: 'Checking for safety alerts'
      };
    }
    
    if (hasCriticalAlerts) {
      return {
        color: 'text-red-600',
        bgColor: 'bg-red-100',
        status: 'Critical',
        description: 'Critical safety alerts require attention'
      };
    }
    
    if (hasUnacknowledgedAlerts) {
      return {
        color: 'text-orange-600',
        bgColor: 'bg-orange-100',
        status: 'Alerts',
        description: 'Safety alerts require acknowledgment'
      };
    }
    
    if (feedbackItems.length > 0) {
      return {
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100',
        status: 'Monitoring',
        description: 'Guardian is monitoring content'
      };
    }
    
    return {
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      status: 'Safe',
      description: 'No safety concerns detected'
    };
  };

  const config = getStatusConfig();

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={`w-2 h-2 rounded-full ${config.bgColor}`} />
      <span className={`text-sm font-medium ${config.color}`}>
        Guardian: {config.status}
      </span>
      {feedbackItems.length > 0 && (
        <span className="text-xs text-gray-500">
          ({feedbackItems.length})
        </span>
      )}
    </div>
  );
};
