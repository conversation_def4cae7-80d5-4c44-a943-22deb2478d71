import React from 'react';
import {
  Users,
  Activity,
  AlertTriangle,
  TrendingUp,
  <PERSON>,
  Brain,
  Mic,
  FileText
} from 'lucide-react';
import { GuardianAlertPanel } from '../components/GuardianFeedbackProvider';

interface StatCardProps {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  icon: React.ReactNode;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, change, changeType, icon }) => (
  <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600">{title}</p>
        <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
        <p className={`text-sm mt-1 ${
          changeType === 'positive' ? 'text-green-600' : 
          changeType === 'negative' ? 'text-red-600' : 
          'text-gray-600'
        }`}>
          {change}
        </p>
      </div>
      <div className="p-3 bg-blue-50 rounded-lg">
        {icon}
      </div>
    </div>
  </div>
);

interface RecentActivityProps {
  patient: string;
  action: string;
  time: string;
  type: 'symptom' | 'medication' | 'alert';
}

const RecentActivity: React.FC<RecentActivityProps> = ({ patient, action, time, type }) => {
  const getIcon = () => {
    switch (type) {
      case 'symptom':
        return <Activity className="w-4 h-4 text-blue-600" />;
      case 'medication':
        return <FileText className="w-4 h-4 text-green-600" />;
      case 'alert':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <div className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg">
      <div className="flex-shrink-0">
        {getIcon()}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">{patient}</p>
        <p className="text-sm text-gray-500 truncate">{action}</p>
      </div>
      <div className="flex-shrink-0">
        <p className="text-xs text-gray-400">{time}</p>
      </div>
    </div>
  );
};

const Dashboard: React.FC = () => {
  const recentActivities = [
    {
      patient: "Sarah Johnson",
      action: "Reported severe headache (8/10)",
      time: "2 min ago",
      type: "symptom" as const
    },
    {
      patient: "Michael Chen",
      action: "Took prescribed ibuprofen 400mg",
      time: "15 min ago",
      type: "medication" as const
    },
    {
      patient: "Emma Davis",
      action: "High fever detected - requires attention",
      time: "1 hour ago",
      type: "alert" as const
    },
    {
      patient: "James Wilson",
      action: "Recorded voice symptom update",
      time: "2 hours ago",
      type: "symptom" as const
    }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">Welcome back, Dr. Smith</p>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Clock className="w-4 h-4" />
          <span>Last updated: {new Date().toLocaleTimeString()}</span>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Active Patients"
          value="127"
          change="+12% from last month"
          changeType="positive"
          icon={<Users className="w-6 h-6 text-blue-600" />}
        />
        <StatCard
          title="Symptoms Tracked"
          value="1,284"
          change="+8% from last week"
          changeType="positive"
          icon={<Activity className="w-6 h-6 text-blue-600" />}
        />
        <StatCard
          title="Active Alerts"
          value="7"
          change="3 new today"
          changeType="negative"
          icon={<AlertTriangle className="w-6 h-6 text-blue-600" />}
        />
        <StatCard
          title="AI Insights"
          value="23"
          change="5 new patterns detected"
          changeType="positive"
          icon={<Brain className="w-6 h-6 text-blue-600" />}
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
              <p className="text-sm text-gray-600 mt-1">Latest patient updates and system events</p>
            </div>
            <div className="p-6 space-y-1">
              {recentActivities.map((activity, index) => (
                <RecentActivity key={index} {...activity} />
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-6">
          {/* AI Status */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">AI Status</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Brain className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium">IBM Granite</span>
                </div>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                  Online
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Mic className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium">Voice Processing</span>
                </div>
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                  Active
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4 text-purple-600" />
                  <span className="text-sm font-medium">Pattern Analysis</span>
                </div>
                <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">
                  Running
                </span>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Summary</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Voice recordings</span>
                <span className="text-sm font-medium">34</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Symptoms logged</span>
                <span className="text-sm font-medium">89</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Medications tracked</span>
                <span className="text-sm font-medium">156</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Alerts generated</span>
                <span className="text-sm font-medium text-red-600">3</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Guardian Alerts Section */}
      <div className="mt-6">
        <GuardianAlertPanel maxHeight="max-h-64" />
      </div>
    </div>
  );
};

export default Dashboard;
