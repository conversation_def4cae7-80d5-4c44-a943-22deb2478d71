import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Search, 
  Filter, 
  Plus, 
  AlertTriangle, 
  Activity,
  Calendar,
  MoreVertical,
  Eye
} from 'lucide-react';

interface Patient {
  id: string;
  name: string;
  age: number;
  lastActivity: string;
  status: 'stable' | 'attention' | 'critical';
  symptomsCount: number;
  lastSymptom: string;
  avatar?: string;
}

const mockPatients: Patient[] = [
  {
    id: '1',
    name: '<PERSON>',
    age: 34,
    lastActivity: '2 minutes ago',
    status: 'attention',
    symptomsCount: 12,
    lastSymptom: 'Severe headache (8/10)'
  },
  {
    id: '2',
    name: '<PERSON>',
    age: 45,
    lastActivity: '15 minutes ago',
    status: 'stable',
    symptomsCount: 8,
    lastSymptom: 'Mild fatigue (3/10)'
  },
  {
    id: '3',
    name: '<PERSON>',
    age: 28,
    lastActivity: '1 hour ago',
    status: 'critical',
    symptomsCount: 23,
    lastSymptom: 'High fever (39.2°C)'
  },
  {
    id: '4',
    name: '<PERSON>',
    age: 52,
    lastActivity: '2 hours ago',
    status: 'stable',
    symptomsCount: 5,
    lastSymptom: 'Joint stiffness'
  },
  {
    id: '5',
    name: '<PERSON> <PERSON>',
    age: 41,
    lastActivity: '3 hours ago',
    status: 'attention',
    symptomsCount: 15,
    lastSymptom: 'Chest tightness'
  }
];

const getStatusColor = (status: Patient['status']) => {
  switch (status) {
    case 'stable':
      return 'bg-green-100 text-green-800';
    case 'attention':
      return 'bg-yellow-100 text-yellow-800';
    case 'critical':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: Patient['status']) => {
  switch (status) {
    case 'stable':
      return <Activity className="w-3 h-3" />;
    case 'attention':
      return <AlertTriangle className="w-3 h-3" />;
    case 'critical':
      return <AlertTriangle className="w-3 h-3" />;
    default:
      return <Activity className="w-3 h-3" />;
  }
};

const PatientCard: React.FC<{ patient: Patient; onClick: () => void }> = ({ patient, onClick }) => (
  <div className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
    <div className="p-6">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 font-semibold text-lg">
              {patient.name.split(' ').map(n => n[0]).join('')}
            </span>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{patient.name}</h3>
            <p className="text-sm text-gray-500">Age {patient.age}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(patient.status)}`}>
            {getStatusIcon(patient.status)}
            <span className="capitalize">{patient.status}</span>
          </span>
          <button className="p-1 hover:bg-gray-100 rounded">
            <MoreVertical className="w-4 h-4 text-gray-400" />
          </button>
        </div>
      </div>
      
      <div className="mt-4 space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Symptoms tracked:</span>
          <span className="font-medium">{patient.symptomsCount}</span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Last activity:</span>
          <span className="font-medium">{patient.lastActivity}</span>
        </div>
        <div className="mt-3">
          <p className="text-sm text-gray-600">Latest symptom:</p>
          <p className="text-sm font-medium text-gray-900 mt-1">{patient.lastSymptom}</p>
        </div>
      </div>
      
      <div className="mt-4 pt-4 border-t border-gray-100">
        <button 
          onClick={onClick}
          className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
        >
          <Eye className="w-4 h-4" />
          <span>View Details</span>
        </button>
      </div>
    </div>
  </div>
);

const Patients: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | Patient['status']>('all');

  const filteredPatients = mockPatients.filter(patient => {
    const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || patient.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handlePatientClick = (patientId: string) => {
    navigate(`/patients/${patientId}`);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Patients</h1>
          <p className="text-gray-600 mt-1">Manage and monitor your patients</p>
        </div>
        <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
          <Plus className="w-4 h-4" />
          <span>Add Patient</span>
        </button>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search patients..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-gray-400" />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as typeof statusFilter)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="stable">Stable</option>
            <option value="attention">Needs Attention</option>
            <option value="critical">Critical</option>
          </select>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Patients</p>
              <p className="text-2xl font-bold text-gray-900">{mockPatients.length}</p>
            </div>
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <Activity className="w-4 h-4 text-blue-600" />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Stable</p>
              <p className="text-2xl font-bold text-green-600">
                {mockPatients.filter(p => p.status === 'stable').length}
              </p>
            </div>
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <Activity className="w-4 h-4 text-green-600" />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Need Attention</p>
              <p className="text-2xl font-bold text-yellow-600">
                {mockPatients.filter(p => p.status === 'attention').length}
              </p>
            </div>
            <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-4 h-4 text-yellow-600" />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Critical</p>
              <p className="text-2xl font-bold text-red-600">
                {mockPatients.filter(p => p.status === 'critical').length}
              </p>
            </div>
            <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-4 h-4 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Patients Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredPatients.map((patient) => (
          <PatientCard
            key={patient.id}
            patient={patient}
            onClick={() => handlePatientClick(patient.id)}
          />
        ))}
      </div>

      {filteredPatients.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Search className="w-6 h-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No patients found</h3>
          <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
        </div>
      )}
    </div>
  );
};

export default Patients;
