import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Calendar, 
  Activity, 
  Pill, 
  FileText,
  AlertTriangle,
  TrendingUp,
  Mic,
  Download
} from 'lucide-react';
import TimelineRings from '../components/TimelineRings';

const PatientDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'timeline' | 'symptoms' | 'medications' | 'insights'>('timeline');

  // Mock patient data
  const patient = {
    id: id || '1',
    name: '<PERSON>',
    age: 34,
    email: '<EMAIL>',
    phone: '+****************',
    status: 'attention' as const,
    lastActivity: '2 minutes ago',
    joinDate: '2024-01-15'
  };

  const timelineEvents = [
    {
      id: '1',
      type: 'symptom',
      title: 'Severe headache reported',
      description: 'Patient reported severe headache with intensity 8/10. Voice recording analyzed.',
      timestamp: '2 minutes ago',
      severity: 8
    },
    {
      id: '2',
      type: 'medication',
      title: 'Ibuprofen taken',
      description: '400mg ibuprofen taken for headache relief',
      timestamp: '1 hour ago'
    },
    {
      id: '3',
      type: 'insight',
      title: 'Pattern detected',
      description: 'AI detected correlation between stress levels and headache frequency',
      timestamp: '3 hours ago'
    }
  ];

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'symptom':
        return <Activity className="w-4 h-4 text-red-600" />;
      case 'medication':
        return <Pill className="w-4 h-4 text-blue-600" />;
      case 'insight':
        return <TrendingUp className="w-4 h-4 text-purple-600" />;
      default:
        return <FileText className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'stable':
        return 'bg-green-100 text-green-800';
      case 'attention':
        return 'bg-yellow-100 text-yellow-800';
      case 'critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/patients')}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{patient.name}</h1>
            <p className="text-gray-600">Patient ID: {patient.id}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(patient.status)}`}>
            <AlertTriangle className="w-3 h-3 mr-1" />
            {patient.status.charAt(0).toUpperCase() + patient.status.slice(1)}
          </span>
          <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
            <Download className="w-4 h-4" />
            <span>Export Data</span>
          </button>
        </div>
      </div>

      {/* Patient Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 font-semibold text-lg">
                {patient.name.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
            <div>
              <p className="font-semibold text-gray-900">{patient.name}</p>
              <p className="text-sm text-gray-500">Age {patient.age}</p>
            </div>
          </div>
          <div className="mt-4 space-y-2 text-sm">
            <p className="text-gray-600">{patient.email}</p>
            <p className="text-gray-600">{patient.phone}</p>
            <p className="text-gray-600">Joined: {patient.joinDate}</p>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Symptoms</p>
              <p className="text-2xl font-bold text-gray-900">47</p>
            </div>
            <Activity className="w-8 h-8 text-red-600" />
          </div>
          <p className="text-sm text-gray-500 mt-2">Last: {patient.lastActivity}</p>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Medications</p>
              <p className="text-2xl font-bold text-gray-900">12</p>
            </div>
            <Pill className="w-8 h-8 text-blue-600" />
          </div>
          <p className="text-sm text-gray-500 mt-2">3 active prescriptions</p>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">AI Insights</p>
              <p className="text-2xl font-bold text-gray-900">8</p>
            </div>
            <TrendingUp className="w-8 h-8 text-purple-600" />
          </div>
          <p className="text-sm text-gray-500 mt-2">2 new patterns found</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'timeline', label: 'Timeline', icon: Calendar },
            { id: 'symptoms', label: 'Symptoms', icon: Activity },
            { id: 'medications', label: 'Medications', icon: Pill },
            { id: 'insights', label: 'AI Insights', icon: TrendingUp }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg border border-gray-200">
        {activeTab === 'timeline' && (
          <div className="p-6">
            <TimelineRings patientId={patient.id} />
          </div>
        )}

        {activeTab === 'symptoms' && (
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Symptom History</h3>
            <p className="text-gray-600">Detailed symptom tracking and analysis coming soon...</p>
          </div>
        )}

        {activeTab === 'medications' && (
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Medication History</h3>
            <p className="text-gray-600">Medication tracking and adherence monitoring coming soon...</p>
          </div>
        )}

        {activeTab === 'insights' && (
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">AI-Generated Insights</h3>
            <div className="space-y-4">
              <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <TrendingUp className="w-4 h-4 text-purple-600" />
                  <span className="font-medium text-purple-900">Pattern Detection</span>
                </div>
                <p className="text-sm text-purple-800">
                  Headache frequency increases by 40% during high-stress periods. Consider stress management techniques.
                </p>
              </div>
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Mic className="w-4 h-4 text-blue-600" />
                  <span className="font-medium text-blue-900">Voice Analysis</span>
                </div>
                <p className="text-sm text-blue-800">
                  Voice patterns suggest fatigue levels correlate with symptom severity reporting.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PatientDetail;
