hoistPattern:
  - '*'
hoistedDependencies:
  /@alloc/quick-lru/5.2.0:
    '@alloc/quick-lru': private
  /@ampproject/remapping/2.3.0:
    '@ampproject/remapping': private
  /@babel/code-frame/7.27.1:
    '@babel/code-frame': private
  /@babel/compat-data/7.27.7:
    '@babel/compat-data': private
  /@babel/core/7.27.7:
    '@babel/core': private
  /@babel/generator/7.27.5:
    '@babel/generator': private
  /@babel/helper-compilation-targets/7.27.2:
    '@babel/helper-compilation-targets': private
  /@babel/helper-module-imports/7.27.1:
    '@babel/helper-module-imports': private
  /@babel/helper-module-transforms/7.27.3(@babel/core@7.27.7):
    '@babel/helper-module-transforms': private
  /@babel/helper-plugin-utils/7.27.1:
    '@babel/helper-plugin-utils': private
  /@babel/helper-string-parser/7.27.1:
    '@babel/helper-string-parser': private
  /@babel/helper-validator-identifier/7.27.1:
    '@babel/helper-validator-identifier': private
  /@babel/helper-validator-option/7.27.1:
    '@babel/helper-validator-option': private
  /@babel/helpers/7.27.6:
    '@babel/helpers': private
  /@babel/parser/7.27.7:
    '@babel/parser': private
  /@babel/plugin-transform-react-jsx-self/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-react-jsx-self': private
  /@babel/plugin-transform-react-jsx-source/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-react-jsx-source': private
  /@babel/template/7.27.2:
    '@babel/template': private
  /@babel/traverse/7.27.7:
    '@babel/traverse': private
  /@babel/types/7.27.7:
    '@babel/types': private
  /@esbuild/aix-ppc64/0.21.5:
    '@esbuild/aix-ppc64': private
  /@esbuild/android-arm/0.21.5:
    '@esbuild/android-arm': private
  /@esbuild/android-arm64/0.21.5:
    '@esbuild/android-arm64': private
  /@esbuild/android-x64/0.21.5:
    '@esbuild/android-x64': private
  /@esbuild/darwin-arm64/0.21.5:
    '@esbuild/darwin-arm64': private
  /@esbuild/darwin-x64/0.21.5:
    '@esbuild/darwin-x64': private
  /@esbuild/freebsd-arm64/0.21.5:
    '@esbuild/freebsd-arm64': private
  /@esbuild/freebsd-x64/0.21.5:
    '@esbuild/freebsd-x64': private
  /@esbuild/linux-arm/0.21.5:
    '@esbuild/linux-arm': private
  /@esbuild/linux-arm64/0.21.5:
    '@esbuild/linux-arm64': private
  /@esbuild/linux-ia32/0.21.5:
    '@esbuild/linux-ia32': private
  /@esbuild/linux-loong64/0.21.5:
    '@esbuild/linux-loong64': private
  /@esbuild/linux-mips64el/0.21.5:
    '@esbuild/linux-mips64el': private
  /@esbuild/linux-ppc64/0.21.5:
    '@esbuild/linux-ppc64': private
  /@esbuild/linux-riscv64/0.21.5:
    '@esbuild/linux-riscv64': private
  /@esbuild/linux-s390x/0.21.5:
    '@esbuild/linux-s390x': private
  /@esbuild/linux-x64/0.21.5:
    '@esbuild/linux-x64': private
  /@esbuild/netbsd-x64/0.21.5:
    '@esbuild/netbsd-x64': private
  /@esbuild/openbsd-x64/0.21.5:
    '@esbuild/openbsd-x64': private
  /@esbuild/sunos-x64/0.21.5:
    '@esbuild/sunos-x64': private
  /@esbuild/win32-arm64/0.21.5:
    '@esbuild/win32-arm64': private
  /@esbuild/win32-ia32/0.21.5:
    '@esbuild/win32-ia32': private
  /@esbuild/win32-x64/0.21.5:
    '@esbuild/win32-x64': private
  /@isaacs/cliui/8.0.2:
    '@isaacs/cliui': private
  /@jridgewell/gen-mapping/0.3.8:
    '@jridgewell/gen-mapping': private
  /@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  /@jridgewell/set-array/1.2.1:
    '@jridgewell/set-array': private
  /@jridgewell/sourcemap-codec/1.5.0:
    '@jridgewell/sourcemap-codec': private
  /@jridgewell/trace-mapping/0.3.25:
    '@jridgewell/trace-mapping': private
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@pkgjs/parseargs/0.11.0:
    '@pkgjs/parseargs': private
  /@remix-run/router/1.23.0:
    '@remix-run/router': private
  /@rolldown/pluginutils/1.0.0-beta.19:
    '@rolldown/pluginutils': private
  /@rollup/rollup-android-arm-eabi/4.44.1:
    '@rollup/rollup-android-arm-eabi': private
  /@rollup/rollup-android-arm64/4.44.1:
    '@rollup/rollup-android-arm64': private
  /@rollup/rollup-darwin-arm64/4.44.1:
    '@rollup/rollup-darwin-arm64': private
  /@rollup/rollup-darwin-x64/4.44.1:
    '@rollup/rollup-darwin-x64': private
  /@rollup/rollup-freebsd-arm64/4.44.1:
    '@rollup/rollup-freebsd-arm64': private
  /@rollup/rollup-freebsd-x64/4.44.1:
    '@rollup/rollup-freebsd-x64': private
  /@rollup/rollup-linux-arm-gnueabihf/4.44.1:
    '@rollup/rollup-linux-arm-gnueabihf': private
  /@rollup/rollup-linux-arm-musleabihf/4.44.1:
    '@rollup/rollup-linux-arm-musleabihf': private
  /@rollup/rollup-linux-arm64-gnu/4.44.1:
    '@rollup/rollup-linux-arm64-gnu': private
  /@rollup/rollup-linux-arm64-musl/4.44.1:
    '@rollup/rollup-linux-arm64-musl': private
  /@rollup/rollup-linux-loongarch64-gnu/4.44.1:
    '@rollup/rollup-linux-loongarch64-gnu': private
  /@rollup/rollup-linux-powerpc64le-gnu/4.44.1:
    '@rollup/rollup-linux-powerpc64le-gnu': private
  /@rollup/rollup-linux-riscv64-gnu/4.44.1:
    '@rollup/rollup-linux-riscv64-gnu': private
  /@rollup/rollup-linux-riscv64-musl/4.44.1:
    '@rollup/rollup-linux-riscv64-musl': private
  /@rollup/rollup-linux-s390x-gnu/4.44.1:
    '@rollup/rollup-linux-s390x-gnu': private
  /@rollup/rollup-linux-x64-gnu/4.44.1:
    '@rollup/rollup-linux-x64-gnu': private
  /@rollup/rollup-linux-x64-musl/4.44.1:
    '@rollup/rollup-linux-x64-musl': private
  /@rollup/rollup-win32-arm64-msvc/4.44.1:
    '@rollup/rollup-win32-arm64-msvc': private
  /@rollup/rollup-win32-ia32-msvc/4.44.1:
    '@rollup/rollup-win32-ia32-msvc': private
  /@rollup/rollup-win32-x64-msvc/4.44.1:
    '@rollup/rollup-win32-x64-msvc': private
  /@types/babel__core/7.20.5:
    '@types/babel__core': private
  /@types/babel__generator/7.27.0:
    '@types/babel__generator': private
  /@types/babel__template/7.4.4:
    '@types/babel__template': private
  /@types/babel__traverse/7.20.7:
    '@types/babel__traverse': private
  /@types/estree/1.0.8:
    '@types/estree': private
  /@types/prop-types/15.7.15:
    '@types/prop-types': private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/4.3.0:
    ansi-styles: private
  /any-promise/1.3.0:
    any-promise: private
  /anymatch/3.1.3:
    anymatch: private
  /arg/5.0.2:
    arg: private
  /balanced-match/1.0.2:
    balanced-match: private
  /binary-extensions/2.3.0:
    binary-extensions: private
  /brace-expansion/2.0.2:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /browserslist/4.25.1:
    browserslist: private
  /camelcase-css/2.0.1:
    camelcase-css: private
  /caniuse-lite/1.0.30001726:
    caniuse-lite: private
  /chokidar/3.6.0:
    chokidar: private
  /color-convert/2.0.1:
    color-convert: private
  /color-name/1.1.4:
    color-name: private
  /commander/4.1.1:
    commander: private
  /convert-source-map/2.0.0:
    convert-source-map: private
  /cross-spawn/7.0.6:
    cross-spawn: private
  /cssesc/3.0.0:
    cssesc: private
  /csstype/3.1.3:
    csstype: private
  /debug/4.4.1:
    debug: private
  /didyoumean/1.2.2:
    didyoumean: private
  /dlv/1.1.3:
    dlv: private
  /eastasianwidth/0.2.0:
    eastasianwidth: private
  /electron-to-chromium/1.5.177:
    electron-to-chromium: private
  /emoji-regex/8.0.0:
    emoji-regex: private
  /esbuild/0.21.5:
    esbuild: private
  /escalade/3.2.0:
    escalade: private
  /fast-glob/3.3.3:
    fast-glob: private
  /fastq/1.19.1:
    fastq: private
  /fill-range/7.1.1:
    fill-range: private
  /foreground-child/3.3.1:
    foreground-child: private
  /fraction.js/4.3.7:
    fraction.js: private
  /fsevents/2.3.3:
    fsevents: private
  /function-bind/1.1.2:
    function-bind: private
  /gensync/1.0.0-beta.2:
    gensync: private
  /glob-parent/6.0.2:
    glob-parent: private
  /glob/10.4.5:
    glob: private
  /globals/11.12.0:
    globals: private
  /hasown/2.0.2:
    hasown: private
  /is-binary-path/2.1.0:
    is-binary-path: private
  /is-core-module/2.16.1:
    is-core-module: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-fullwidth-code-point/3.0.0:
    is-fullwidth-code-point: private
  /is-glob/4.0.3:
    is-glob: private
  /is-number/7.0.0:
    is-number: private
  /isexe/2.0.0:
    isexe: private
  /jackspeak/3.4.3:
    jackspeak: private
  /jiti/1.21.7:
    jiti: private
  /js-tokens/4.0.0:
    js-tokens: private
  /jsesc/3.1.0:
    jsesc: private
  /json5/2.2.3:
    json5: private
  /lilconfig/3.1.3:
    lilconfig: private
  /lines-and-columns/1.2.4:
    lines-and-columns: private
  /loose-envify/1.4.0:
    loose-envify: private
  /lru-cache/5.1.1:
    lru-cache: private
  /merge2/1.4.1:
    merge2: private
  /micromatch/4.0.8:
    micromatch: private
  /minimatch/9.0.5:
    minimatch: private
  /minipass/7.1.2:
    minipass: private
  /ms/2.1.3:
    ms: private
  /mz/2.7.0:
    mz: private
  /nanoid/3.3.11:
    nanoid: private
  /node-releases/2.0.19:
    node-releases: private
  /normalize-path/3.0.0:
    normalize-path: private
  /normalize-range/0.1.2:
    normalize-range: private
  /object-assign/4.1.1:
    object-assign: private
  /object-hash/3.0.0:
    object-hash: private
  /package-json-from-dist/1.0.1:
    package-json-from-dist: private
  /path-key/3.1.1:
    path-key: private
  /path-parse/1.0.7:
    path-parse: private
  /path-scurry/1.11.1:
    path-scurry: private
  /picocolors/1.1.1:
    picocolors: private
  /picomatch/2.3.1:
    picomatch: private
  /pify/2.3.0:
    pify: private
  /pirates/4.0.7:
    pirates: private
  /postcss-import/15.1.0(postcss@8.5.6):
    postcss-import: private
  /postcss-js/4.0.1(postcss@8.5.6):
    postcss-js: private
  /postcss-load-config/4.0.2(postcss@8.5.6):
    postcss-load-config: private
  /postcss-nested/6.2.0(postcss@8.5.6):
    postcss-nested: private
  /postcss-selector-parser/6.1.2:
    postcss-selector-parser: private
  /postcss-value-parser/4.2.0:
    postcss-value-parser: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /react-refresh/0.17.0:
    react-refresh: private
  /react-router/6.30.1(react@18.3.1):
    react-router: private
  /read-cache/1.0.0:
    read-cache: private
  /readdirp/3.6.0:
    readdirp: private
  /resolve/1.22.10:
    resolve: private
  /reusify/1.1.0:
    reusify: private
  /rollup/4.44.1:
    rollup: private
  /run-parallel/1.2.0:
    run-parallel: private
  /scheduler/0.23.2:
    scheduler: private
  /semver/6.3.1:
    semver: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /signal-exit/4.1.0:
    signal-exit: private
  /source-map-js/1.2.1:
    source-map-js: private
  /string-width/4.2.3:
    string-width-cjs: private
  /string-width/5.1.2:
    string-width: private
  /strip-ansi/6.0.1:
    strip-ansi-cjs: private
  /strip-ansi/7.1.0:
    strip-ansi: private
  /sucrase/3.35.0:
    sucrase: private
  /supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  /thenify-all/1.6.0:
    thenify-all: private
  /thenify/3.3.1:
    thenify: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /ts-interface-checker/0.1.13:
    ts-interface-checker: private
  /update-browserslist-db/1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  /util-deprecate/1.0.2:
    util-deprecate: private
  /which/2.0.2:
    which: private
  /wrap-ansi/7.0.0:
    wrap-ansi-cjs: private
  /wrap-ansi/8.1.0:
    wrap-ansi: private
  /yallist/3.1.1:
    yallist: private
  /yaml/2.8.0:
    yaml: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.15.0
pendingBuilds: []
prunedAt: Sat, 28 Jun 2025 14:34:07 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - /@esbuild/aix-ppc64/0.21.5
  - /@esbuild/android-arm/0.21.5
  - /@esbuild/android-arm64/0.21.5
  - /@esbuild/android-x64/0.21.5
  - /@esbuild/darwin-x64/0.21.5
  - /@esbuild/freebsd-arm64/0.21.5
  - /@esbuild/freebsd-x64/0.21.5
  - /@esbuild/linux-arm/0.21.5
  - /@esbuild/linux-arm64/0.21.5
  - /@esbuild/linux-ia32/0.21.5
  - /@esbuild/linux-loong64/0.21.5
  - /@esbuild/linux-mips64el/0.21.5
  - /@esbuild/linux-ppc64/0.21.5
  - /@esbuild/linux-riscv64/0.21.5
  - /@esbuild/linux-s390x/0.21.5
  - /@esbuild/linux-x64/0.21.5
  - /@esbuild/netbsd-x64/0.21.5
  - /@esbuild/openbsd-x64/0.21.5
  - /@esbuild/sunos-x64/0.21.5
  - /@esbuild/win32-arm64/0.21.5
  - /@esbuild/win32-ia32/0.21.5
  - /@esbuild/win32-x64/0.21.5
  - /@rollup/rollup-android-arm-eabi/4.44.1
  - /@rollup/rollup-android-arm64/4.44.1
  - /@rollup/rollup-darwin-x64/4.44.1
  - /@rollup/rollup-freebsd-arm64/4.44.1
  - /@rollup/rollup-freebsd-x64/4.44.1
  - /@rollup/rollup-linux-arm-gnueabihf/4.44.1
  - /@rollup/rollup-linux-arm-musleabihf/4.44.1
  - /@rollup/rollup-linux-arm64-gnu/4.44.1
  - /@rollup/rollup-linux-arm64-musl/4.44.1
  - /@rollup/rollup-linux-loongarch64-gnu/4.44.1
  - /@rollup/rollup-linux-powerpc64le-gnu/4.44.1
  - /@rollup/rollup-linux-riscv64-gnu/4.44.1
  - /@rollup/rollup-linux-riscv64-musl/4.44.1
  - /@rollup/rollup-linux-s390x-gnu/4.44.1
  - /@rollup/rollup-linux-x64-gnu/4.44.1
  - /@rollup/rollup-linux-x64-musl/4.44.1
  - /@rollup/rollup-win32-arm64-msvc/4.44.1
  - /@rollup/rollup-win32-ia32-msvc/4.44.1
  - /@rollup/rollup-win32-x64-msvc/4.44.1
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
