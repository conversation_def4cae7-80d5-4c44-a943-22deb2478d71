"""
Summary Agent - Medical Summaries and <PERSON> using IBM Granite Instruct

Handles medical summaries and doctor chat using granite-3-3-8b-instruct
with 131k context window for 30-day recap queries and patient interactions.

Author: SymptomOS Team
Version: 0.1
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog
import redis.asyncio as redis
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

from .config import Settings
from .granite_client import GraniteInstructClient
from .medical_summarizer import MedicalSummarizer
from .context_manager import ContextManager
from .doctor_chat import Doctor<PERSON>hatHandler
from .schemas import (
    SummaryRequest, SummaryResponse, ChatRequest, ChatResponse,
    ReportRequest, ReportResponse, PatientTimeline, MedicalSummary
)

logger = structlog.get_logger()

# Global state
app_state = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    settings = Settings()
    app_state["settings"] = settings
    app_state["redis"] = redis.from_url(settings.redis_url)
    app_state["granite_client"] = GraniteInstructClient(settings)
    app_state["medical_summarizer"] = MedicalSummarizer(settings)
    app_state["context_manager"] = ContextManager(settings)
    app_state["doctor_chat"] = DoctorChatHandler(settings)
    
    # Initialize clients
    await app_state["granite_client"].initialize()
    await app_state["context_manager"].initialize()
    
    logger.info("Summary agent started", model="granite-3-3-8b-instruct", port=settings.port)
    
    yield
    
    # Shutdown
    await app_state["redis"].close()
    await app_state["granite_client"].cleanup()
    logger.info("Summary agent stopped")

app = FastAPI(
    title="SymptomOS Summary Agent",
    description="Medical summaries and doctor chat using IBM Granite Instruct",
    version="0.1.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.post("/v1/summarize", response_model=SummaryResponse)
async def create_medical_summary(
    request: SummaryRequest
) -> SummaryResponse:
    """
    Create medical summary using Granite Instruct with 131k context window.
    
    Supports 30-day recap queries with sliding window context management.
    Temperature=0.3, top_p=0.9 for readability while maintaining accuracy.
    """
    settings = app_state["settings"]
    granite_client = app_state["granite_client"]
    medical_summarizer = app_state["medical_summarizer"]
    context_manager = app_state["context_manager"]
    
    start_time = time.time()
    
    try:
        # Step 1: Gather patient data within time range
        patient_data = await context_manager.gather_patient_data(
            patient_id=request.patient_id,
            start_date=request.time_range.start_date,
            end_date=request.time_range.end_date,
            data_types=request.include_data_types
        )
        
        # Step 2: Build context with sliding window strategy
        context_window = await context_manager.build_context_window(
            patient_data,
            max_tokens=settings.max_context_tokens,
            summary_type=request.summary_type
        )
        
        # Step 3: Generate summary using Granite Instruct
        summary_result = await granite_client.generate_summary(
            context=context_window,
            summary_type=request.summary_type,
            focus_areas=request.focus_areas,
            temperature=0.3,  # Balanced for readability
            top_p=0.9,
            max_tokens=request.max_length
        )
        
        # Step 4: Post-process and validate summary
        processed_summary = await medical_summarizer.post_process_summary(
            summary_result,
            patient_data,
            request.summary_type
        )
        
        # Step 5: Extract key insights and recommendations
        insights = await medical_summarizer.extract_insights(
            processed_summary,
            patient_data,
            request.focus_areas
        )
        
        processing_time = time.time() - start_time
        
        # Log metrics
        await _log_summary_metrics(
            patient_id=request.patient_id,
            summary_type=request.summary_type,
            processing_time=processing_time,
            context_tokens=len(context_window.split()),
            summary_length=len(processed_summary.split())
        )
        
        return SummaryResponse(
            summary=processed_summary,
            summary_type=request.summary_type,
            patient_id=request.patient_id,
            time_range=request.time_range,
            insights=insights,
            processing_time=processing_time,
            model_used="granite-3-3-8b-instruct",
            context_tokens_used=len(context_window.split()),
            metadata={
                "focus_areas": request.focus_areas,
                "data_types_included": request.include_data_types,
                "summary_length_words": len(processed_summary.split()),
                "confidence_score": summary_result.get("confidence", 0.0)
            }
        )
        
    except Exception as e:
        logger.error("Summary generation failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Summary generation failed: {str(e)}"
        )


@app.post("/v1/chat", response_model=ChatResponse)
async def doctor_chat(
    request: ChatRequest
) -> ChatResponse:
    """
    Handle doctor chat queries with patient context.
    
    Maintains conversation history and provides contextual medical responses.
    """
    doctor_chat_handler = app_state["doctor_chat"]
    context_manager = app_state["context_manager"]
    
    try:
        # Step 1: Load conversation context
        conversation_context = await doctor_chat_handler.load_conversation_context(
            session_id=request.session_id,
            patient_id=request.patient_id
        )
        
        # Step 2: Gather relevant patient data
        if request.patient_id:
            patient_context = await context_manager.get_patient_context(
                patient_id=request.patient_id,
                context_type="chat",
                lookback_days=request.context_days
            )
        else:
            patient_context = None
        
        # Step 3: Generate response
        chat_response = await doctor_chat_handler.generate_response(
            message=request.message,
            conversation_context=conversation_context,
            patient_context=patient_context,
            response_type=request.response_type
        )
        
        # Step 4: Update conversation history
        await doctor_chat_handler.update_conversation_history(
            session_id=request.session_id,
            user_message=request.message,
            assistant_response=chat_response["response"],
            patient_id=request.patient_id
        )
        
        return ChatResponse(
            response=chat_response["response"],
            session_id=request.session_id,
            response_type=request.response_type,
            confidence=chat_response.get("confidence", 0.0),
            suggestions=chat_response.get("suggestions", []),
            patient_references=chat_response.get("patient_references", []),
            metadata={
                "conversation_turn": conversation_context.get("turn_count", 0),
                "patient_context_used": patient_context is not None,
                "response_length": len(chat_response["response"].split())
            }
        )
        
    except Exception as e:
        logger.error("Doctor chat failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Doctor chat failed: {str(e)}"
        )


@app.post("/v1/report", response_model=ReportResponse)
async def generate_medical_report(
    request: ReportRequest
) -> ReportResponse:
    """
    Generate structured medical reports for clinical documentation.
    
    Supports various report types: discharge, consultation, progress notes.
    """
    medical_summarizer = app_state["medical_summarizer"]
    context_manager = app_state["context_manager"]
    granite_client = app_state["granite_client"]
    
    try:
        # Step 1: Gather comprehensive patient data
        patient_data = await context_manager.gather_comprehensive_data(
            patient_id=request.patient_id,
            report_type=request.report_type,
            time_range=request.time_range
        )
        
        # Step 2: Generate structured report
        report = await medical_summarizer.generate_structured_report(
            patient_data=patient_data,
            report_type=request.report_type,
            template=request.template,
            sections=request.sections
        )
        
        # Step 3: Validate medical accuracy
        validated_report = await medical_summarizer.validate_report(
            report,
            patient_data,
            request.report_type
        )
        
        return ReportResponse(
            report=validated_report,
            report_type=request.report_type,
            patient_id=request.patient_id,
            generated_at=datetime.utcnow(),
            sections_included=request.sections,
            template_used=request.template,
            metadata={
                "word_count": len(validated_report.split()),
                "data_sources": list(patient_data.keys()),
                "validation_passed": True
            }
        )
        
    except Exception as e:
        logger.error("Report generation failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Report generation failed: {str(e)}"
        )


@app.get("/v1/timeline/{patient_id}", response_model=PatientTimeline)
async def get_patient_timeline(
    patient_id: str,
    days: int = 30,
    include_predictions: bool = False
) -> PatientTimeline:
    """
    Get patient timeline with optional trend predictions.
    
    Provides chronological view of patient data with insights.
    """
    context_manager = app_state["context_manager"]
    medical_summarizer = app_state["medical_summarizer"]
    
    try:
        # Get timeline data
        timeline_data = await context_manager.build_patient_timeline(
            patient_id=patient_id,
            days=days,
            include_predictions=include_predictions
        )
        
        # Generate timeline insights
        insights = await medical_summarizer.analyze_timeline(
            timeline_data,
            patient_id
        )
        
        return PatientTimeline(
            patient_id=patient_id,
            timeline_data=timeline_data,
            insights=insights,
            time_range_days=days,
            generated_at=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error("Timeline generation failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Timeline generation failed: {str(e)}"
        )


@app.get("/v1/templates")
async def list_report_templates() -> Dict[str, Any]:
    """List available report templates"""
    medical_summarizer = app_state["medical_summarizer"]
    
    try:
        templates = await medical_summarizer.get_available_templates()
        return {
            "templates": templates,
            "total": len(templates),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to list templates", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list templates: {str(e)}"
        )


@app.delete("/v1/chat/{session_id}")
async def clear_chat_session(session_id: str) -> Dict[str, str]:
    """Clear chat session history"""
    doctor_chat_handler = app_state["doctor_chat"]
    
    try:
        await doctor_chat_handler.clear_session(session_id)
        return {
            "status": "success",
            "session_id": session_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to clear chat session", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear chat session: {str(e)}"
        )


@app.get("/v1/health")
async def health_check() -> Dict[str, Any]:
    """Health check with model and context status"""
    granite_client = app_state["granite_client"]
    context_manager = app_state["context_manager"]
    
    try:
        # Check Granite model availability
        granite_healthy = await granite_client.health_check()
        
        # Check context manager
        context_healthy = await context_manager.health_check()
        
        # Check Redis connection
        redis_client = app_state["redis"]
        await redis_client.ping()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "models": {
                "granite_instruct": "healthy" if granite_healthy else "unhealthy",
                "context_manager": "healthy" if context_healthy else "unhealthy"
            },
            "redis": "healthy",
            "version": "0.1.0"
        }
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.get("/healthz")
async def kubernetes_health() -> Dict[str, str]:
    """Kubernetes-style health check"""
    try:
        granite_client = app_state["granite_client"]
        if not await granite_client.health_check():
            raise Exception("Granite client unhealthy")
        
        return {"status": "ok"}
    except Exception:
        raise HTTPException(status_code=503, detail="unhealthy")


async def _log_summary_metrics(
    patient_id: str,
    summary_type: str,
    processing_time: float,
    context_tokens: int,
    summary_length: int
):
    """Log summary metrics to Redis"""
    try:
        redis_client = app_state["redis"]
        
        metrics = {
            "patient_id": patient_id,
            "summary_type": summary_type,
            "processing_time": processing_time,
            "context_tokens": context_tokens,
            "summary_length": summary_length,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await redis_client.lpush("metrics:summary:generations", str(metrics))
        await redis_client.ltrim("metrics:summary:generations", 0, 999)  # Keep last 1000
        
    except Exception as e:
        logger.warning("Failed to log metrics", error=str(e))


if __name__ == "__main__":
    import uvicorn
    settings = Settings()
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.port,
        reload=settings.debug
    )
