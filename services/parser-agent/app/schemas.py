"""
Pydantic schemas for Parser Agent

Defines data models for medical entity extraction, intent classification,
and function calling structures.
"""

from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class EntityType(str, Enum):
    """Medical entity types"""
    SYMPTOM = "symptom"
    MEDICATION = "medication"
    DOSAGE = "dosage"
    FREQUENCY = "frequency"
    DURATION = "duration"
    VITAL_SIGN = "vital_sign"
    BODY_PART = "body_part"
    SEVERITY = "severity"
    TEMPORAL = "temporal"
    PROCEDURE = "procedure"
    CONDITION = "condition"
    ALLERGY = "allergy"


class IntentType(str, Enum):
    """Intent classification types"""
    SYMPTOM_REPORT = "symptom_report"
    MEDICATION_QUERY = "medication_query"
    APPOINTMENT_REQUEST = "appointment_request"
    EMERGENCY = "emergency"
    GENERAL_INQUIRY = "general_inquiry"
    FOLLOW_UP = "follow_up"
    PRESCRIPTION_REFILL = "prescription_refill"
    TEST_RESULTS = "test_results"


class MedicalContext(BaseModel):
    """Medical context for enhanced parsing"""
    patient_id: Optional[str] = Field(None, description="Patient identifier")
    patient_age: Optional[int] = Field(None, description="Patient age")
    patient_gender: Optional[str] = Field(None, description="Patient gender")
    medical_history: List[str] = Field(default_factory=list, description="Known medical conditions")
    current_medications: List[str] = Field(default_factory=list, description="Current medications")
    allergies: List[str] = Field(default_factory=list, description="Known allergies")
    visit_type: Optional[str] = Field(None, description="Type of medical visit")
    
    @validator('patient_age')
    def validate_age(cls, v):
        """Validate patient age"""
        if v is not None and (v < 0 or v > 150):
            raise ValueError("Patient age must be between 0 and 150")
        return v


class ExtractedEntity(BaseModel):
    """Individual extracted medical entity"""
    text: str = Field(..., description="Original text of the entity")
    entity_type: EntityType = Field(..., description="Type of medical entity")
    confidence: float = Field(..., description="Confidence score (0-1)")
    start_pos: Optional[int] = Field(None, description="Start position in text")
    end_pos: Optional[int] = Field(None, description="End position in text")
    normalized_value: Optional[str] = Field(None, description="Normalized/standardized value")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="Additional attributes")
    
    @validator('confidence')
    def validate_confidence(cls, v):
        """Validate confidence score"""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Confidence must be between 0 and 1")
        return v


class IntentClassification(BaseModel):
    """Intent classification result"""
    intent: IntentType = Field(..., description="Classified intent")
    confidence: float = Field(..., description="Confidence score (0-1)")
    reasoning: Optional[str] = Field(None, description="Reasoning for classification")
    urgency: str = Field(default="normal", description="Urgency level (low, normal, high, critical)")
    
    @validator('confidence')
    def validate_confidence(cls, v):
        """Validate confidence score"""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Confidence must be between 0 and 1")
        return v
    
    @validator('urgency')
    def validate_urgency(cls, v):
        """Validate urgency level"""
        allowed_urgency = {'low', 'normal', 'high', 'critical'}
        if v not in allowed_urgency:
            raise ValueError(f"Urgency must be one of: {allowed_urgency}")
        return v


class ParseRequest(BaseModel):
    """Request for medical text parsing"""
    text: str = Field(..., description="Text to parse")
    context: Optional[MedicalContext] = Field(None, description="Medical context")
    entity_types: Optional[List[EntityType]] = Field(None, description="Specific entity types to extract")
    create_nodes: bool = Field(default=False, description="Create OnionGraph nodes")
    patient_id: Optional[str] = Field(None, description="Patient ID for node creation")
    
    @validator('text')
    def validate_text(cls, v):
        """Validate input text"""
        if not v or not v.strip():
            raise ValueError("Text cannot be empty")
        if len(v) > 10000:  # 10k character limit
            raise ValueError("Text exceeds maximum length (10,000 characters)")
        return v.strip()


class EntityExtractionRequest(BaseModel):
    """Request for entity extraction only"""
    text: str = Field(..., description="Text to extract entities from")
    entity_types: Optional[List[EntityType]] = Field(None, description="Specific entity types")
    context: Optional[MedicalContext] = Field(None, description="Medical context")
    confidence_threshold: float = Field(default=0.7, description="Minimum confidence threshold")
    
    @validator('confidence_threshold')
    def validate_threshold(cls, v):
        """Validate confidence threshold"""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Confidence threshold must be between 0 and 1")
        return v


class MedicalInsight(BaseModel):
    """Medical insight generated from entities"""
    insight_type: str = Field(..., description="Type of insight")
    description: str = Field(..., description="Human-readable description")
    severity: str = Field(..., description="Severity level")
    recommendations: List[str] = Field(default_factory=list, description="Recommended actions")
    confidence: float = Field(..., description="Confidence in insight")
    
    @validator('severity')
    def validate_severity(cls, v):
        """Validate severity level"""
        allowed_severity = {'low', 'medium', 'high', 'critical'}
        if v not in allowed_severity:
            raise ValueError(f"Severity must be one of: {allowed_severity}")
        return v


class GraphNode(BaseModel):
    """OnionGraph node for medical data"""
    node_id: str = Field(..., description="Unique node identifier")
    event_type: str = Field(..., description="Event type for OnionGraph")
    metadata: Dict[str, Any] = Field(..., description="Node metadata")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Node timestamp")
    patient_id: Optional[str] = Field(None, description="Associated patient ID")


class ParseResponse(BaseModel):
    """Response from medical text parsing"""
    entities: List[ExtractedEntity] = Field(..., description="Extracted medical entities")
    intent: IntentClassification = Field(..., description="Classified intent")
    insights: List[MedicalInsight] = Field(default_factory=list, description="Generated insights")
    nodes: List[GraphNode] = Field(default_factory=list, description="OnionGraph nodes")
    processing_time: float = Field(..., description="Processing time in seconds")
    model_used: str = Field(..., description="Model used for extraction")
    confidence: float = Field(..., description="Overall confidence score")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class FunctionCall(BaseModel):
    """Function call structure for Granite Instruct"""
    name: str = Field(..., description="Function name")
    arguments: Dict[str, Any] = Field(..., description="Function arguments")
    
    @validator('name')
    def validate_function_name(cls, v):
        """Validate function name"""
        allowed_functions = {
            'insert_nodes', 'extract_entities', 'classify_intent',
            'create_medical_summary', 'analyze_symptoms'
        }
        if v not in allowed_functions:
            raise ValueError(f"Function '{v}' not in allowed list: {allowed_functions}")
        return v


class FunctionCallResponse(BaseModel):
    """Response from function call execution"""
    success: bool = Field(..., description="Whether function call succeeded")
    result: Any = Field(None, description="Function result")
    error: Optional[str] = Field(None, description="Error message if failed")
    function_name: str = Field(..., description="Function that was called")
    execution_time: float = Field(..., description="Execution time in seconds")


class MedicalTerminology(BaseModel):
    """Medical terminology for entity recognition"""
    symptoms: List[str] = Field(default_factory=list, description="Symptom terms")
    medications: List[str] = Field(default_factory=list, description="Medication names")
    body_parts: List[str] = Field(default_factory=list, description="Anatomical terms")
    procedures: List[str] = Field(default_factory=list, description="Medical procedures")
    conditions: List[str] = Field(default_factory=list, description="Medical conditions")
    
    @classmethod
    def get_default_terminology(cls) -> "MedicalTerminology":
        """Get default medical terminology"""
        return cls(
            symptoms=[
                "headache", "nausea", "fever", "fatigue", "dizziness", "pain",
                "shortness of breath", "chest pain", "abdominal pain", "vomiting",
                "diarrhea", "constipation", "insomnia", "anxiety", "depression",
                "cough", "sore throat", "runny nose", "muscle aches", "joint pain"
            ],
            medications=[
                "aspirin", "ibuprofen", "acetaminophen", "tylenol", "advil",
                "metformin", "lisinopril", "atorvastatin", "omeprazole",
                "levothyroxine", "amlodipine", "metoprolol", "losartan",
                "simvastatin", "hydrochlorothiazide", "gabapentin", "sertraline"
            ],
            body_parts=[
                "head", "neck", "chest", "abdomen", "back", "arms", "legs",
                "heart", "lungs", "liver", "kidney", "brain", "stomach",
                "intestine", "spine", "joints", "muscles", "bones", "skin"
            ],
            procedures=[
                "blood pressure", "temperature", "pulse", "weight", "height",
                "blood test", "urine test", "x-ray", "mri", "ct scan",
                "ultrasound", "ecg", "ekg", "colonoscopy", "endoscopy",
                "biopsy", "surgery", "vaccination", "injection"
            ],
            conditions=[
                "diabetes", "hypertension", "asthma", "arthritis", "depression",
                "anxiety", "migraine", "allergies", "heart disease", "cancer",
                "stroke", "pneumonia", "bronchitis", "flu", "cold"
            ]
        )


class ParsingMetrics(BaseModel):
    """Parser performance metrics"""
    total_requests: int = Field(default=0, description="Total parsing requests")
    successful_extractions: int = Field(default=0, description="Successful entity extractions")
    failed_extractions: int = Field(default=0, description="Failed extractions")
    granite_usage: int = Field(default=0, description="Granite model usage count")
    regex_fallback_usage: int = Field(default=0, description="Regex fallback usage count")
    average_processing_time: float = Field(default=0.0, description="Average processing time")
    average_entity_count: float = Field(default=0.0, description="Average entities per request")
    average_confidence: float = Field(default=0.0, description="Average confidence score")
    
    def update_success(self, processing_time: float, entity_count: int, confidence: float, model: str):
        """Update metrics for successful extraction"""
        self.total_requests += 1
        self.successful_extractions += 1
        
        if model == "granite-3-3-8b-instruct":
            self.granite_usage += 1
        elif model == "regex_fallback":
            self.regex_fallback_usage += 1
        
        # Update averages
        total_successful = self.successful_extractions
        self.average_processing_time = (
            (self.average_processing_time * (total_successful - 1) + processing_time) / total_successful
        )
        self.average_entity_count = (
            (self.average_entity_count * (total_successful - 1) + entity_count) / total_successful
        )
        self.average_confidence = (
            (self.average_confidence * (total_successful - 1) + confidence) / total_successful
        )
    
    def update_failure(self):
        """Update metrics for failed extraction"""
        self.total_requests += 1
        self.failed_extractions += 1
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_extractions / self.total_requests
    
    @property
    def granite_usage_rate(self) -> float:
        """Calculate Granite model usage rate"""
        if self.successful_extractions == 0:
            return 0.0
        return self.granite_usage / self.successful_extractions
