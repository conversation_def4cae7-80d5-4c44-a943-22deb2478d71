"""
Parser Agent - Medical Entity Extraction using IBM Granite Instruct

Handles intent classification and medical entity extraction using
ibm/granite-3-3-8b-instruct with function calling and regex fallbacks.

Author: SymptomOS Team
Version: 0.1
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
import structlog
import redis.asyncio as redis
from contextlib import asynccontextmanager

from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

from .config import Settings
from .granite_client import GraniteInstructClient
from .medical_parser import MedicalEntityParser
from .regex_fallback import RegexFallback
from .function_calling import FunctionCallHandler
from .schemas import (
    ParseRequest, ParseResponse, EntityExtractionRequest,
    ExtractedEntity, IntentClassification, MedicalContext
)

logger = structlog.get_logger()

# Global state
app_state = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    settings = Settings()
    app_state["settings"] = settings
    app_state["redis"] = redis.from_url(settings.redis_url)
    app_state["granite_client"] = GraniteInstructClient(settings)
    app_state["medical_parser"] = MedicalEntityParser(settings)
    app_state["regex_fallback"] = RegexFallback(settings)
    app_state["function_handler"] = FunctionCallHandler(settings)
    
    # Initialize clients
    await app_state["granite_client"].initialize()
    await app_state["medical_parser"].initialize()
    
    logger.info("Parser agent started", model="granite-3-3-8b-instruct", port=settings.port)
    
    yield
    
    # Shutdown
    await app_state["redis"].close()
    await app_state["granite_client"].cleanup()
    logger.info("Parser agent stopped")

app = FastAPI(
    title="SymptomOS Parser Agent",
    description="Medical entity extraction and intent classification using IBM Granite Instruct",
    version="0.1.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.post("/v1/parse", response_model=ParseResponse)
async def parse_medical_text(
    request: ParseRequest
) -> ParseResponse:
    """
    Parse medical text for entities and intent using Granite Instruct.
    
    Uses function calling for structured extraction with regex fallback
    for reliability. Optimized for medical domain with temperature=0.
    """
    settings = app_state["settings"]
    granite_client = app_state["granite_client"]
    medical_parser = app_state["medical_parser"]
    regex_fallback = app_state["regex_fallback"]
    function_handler = app_state["function_handler"]
    
    start_time = time.time()
    
    try:
        # Step 1: Preprocess text for medical domain
        processed_text = await medical_parser.preprocess_text(
            request.text,
            request.context
        )
        
        # Step 2: Intent classification using Granite Instruct
        intent_result = await granite_client.classify_intent(
            processed_text,
            request.context,
            temperature=0.0,  # Deterministic for medical accuracy
            top_p=0.1
        )
        
        # Step 3: Entity extraction using function calling
        extraction_success = False
        entities = []
        
        try:
            # Try Granite function calling first
            function_result = await granite_client.extract_entities_with_functions(
                processed_text,
                request.context,
                request.entity_types
            )
            
            if function_result and function_result.get("success"):
                entities = function_result["entities"]
                extraction_success = True
                model_used = "granite-3-3-8b-instruct"
                
        except Exception as e:
            logger.warning("Granite function calling failed", error=str(e))
        
        # Step 4: Fallback to regex if function calling fails
        if not extraction_success:
            logger.info("Using regex fallback for entity extraction")
            
            regex_result = await regex_fallback.extract_entities(
                processed_text,
                request.entity_types,
                request.context
            )
            
            entities = regex_result["entities"]
            model_used = "regex_fallback"
        
        # Step 5: Post-process and validate entities
        validated_entities = await medical_parser.validate_entities(
            entities,
            request.context
        )
        
        # Step 6: Generate medical insights
        insights = await medical_parser.generate_insights(
            validated_entities,
            intent_result,
            request.context
        )
        
        processing_time = time.time() - start_time
        
        # Step 7: Create nodes for OnionGraph if requested
        nodes = []
        if request.create_nodes:
            nodes = await function_handler.create_graph_nodes(
                validated_entities,
                intent_result,
                request.context,
                request.patient_id
            )
        
        # Log metrics
        await _log_parsing_metrics(
            model_used=model_used,
            processing_time=processing_time,
            entity_count=len(validated_entities),
            intent_confidence=intent_result.get("confidence", 0.0)
        )
        
        return ParseResponse(
            entities=validated_entities,
            intent=IntentClassification(**intent_result),
            insights=insights,
            nodes=nodes,
            processing_time=processing_time,
            model_used=model_used,
            confidence=intent_result.get("confidence", 0.0),
            metadata={
                "original_text": request.text,
                "processed_text": processed_text,
                "extraction_method": "function_calling" if extraction_success else "regex_fallback",
                "entity_types_requested": request.entity_types,
                "medical_context": request.context.dict() if request.context else None
            }
        )
        
    except Exception as e:
        logger.error("Parsing failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Parsing failed: {str(e)}"
        )


@app.post("/v1/extract", response_model=List[ExtractedEntity])
async def extract_entities_only(
    request: EntityExtractionRequest
) -> List[ExtractedEntity]:
    """
    Extract only medical entities without intent classification.
    
    Optimized for high-throughput entity extraction scenarios.
    """
    granite_client = app_state["granite_client"]
    medical_parser = app_state["medical_parser"]
    regex_fallback = app_state["regex_fallback"]
    
    try:
        # Preprocess text
        processed_text = await medical_parser.preprocess_text(
            request.text,
            request.context
        )
        
        # Try Granite extraction first
        try:
            function_result = await granite_client.extract_entities_with_functions(
                processed_text,
                request.context,
                request.entity_types
            )
            
            if function_result and function_result.get("success"):
                entities = function_result["entities"]
            else:
                raise Exception("Function calling returned no results")
                
        except Exception:
            # Fallback to regex
            regex_result = await regex_fallback.extract_entities(
                processed_text,
                request.entity_types,
                request.context
            )
            entities = regex_result["entities"]
        
        # Validate entities
        validated_entities = await medical_parser.validate_entities(
            entities,
            request.context
        )
        
        return validated_entities
        
    except Exception as e:
        logger.error("Entity extraction failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Entity extraction failed: {str(e)}"
        )


@app.post("/v1/classify", response_model=IntentClassification)
async def classify_intent_only(
    text: str,
    context: Optional[MedicalContext] = None
) -> IntentClassification:
    """
    Classify intent without entity extraction.
    
    Useful for quick intent determination in conversational flows.
    """
    granite_client = app_state["granite_client"]
    medical_parser = app_state["medical_parser"]
    
    try:
        # Preprocess text
        processed_text = await medical_parser.preprocess_text(text, context)
        
        # Classify intent
        intent_result = await granite_client.classify_intent(
            processed_text,
            context,
            temperature=0.0,
            top_p=0.1
        )
        
        return IntentClassification(**intent_result)
        
    except Exception as e:
        logger.error("Intent classification failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Intent classification failed: {str(e)}"
        )


@app.post("/v1/function-call")
async def handle_function_call(
    function_name: str,
    arguments: Dict[str, Any],
    context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Handle direct function calls from other agents.
    
    Supports: insert_nodes, extract_entities, classify_intent
    """
    function_handler = app_state["function_handler"]
    
    try:
        result = await function_handler.execute_function(
            function_name,
            arguments,
            context
        )
        
        return {
            "success": True,
            "result": result,
            "function": function_name,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Function call failed", function=function_name, error=str(e))
        return {
            "success": False,
            "error": str(e),
            "function": function_name,
            "timestamp": datetime.utcnow().isoformat()
        }


@app.get("/v1/entity-types")
async def list_entity_types() -> Dict[str, List[str]]:
    """Get supported medical entity types"""
    medical_parser = app_state["medical_parser"]
    return await medical_parser.get_supported_entity_types()


@app.get("/v1/intent-types")
async def list_intent_types() -> Dict[str, str]:
    """Get supported intent types"""
    return {
        "symptom_report": "Patient reporting symptoms",
        "medication_query": "Questions about medications",
        "appointment_request": "Scheduling or appointment related",
        "emergency": "Emergency or urgent medical situation",
        "general_inquiry": "General medical questions",
        "follow_up": "Follow-up on previous consultation",
        "prescription_refill": "Prescription refill requests",
        "test_results": "Discussion of test results"
    }


@app.get("/v1/health")
async def health_check() -> Dict[str, Any]:
    """Health check with model status"""
    granite_client = app_state["granite_client"]
    medical_parser = app_state["medical_parser"]
    
    try:
        # Check Granite model availability
        granite_healthy = await granite_client.health_check()
        
        # Check medical parser
        parser_healthy = await medical_parser.health_check()
        
        # Check Redis connection
        redis_client = app_state["redis"]
        await redis_client.ping()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "models": {
                "granite_instruct": "healthy" if granite_healthy else "unhealthy",
                "medical_parser": "healthy" if parser_healthy else "unhealthy"
            },
            "redis": "healthy",
            "version": "0.1.0"
        }
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.get("/healthz")
async def kubernetes_health() -> Dict[str, str]:
    """Kubernetes-style health check"""
    try:
        granite_client = app_state["granite_client"]
        if not await granite_client.health_check():
            raise Exception("Granite client unhealthy")
        
        return {"status": "ok"}
    except Exception:
        raise HTTPException(status_code=503, detail="unhealthy")


async def _log_parsing_metrics(
    model_used: str,
    processing_time: float,
    entity_count: int,
    intent_confidence: float
):
    """Log parsing metrics to Redis"""
    try:
        redis_client = app_state["redis"]
        
        metrics = {
            "model": model_used,
            "processing_time": processing_time,
            "entity_count": entity_count,
            "intent_confidence": intent_confidence,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await redis_client.lpush("metrics:parser:extractions", str(metrics))
        await redis_client.ltrim("metrics:parser:extractions", 0, 999)  # Keep last 1000
        
    except Exception as e:
        logger.warning("Failed to log metrics", error=str(e))


if __name__ == "__main__":
    import uvicorn
    settings = Settings()
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.port,
        reload=settings.debug
    )
