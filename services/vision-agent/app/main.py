"""
Vision Agent - Medical Image Analysis using IBM Granite Vision

Handles image captioning, OCR, and medical image analysis using
ibm/granite-vision-3.2-2b for wounds, rashes, X-rays, and medical documents.

Author: SymptomOS Team
Version: 0.1
"""

import asyncio
import base64
import io
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
import structlog
import redis.asyncio as redis
from contextlib import asynccontextmanager
from PIL import Image

from fastapi import FastAPI, HTTPException, UploadFile, File, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

from .config import Settings
from .granite_client import GraniteVisionClient
from .image_processor import ImageProcessor
from .medical_analyzer import MedicalImageAnalyzer
from .ocr_engine import OCREngine
from .schemas import (
    VisionRequest, VisionResponse, ImageAnalysisRequest,
    ImageAnalysisResponse, OCRRequest, OCRResponse,
    MedicalImageMetadata, ImageInsight
)

logger = structlog.get_logger()

# Global state
app_state = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    settings = Settings()
    app_state["settings"] = settings
    app_state["redis"] = redis.from_url(settings.redis_url)
    app_state["granite_client"] = GraniteVisionClient(settings)
    app_state["image_processor"] = ImageProcessor(settings)
    app_state["medical_analyzer"] = MedicalImageAnalyzer(settings)
    app_state["ocr_engine"] = OCREngine(settings)
    
    # Initialize clients
    await app_state["granite_client"].initialize()
    await app_state["medical_analyzer"].initialize()
    
    logger.info("Vision agent started", model="granite-vision-3.2-2b", port=settings.port)
    
    yield
    
    # Shutdown
    await app_state["redis"].close()
    await app_state["granite_client"].cleanup()
    logger.info("Vision agent stopped")

app = FastAPI(
    title="SymptomOS Vision Agent",
    description="Medical image analysis using IBM Granite Vision",
    version="0.1.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.post("/v1/vision", response_model=VisionResponse)
async def analyze_image(
    request: VisionRequest
) -> VisionResponse:
    """
    Comprehensive image analysis using Granite Vision.
    
    Supports caption, OCR, labeling, and medical analysis tasks.
    2B multimodal model focused on document & enterprise imagery.
    """
    settings = app_state["settings"]
    granite_client = app_state["granite_client"]
    image_processor = app_state["image_processor"]
    medical_analyzer = app_state["medical_analyzer"]
    ocr_engine = app_state["ocr_engine"]
    
    start_time = time.time()
    
    try:
        # Step 1: Validate and preprocess image
        image_data, metadata = await image_processor.process_image(
            request.image_b64,
            max_size=(2048, 2048),
            supported_formats=["jpg", "jpeg", "png", "tiff"]
        )
        
        # Step 2: Perform requested analysis tasks
        results = {}
        
        # Caption generation
        if "caption" in request.tasks:
            caption_result = await granite_client.generate_caption(
                image_data,
                context=request.medical_context,
                focus="medical" if request.medical_context else "general"
            )
            results["caption"] = caption_result["caption"]
        
        # OCR text extraction
        if "ocr" in request.tasks:
            ocr_result = await ocr_engine.extract_text(
                image_data,
                language="en",
                medical_context=request.medical_context
            )
            results["ocr"] = ocr_result["text"]
        
        # Label detection
        if "label" in request.tasks:
            label_result = await granite_client.detect_labels(
                image_data,
                confidence_threshold=0.5,
                medical_focus=request.medical_context is not None
            )
            results["labels"] = label_result["labels"]
        
        # Medical analysis
        if "medical_analysis" in request.tasks and request.medical_context:
            medical_result = await medical_analyzer.analyze_medical_image(
                image_data,
                metadata,
                request.medical_context
            )
            results["medical_analysis"] = medical_result
        
        # Step 3: Calculate safety score
        safety_score = await _calculate_image_safety_score(
            image_data,
            results,
            request.medical_context
        )
        
        processing_time = time.time() - start_time
        
        # Log metrics
        await _log_vision_metrics(
            tasks=request.tasks,
            processing_time=processing_time,
            image_size=metadata.size_bytes,
            safety_score=safety_score
        )
        
        return VisionResponse(
            caption=results.get("caption", ""),
            ocr=results.get("ocr", ""),
            labels=results.get("labels", []),
            medical_analysis=results.get("medical_analysis"),
            safety_score=safety_score,
            processing_time=processing_time,
            model_used="granite-vision-3.2-2b",
            image_metadata=metadata,
            tasks_completed=request.tasks
        )
        
    except Exception as e:
        logger.error("Vision analysis failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Vision analysis failed: {str(e)}"
        )


@app.post("/v1/medical-image", response_model=ImageAnalysisResponse)
async def analyze_medical_image(
    request: ImageAnalysisRequest
) -> ImageAnalysisResponse:
    """
    Specialized medical image analysis for clinical use.
    
    Supports wound assessment, rash analysis, X-ray interpretation, etc.
    """
    medical_analyzer = app_state["medical_analyzer"]
    image_processor = app_state["image_processor"]
    
    try:
        # Process image
        image_data, metadata = await image_processor.process_image(
            request.image_b64,
            max_size=(2048, 2048)
        )
        
        # Perform medical analysis
        analysis = await medical_analyzer.comprehensive_analysis(
            image_data=image_data,
            metadata=metadata,
            analysis_types=request.analysis_types,
            patient_context=request.patient_context,
            clinical_question=request.clinical_question
        )
        
        return ImageAnalysisResponse(
            analysis_results=analysis["results"],
            insights=analysis["insights"],
            recommendations=analysis["recommendations"],
            confidence_scores=analysis["confidence_scores"],
            risk_assessment=analysis["risk_assessment"],
            patient_id=request.patient_context.get("patient_id") if request.patient_context else None,
            analyzed_at=datetime.utcnow(),
            metadata={
                "analysis_types": request.analysis_types,
                "clinical_question": request.clinical_question,
                "image_quality": analysis.get("image_quality", "unknown")
            }
        )
        
    except Exception as e:
        logger.error("Medical image analysis failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Medical image analysis failed: {str(e)}"
        )


@app.post("/v1/ocr", response_model=OCRResponse)
async def extract_text_from_image(
    request: OCRRequest
) -> OCRResponse:
    """
    Extract text from medical documents and prescriptions.
    
    Optimized for medical handwriting and pharmaceutical labels.
    """
    ocr_engine = app_state["ocr_engine"]
    image_processor = app_state["image_processor"]
    
    try:
        # Process image
        image_data, metadata = await image_processor.process_image(
            request.image_b64,
            enhance_for_ocr=True
        )
        
        # Extract text
        ocr_result = await ocr_engine.extract_text_detailed(
            image_data=image_data,
            language=request.language,
            document_type=request.document_type,
            medical_context=request.medical_context
        )
        
        return OCRResponse(
            text=ocr_result["text"],
            confidence=ocr_result["confidence"],
            text_blocks=ocr_result["text_blocks"],
            detected_entities=ocr_result.get("entities", []),
            document_type=request.document_type,
            language=request.language,
            processing_time=ocr_result["processing_time"]
        )
        
    except Exception as e:
        logger.error("OCR extraction failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"OCR extraction failed: {str(e)}"
        )


@app.post("/v1/upload")
async def upload_and_analyze(
    file: UploadFile = File(...),
    tasks: List[str] = ["caption", "ocr"],
    medical_context: Optional[str] = None
) -> VisionResponse:
    """
    Upload image file and perform analysis.
    
    Supports common image formats with automatic format detection.
    """
    try:
        # Read and encode image
        image_content = await file.read()
        image_b64 = base64.b64encode(image_content).decode('utf-8')
        
        # Create vision request
        request = VisionRequest(
            image_b64=image_b64,
            tasks=tasks,
            medical_context=medical_context
        )
        
        return await analyze_image(request)
        
    except Exception as e:
        logger.error("File upload analysis failed", filename=file.filename, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"File upload analysis failed: {str(e)}"
        )


@app.get("/v1/supported-tasks")
async def get_supported_tasks() -> Dict[str, Any]:
    """Get list of supported vision analysis tasks"""
    return {
        "tasks": {
            "caption": "Generate descriptive captions for images",
            "ocr": "Extract text from images and documents",
            "label": "Detect and label objects in images",
            "medical_analysis": "Specialized medical image analysis"
        },
        "medical_analysis_types": {
            "wound_assessment": "Analyze wounds, cuts, and injuries",
            "rash_analysis": "Analyze skin conditions and rashes",
            "document_analysis": "Analyze medical documents and forms",
            "prescription_analysis": "Analyze prescription bottles and labels",
            "xray_interpretation": "Basic X-ray and imaging interpretation"
        },
        "supported_formats": ["jpg", "jpeg", "png", "tiff", "bmp"],
        "max_image_size": "2048x2048 pixels"
    }


@app.get("/v1/medical-templates")
async def get_medical_templates() -> Dict[str, Any]:
    """Get medical analysis templates and guidelines"""
    medical_analyzer = app_state["medical_analyzer"]
    
    try:
        templates = await medical_analyzer.get_analysis_templates()
        return {
            "templates": templates,
            "total": len(templates),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get medical templates", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get medical templates: {str(e)}"
        )


@app.get("/v1/health")
async def health_check() -> Dict[str, Any]:
    """Health check with vision model status"""
    granite_client = app_state["granite_client"]
    medical_analyzer = app_state["medical_analyzer"]
    
    try:
        # Check Granite Vision model
        vision_healthy = await granite_client.health_check()
        
        # Check medical analyzer
        analyzer_healthy = await medical_analyzer.health_check()
        
        # Check Redis connection
        redis_client = app_state["redis"]
        await redis_client.ping()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "models": {
                "granite_vision": "healthy" if vision_healthy else "unhealthy",
                "medical_analyzer": "healthy" if analyzer_healthy else "unhealthy"
            },
            "redis": "healthy",
            "version": "0.1.0"
        }
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.get("/healthz")
async def kubernetes_health() -> Dict[str, str]:
    """Kubernetes-style health check"""
    try:
        granite_client = app_state["granite_client"]
        if not await granite_client.health_check():
            raise Exception("Granite Vision client unhealthy")
        
        return {"status": "ok"}
    except Exception:
        raise HTTPException(status_code=503, detail="unhealthy")


async def _calculate_image_safety_score(
    image_data: bytes,
    analysis_results: Dict[str, Any],
    medical_context: Optional[str]
) -> float:
    """Calculate safety score for image content"""
    try:
        # Basic safety scoring based on content
        safety_score = 1.0
        
        # Check for potentially sensitive content in labels
        if "labels" in analysis_results:
            sensitive_labels = ["injury", "wound", "blood", "surgery"]
            for label in analysis_results["labels"]:
                if any(sensitive in label.lower() for sensitive in sensitive_labels):
                    safety_score -= 0.1
        
        # Medical context increases safety score
        if medical_context:
            safety_score += 0.1
        
        return max(0.0, min(1.0, safety_score))
        
    except Exception:
        return 0.5  # Default neutral score on error


async def _log_vision_metrics(
    tasks: List[str],
    processing_time: float,
    image_size: int,
    safety_score: float
):
    """Log vision analysis metrics to Redis"""
    try:
        redis_client = app_state["redis"]
        
        metrics = {
            "tasks": tasks,
            "processing_time": processing_time,
            "image_size": image_size,
            "safety_score": safety_score,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await redis_client.lpush("metrics:vision:analyses", str(metrics))
        await redis_client.ltrim("metrics:vision:analyses", 0, 999)  # Keep last 1000
        
    except Exception as e:
        logger.warning("Failed to log metrics", error=str(e))


if __name__ == "__main__":
    import uvicorn
    settings = Settings()
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.port,
        reload=settings.debug
    )
