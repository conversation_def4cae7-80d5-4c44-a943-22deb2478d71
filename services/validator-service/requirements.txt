# Validator Service Dependencies

# FastAPI and ASGI
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0

# Security and JWT
PyJWT>=2.8.0
cryptography>=41.0.0

# Redis for message bus
redis[hiredis]>=5.0.0

# HTTP client for agent health checks
httpx>=0.25.0

# Configuration and YAML
pyyaml>=6.0.1
python-dotenv>=1.0.0

# Logging and monitoring
structlog>=23.2.0
prometheus-client>=0.19.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.0.0
ruff>=0.1.0
mypy>=1.7.0

# Optional: For enhanced security
bcrypt>=4.1.0
passlib[bcrypt]>=1.7.4
