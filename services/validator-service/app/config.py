"""
Configuration for Validator Service

Handles environment variables, model mapping, and service settings
for the secure command validator.
"""

import os
import yaml
from typing import Dict, Any, Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings"""
    
    # Service configuration
    port: int = Field(default=8020, env="VALIDATOR_PORT")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Security settings
    jwt_secret: str = Field(..., env="JWT_SECRET")
    jwt_algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    
    # Redis configuration
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    redis_stream_prefix: str = Field(default="symptomos", env="REDIS_STREAM_PREFIX")
    
    # Model mapping configuration
    model_map_path: str = Field(
        default="/app/config/model-map.yaml",
        env="MODEL_MAP_PATH"
    )
    
    # Rate limiting
    max_requests_per_minute: int = Field(default=1000, env="MAX_REQUESTS_PER_MINUTE")
    max_batch_size: int = Field(default=10, env="MAX_BATCH_SIZE")
    
    # Security thresholds
    max_violations_per_hour: int = Field(default=100, env="MAX_VIOLATIONS_PER_HOUR")
    auto_block_threshold: int = Field(default=10, env="AUTO_BLOCK_THRESHOLD")
    
    # Agent endpoints
    asr_agent_url: str = Field(default="http://localhost:8010", env="ASR_AGENT_URL")
    parser_agent_url: str = Field(default="http://localhost:8011", env="PARSER_AGENT_URL")
    embed_agent_url: str = Field(default="http://localhost:8012", env="EMBED_AGENT_URL")
    summary_agent_url: str = Field(default="http://localhost:8013", env="SUMMARY_AGENT_URL")
    guardian_agent_url: str = Field(default="http://localhost:8014", env="GUARDIAN_AGENT_URL")
    vision_agent_url: str = Field(default="http://localhost:8015", env="VISION_AGENT_URL")
    
    # Monitoring
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    health_check_interval: int = Field(default=30, env="HEALTH_CHECK_INTERVAL")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


class ModelMapConfig:
    """Model mapping configuration loader"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self._config: Optional[Dict[str, Any]] = None
        self._load_config()
    
    def _load_config(self):
        """Load model mapping configuration from YAML"""
        try:
            with open(self.config_path, 'r') as f:
                self._config = yaml.safe_load(f)
        except FileNotFoundError:
            # Fallback configuration if file not found
            self._config = self._get_default_config()
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML in model config: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Default configuration if file not found"""
        return {
            "agents": {
                "asr-agent": {
                    "model_id": "ibm/granite-speech-3-3-8b",
                    "tools": ["transcribe_audio"]
                },
                "parser-agent": {
                    "model_id": "ibm/granite-3-3-8b-instruct",
                    "tools": ["insert_nodes", "extract_entities"]
                },
                "embed-agent": {
                    "model_id": "ibm/granite-embedding-107m-multilingual",
                    "tools": ["generate_embeddings"]
                },
                "summary-agent": {
                    "model_id": "ibm/granite-3-3-8b-instruct",
                    "tools": ["make_summary", "answer_query", "generate_report"]
                },
                "guardian-agent": {
                    "model_id": "ibm/granite-guardian-3-8b",
                    "tools": ["safety_check"]
                },
                "vision-agent": {
                    "model_id": "ibm/granite-vision-3.2-2b",
                    "tools": ["insert_image_node", "analyze_medical_image", "extract_text"],
                    "optional": True
                }
            }
        }
    
    def get_agent_for_tool(self, tool: str) -> Optional[str]:
        """Get the agent responsible for a specific tool"""
        if not self._config:
            return None
        
        agents = self._config.get("agents", {})
        for agent_name, agent_config in agents.items():
            tools = agent_config.get("tools", [])
            if tool in tools:
                return agent_name
        
        return None
    
    def get_agent_config(self, agent_name: str) -> Optional[Dict[str, Any]]:
        """Get configuration for a specific agent"""
        if not self._config:
            return None
        
        return self._config.get("agents", {}).get(agent_name)
    
    def get_all_agents(self) -> Dict[str, Dict[str, Any]]:
        """Get all agent configurations"""
        if not self._config:
            return {}
        
        return self._config.get("agents", {})
    
    def get_tool_mapping(self) -> Dict[str, str]:
        """Get mapping of tools to agents"""
        mapping = {}
        
        if not self._config:
            return mapping
        
        agents = self._config.get("agents", {})
        for agent_name, agent_config in agents.items():
            tools = agent_config.get("tools", [])
            for tool in tools:
                mapping[tool] = agent_name
        
        return mapping
    
    def is_agent_optional(self, agent_name: str) -> bool:
        """Check if an agent is optional"""
        agent_config = self.get_agent_config(agent_name)
        if not agent_config:
            return False
        
        return agent_config.get("optional", False)
    
    def get_model_id(self, agent_name: str) -> Optional[str]:
        """Get the model ID for an agent"""
        agent_config = self.get_agent_config(agent_name)
        if not agent_config:
            return None
        
        return agent_config.get("model_id")
    
    def get_runtime_hints(self, agent_name: str) -> Dict[str, Any]:
        """Get runtime hints for an agent"""
        agent_config = self.get_agent_config(agent_name)
        if not agent_config:
            return {}
        
        return agent_config.get("runtime_hints", {})
    
    def reload_config(self):
        """Reload configuration from file"""
        self._load_config()


class SecurityConfig:
    """Security-specific configuration"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
    
    def get_allowed_origins(self) -> list:
        """Get allowed CORS origins"""
        origins = os.getenv("ALLOWED_ORIGINS", "").split(",")
        return [origin.strip() for origin in origins if origin.strip()]
    
    def get_rate_limit_config(self) -> Dict[str, int]:
        """Get rate limiting configuration"""
        return {
            "requests_per_minute": self.settings.max_requests_per_minute,
            "batch_size": self.settings.max_batch_size,
            "violations_per_hour": self.settings.max_violations_per_hour,
            "auto_block_threshold": self.settings.auto_block_threshold
        }
    
    def get_jwt_config(self) -> Dict[str, str]:
        """Get JWT configuration"""
        return {
            "secret": self.settings.jwt_secret,
            "algorithm": self.settings.jwt_algorithm
        }
    
    def is_development_mode(self) -> bool:
        """Check if running in development mode"""
        return self.settings.debug or os.getenv("ENVIRONMENT", "").lower() == "development"


class RedisConfig:
    """Redis-specific configuration"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
    
    def get_stream_names(self) -> Dict[str, str]:
        """Get Redis stream names"""
        prefix = self.settings.redis_stream_prefix
        return {
            "commands": f"{prefix}:commands",
            "violations": f"{prefix}:violations",
            "health": f"{prefix}:health",
            "metrics": f"{prefix}:metrics"
        }
    
    def get_connection_config(self) -> Dict[str, Any]:
        """Get Redis connection configuration"""
        return {
            "url": self.settings.redis_url,
            "decode_responses": True,
            "health_check_interval": 30,
            "retry_on_timeout": True,
            "socket_keepalive": True,
            "socket_keepalive_options": {}
        }
