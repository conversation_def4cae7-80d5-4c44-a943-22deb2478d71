"""
Agent Router for Validator Service

Handles routing of validated commands to appropriate agents
via Redis streams and manages agent health monitoring.
"""

import json
import asyncio
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog
import redis.asyncio as redis
import httpx

from .config import Settings, ModelMapConfig
from .schemas import <PERSON><PERSON><PERSON>om<PERSON>, AgentStatus, EmergencyStop

logger = structlog.get_logger()


class AgentRouter:
    """Routes validated commands to appropriate agents"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.model_config = ModelMapConfig(settings.model_map_path)
        self.redis_client: Optional[redis.Redis] = None
        self.agent_health: Dict[str, AgentStatus] = {}
        self.emergency_stopped = False
        
        # Agent endpoint mapping
        self.agent_endpoints = {
            "asr-agent": settings.asr_agent_url,
            "parser-agent": settings.parser_agent_url,
            "embed-agent": settings.embed_agent_url,
            "summary-agent": settings.summary_agent_url,
            "guardian-agent": settings.guardian_agent_url,
            "vision-agent": settings.vision_agent_url,
        }
    
    async def initialize(self):
        """Initialize Redis connection and start health monitoring"""
        self.redis_client = redis.from_url(self.settings.redis_url)
        
        # Start health monitoring task
        asyncio.create_task(self._health_monitor_loop())
        
        logger.info("Agent router initialized")
    
    def get_agent_for_tool(self, tool: str) -> Optional[str]:
        """Get the agent responsible for a specific tool"""
        return self.model_config.get_agent_for_tool(tool)
    
    def get_agent_tool_mapping(self) -> Dict[str, str]:
        """Get mapping of tools to agents"""
        return self.model_config.get_tool_mapping()
    
    async def queue_command(self, command: ValidatedCommand) -> bool:
        """
        Queue a validated command for agent processing.
        
        Args:
            command: Validated command to queue
            
        Returns:
            True if successfully queued, False otherwise
        """
        if self.emergency_stopped:
            logger.warning("Command rejected - emergency stop active", agent=command.agent)
            return False
        
        if not self.redis_client:
            logger.error("Redis client not initialized")
            return False
        
        try:
            # Check agent health
            if not await self._is_agent_healthy(command.agent):
                logger.warning("Agent unhealthy, rejecting command", agent=command.agent)
                return False
            
            # Create command message
            message = {
                "command_id": str(uuid.uuid4()),
                "agent": command.agent,
                "tool": command.tool,
                "args": command.args,
                "metadata": command.metadata,
                "timestamp": command.timestamp.isoformat(),
                "priority": self._get_command_priority(command.tool)
            }
            
            # Queue to agent-specific stream
            stream_name = f"agent:{command.agent}:commands"
            message_id = await self.redis_client.xadd(stream_name, message)
            
            # Update metrics
            await self._update_command_metrics(command.agent, "queued")
            
            logger.info(
                "Command queued successfully",
                agent=command.agent,
                tool=command.tool,
                message_id=message_id,
                command_id=message["command_id"]
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "Failed to queue command",
                agent=command.agent,
                tool=command.tool,
                error=str(e)
            )
            return False
    
    async def emergency_stop(self, reason: str) -> bool:
        """
        Emergency stop all agent processing.
        
        Args:
            reason: Reason for emergency stop
            
        Returns:
            True if successfully stopped
        """
        try:
            self.emergency_stopped = True
            
            # Create emergency stop message
            stop_message = EmergencyStop(
                reason=reason,
                affected_agents=list(self.agent_endpoints.keys()),
                initiated_by="validator-service"
            )
            
            # Broadcast to all agents
            for agent_name in self.agent_endpoints.keys():
                stream_name = f"agent:{agent_name}:control"
                await self.redis_client.xadd(stream_name, {
                    "type": "emergency_stop",
                    "reason": reason,
                    "timestamp": datetime.utcnow().isoformat()
                })
            
            # Log emergency stop
            await self.redis_client.lpush(
                "system:emergency_stops",
                stop_message.json()
            )
            
            logger.critical("Emergency stop activated", reason=reason)
            return True
            
        except Exception as e:
            logger.error("Failed to execute emergency stop", error=str(e))
            return False
    
    async def resume_operations(self) -> bool:
        """Resume operations after emergency stop"""
        try:
            self.emergency_stopped = False
            
            # Broadcast resume to all agents
            for agent_name in self.agent_endpoints.keys():
                stream_name = f"agent:{agent_name}:control"
                await self.redis_client.xadd(stream_name, {
                    "type": "resume",
                    "timestamp": datetime.utcnow().isoformat()
                })
            
            logger.info("Operations resumed")
            return True
            
        except Exception as e:
            logger.error("Failed to resume operations", error=str(e))
            return False
    
    async def get_agent_status(self, agent_name: str) -> Optional[AgentStatus]:
        """Get current status of an agent"""
        return self.agent_health.get(agent_name)
    
    async def get_all_agent_status(self) -> Dict[str, AgentStatus]:
        """Get status of all agents"""
        return self.agent_health.copy()
    
    async def _is_agent_healthy(self, agent_name: str) -> bool:
        """Check if an agent is healthy"""
        status = self.agent_health.get(agent_name)
        if not status:
            return False
        
        # Check if agent is responsive
        if status.status != "healthy":
            return False
        
        # Check if last seen is recent
        time_threshold = datetime.utcnow() - timedelta(minutes=2)
        if status.last_seen < time_threshold:
            return False
        
        return True
    
    async def _health_monitor_loop(self):
        """Background task to monitor agent health"""
        while True:
            try:
                await self._check_all_agents_health()
                await asyncio.sleep(self.settings.health_check_interval)
            except Exception as e:
                logger.error("Health monitor error", error=str(e))
                await asyncio.sleep(5)  # Short delay on error
    
    async def _check_all_agents_health(self):
        """Check health of all agents"""
        for agent_name, endpoint in self.agent_endpoints.items():
            try:
                # Skip optional agents if not configured
                if self.model_config.is_agent_optional(agent_name):
                    continue
                
                await self._check_agent_health(agent_name, endpoint)
                
            except Exception as e:
                logger.warning(
                    "Agent health check failed",
                    agent=agent_name,
                    error=str(e)
                )
                
                # Mark agent as unhealthy
                self.agent_health[agent_name] = AgentStatus(
                    agent_name=agent_name,
                    status="unhealthy",
                    last_seen=datetime.utcnow(),
                    model_id=self.model_config.get_model_id(agent_name) or "unknown"
                )
    
    async def _check_agent_health(self, agent_name: str, endpoint: str):
        """Check health of a specific agent"""
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get(f"{endpoint}/healthz")
            
            if response.status_code == 200:
                health_data = response.json()
                
                # Get queue depth from Redis
                queue_depth = await self._get_queue_depth(agent_name)
                
                # Update agent status
                self.agent_health[agent_name] = AgentStatus(
                    agent_name=agent_name,
                    status="healthy",
                    last_seen=datetime.utcnow(),
                    queue_depth=queue_depth,
                    model_id=self.model_config.get_model_id(agent_name) or "unknown"
                )
            else:
                raise Exception(f"Health check failed with status {response.status_code}")
    
    async def _get_queue_depth(self, agent_name: str) -> int:
        """Get queue depth for an agent"""
        try:
            stream_name = f"agent:{agent_name}:commands"
            info = await self.redis_client.xinfo_stream(stream_name)
            return info.get("length", 0)
        except:
            return 0
    
    def _get_command_priority(self, tool: str) -> int:
        """Get priority for a command based on tool type"""
        high_priority_tools = {"emergency_stop", "safety_check"}
        medium_priority_tools = {"insert_nodes", "extract_entities"}
        
        if tool in high_priority_tools:
            return 1  # High priority
        elif tool in medium_priority_tools:
            return 2  # Medium priority
        else:
            return 3  # Low priority
    
    async def _update_command_metrics(self, agent_name: str, action: str):
        """Update command processing metrics"""
        try:
            metric_key = f"metrics:agent:{agent_name}:{action}"
            await self.redis_client.incr(metric_key)
            await self.redis_client.expire(metric_key, 3600)  # 1 hour TTL
        except Exception as e:
            logger.warning("Failed to update metrics", error=str(e))
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get routing and agent metrics"""
        try:
            metrics = {
                "agents": {},
                "total_commands": 0,
                "emergency_stopped": self.emergency_stopped
            }
            
            for agent_name in self.agent_endpoints.keys():
                agent_metrics = {}
                
                # Get command counts
                for action in ["queued", "processed", "failed"]:
                    key = f"metrics:agent:{agent_name}:{action}"
                    count = await self.redis_client.get(key)
                    agent_metrics[action] = int(count) if count else 0
                
                metrics["agents"][agent_name] = agent_metrics
                metrics["total_commands"] += agent_metrics.get("queued", 0)
            
            return metrics
            
        except Exception as e:
            logger.error("Failed to get metrics", error=str(e))
            return {"error": str(e)}
