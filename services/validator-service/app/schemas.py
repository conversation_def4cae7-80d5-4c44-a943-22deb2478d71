"""
Pydantic schemas for the validator service.

Defines the data models for command validation, JWT envelopes,
and security validation results.
"""

import json
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
import jwt


class ToolCall(BaseModel):
    """Represents a tool call from an agent"""
    tool: str = Field(..., description="Tool name to execute")
    args: Dict[str, Any] = Field(default_factory=dict, description="Tool arguments")
    
    @validator('tool')
    def validate_tool_name(cls, v):
        """Ensure tool name is valid"""
        allowed_tools = {
            "insert_nodes",
            "extract_entities", 
            "make_summary",
            "insert_image_node",
            "analyze_medical_image",
            "answer_query",
            "generate_report",
            "extract_text"
        }
        
        if v not in allowed_tools:
            raise ValueError(f"Tool '{v}' not in allowed list: {allowed_tools}")
        return v


class CommandEnvelope(BaseModel):
    """Command envelope that can contain either JWT or native JSON"""
    command: Union[str, Dict[str, Any]] = Field(..., description="JWT token or JSON command")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    def is_jwt_format(self) -> bool:
        """Check if command is in JWT format"""
        if isinstance(self.command, str):
            try:
                # Try to decode without verification to check format
                jwt.decode(self.command, options={"verify_signature": False})
                return True
            except jwt.InvalidTokenError:
                return False
        return False


class ValidationResult(BaseModel):
    """Result of command validation"""
    valid: bool = Field(..., description="Whether command is valid")
    agent: Optional[str] = Field(None, description="Target agent for valid commands")
    command_id: Optional[str] = Field(None, description="Unique command identifier")
    error: Optional[str] = Field(None, description="Error message for invalid commands")
    violation_type: Optional[str] = Field(None, description="Type of security violation")
    queued_at: Optional[datetime] = Field(None, description="When command was queued")


class SecurityViolation(BaseModel):
    """Security violation record"""
    violation_id: str = Field(..., description="Unique violation identifier")
    violation_type: str = Field(..., description="Type of violation")
    tool: Optional[str] = Field(None, description="Tool that caused violation")
    source_ip: str = Field(..., description="Source IP address")
    user_agent: Optional[str] = Field(None, description="User agent string")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    details: Dict[str, Any] = Field(default_factory=dict)
    severity: str = Field(default="medium", description="Violation severity")


class AgentStatus(BaseModel):
    """Agent health and status information"""
    agent_name: str = Field(..., description="Agent identifier")
    status: str = Field(..., description="Agent status (healthy/unhealthy/stopped)")
    last_seen: datetime = Field(..., description="Last health check timestamp")
    queue_depth: int = Field(default=0, description="Number of pending commands")
    error_rate: float = Field(default=0.0, description="Recent error rate (0-1)")
    model_id: str = Field(..., description="Granite model being used")


class ToolSchema(BaseModel):
    """Schema definition for a tool"""
    tool_name: str = Field(..., description="Tool identifier")
    description: str = Field(..., description="Tool description")
    parameters: Dict[str, Any] = Field(..., description="JSON schema for parameters")
    required_fields: List[str] = Field(default_factory=list)
    agent: str = Field(..., description="Agent that handles this tool")
    
    
class InsertNodesArgs(BaseModel):
    """Arguments for insert_nodes tool"""
    nodes: List[Dict[str, Any]] = Field(..., description="Nodes to insert")
    patient_id: Optional[str] = Field(None, description="Patient identifier")
    timestamp: Optional[datetime] = Field(None, description="Event timestamp")
    
    @validator('nodes')
    def validate_nodes(cls, v):
        """Validate node structure"""
        for node in v:
            if 'event_type' not in node:
                raise ValueError("Each node must have an 'event_type' field")
            if 'metadata' not in node:
                raise ValueError("Each node must have a 'metadata' field")
        return v


class ExtractEntitiesArgs(BaseModel):
    """Arguments for extract_entities tool"""
    text: str = Field(..., description="Text to extract entities from")
    entity_types: Optional[List[str]] = Field(None, description="Specific entity types to extract")
    confidence_threshold: float = Field(default=0.7, description="Minimum confidence score")


class MakeSummaryArgs(BaseModel):
    """Arguments for make_summary tool"""
    patient_id: str = Field(..., description="Patient identifier")
    time_range: Optional[Dict[str, str]] = Field(None, description="Time range for summary")
    summary_type: str = Field(default="general", description="Type of summary to generate")
    max_length: int = Field(default=500, description="Maximum summary length in words")


class InsertImageNodeArgs(BaseModel):
    """Arguments for insert_image_node tool"""
    image_data: str = Field(..., description="Base64 encoded image data")
    image_type: str = Field(..., description="Image type (jpg, png, etc.)")
    patient_id: str = Field(..., description="Patient identifier")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    @validator('image_type')
    def validate_image_type(cls, v):
        """Validate image type"""
        allowed_types = {'jpg', 'jpeg', 'png', 'tiff', 'bmp'}
        if v.lower() not in allowed_types:
            raise ValueError(f"Image type must be one of: {allowed_types}")
        return v.lower()


class AnalyzeMedicalImageArgs(BaseModel):
    """Arguments for analyze_medical_image tool"""
    image_data: str = Field(..., description="Base64 encoded image data")
    analysis_type: List[str] = Field(..., description="Types of analysis to perform")
    patient_context: Optional[Dict[str, Any]] = Field(None, description="Patient context for analysis")
    
    @validator('analysis_type')
    def validate_analysis_type(cls, v):
        """Validate analysis types"""
        allowed_types = {'caption', 'ocr', 'label', 'medical_analysis', 'wound_assessment'}
        for analysis in v:
            if analysis not in allowed_types:
                raise ValueError(f"Analysis type '{analysis}' not in allowed list: {allowed_types}")
        return v


# Tool argument mapping for validation
TOOL_SCHEMAS = {
    "insert_nodes": InsertNodesArgs,
    "extract_entities": ExtractEntitiesArgs,
    "make_summary": MakeSummaryArgs,
    "insert_image_node": InsertImageNodeArgs,
    "analyze_medical_image": AnalyzeMedicalImageArgs,
}


class EmergencyStop(BaseModel):
    """Emergency stop command"""
    reason: str = Field(..., description="Reason for emergency stop")
    affected_agents: Optional[List[str]] = Field(None, description="Specific agents to stop")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    initiated_by: str = Field(..., description="Who initiated the stop")


class SystemHealth(BaseModel):
    """Overall system health status"""
    status: str = Field(..., description="Overall system status")
    agents: List[AgentStatus] = Field(..., description="Individual agent statuses")
    violations_last_hour: int = Field(default=0, description="Security violations in last hour")
    total_commands_processed: int = Field(default=0, description="Total commands processed")
    error_rate: float = Field(default=0.0, description="Overall error rate")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
