"""
Security Manager for Validator Service

Handles JWT validation, schema checking, tool allow-lists,
and security violation tracking for medical-grade safety.
"""

import jwt
import json
import hashlib
import uuid
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
import structlog
import redis.asyncio as redis
from pydantic import ValidationError

from .schemas import (
    ToolCall, SecurityViolation, TOOL_SCHEMAS,
    InsertNodesArgs, ExtractEntitiesArgs, MakeSummaryArgs,
    InsertImageNodeArgs, AnalyzeMedicalImageArgs
)
from .config import Settings

logger = structlog.get_logger()


class SecurityManager:
    """Manages security validation and violation tracking"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.jwt_secret = settings.jwt_secret
        self.jwt_algorithm = settings.jwt_algorithm
        
        # Tool allow-list for medical safety
        self.allowed_tools: Set[str] = {
            "insert_nodes",
            "extract_entities", 
            "make_summary",
            "insert_image_node",
            "analyze_medical_image",
            "answer_query",
            "generate_report",
            "extract_text"
        }
        
        # PHI detection patterns (basic implementation)
        self.phi_patterns = [
            r'\b\d{3}-\d{2}-\d{4}\b',  # SSN
            r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',  # Credit card
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # Email
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',  # Phone number
        ]
        
    def verify_jwt(self, token: str) -> Dict[str, Any]:
        """
        Verify JWT token and return payload.
        
        Args:
            token: JWT token string
            
        Returns:
            Decoded payload
            
        Raises:
            jwt.InvalidTokenError: If token is invalid
        """
        try:
            payload = jwt.decode(
                token,
                self.jwt_secret,
                algorithms=[self.jwt_algorithm],
                options={
                    "verify_exp": True,
                    "verify_iat": True,
                    "verify_signature": True
                }
            )
            
            # Validate required fields
            required_fields = {"tool", "args"}
            if not all(field in payload for field in required_fields):
                raise jwt.InvalidTokenError(f"Missing required fields: {required_fields}")
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token expired")
            raise jwt.InvalidTokenError("Token expired")
            
        except jwt.InvalidSignatureError:
            logger.warning("JWT signature invalid")
            raise jwt.InvalidTokenError("Invalid signature")
            
        except Exception as e:
            logger.error("JWT validation failed", error=str(e))
            raise jwt.InvalidTokenError(f"Token validation failed: {str(e)}")
    
    def is_tool_allowed(self, tool: str) -> bool:
        """Check if tool is in the allow-list"""
        return tool in self.allowed_tools
    
    def get_allowed_tools(self) -> List[str]:
        """Get list of allowed tools"""
        return list(self.allowed_tools)
    
    def validate_schema(self, tool_call: ToolCall) -> List[str]:
        """
        Validate tool call arguments against schema.
        
        Args:
            tool_call: Tool call to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        try:
            # Get schema for tool
            schema_class = TOOL_SCHEMAS.get(tool_call.tool)
            if not schema_class:
                errors.append(f"No schema defined for tool: {tool_call.tool}")
                return errors
            
            # Validate arguments
            schema_class(**tool_call.args)
            
            # Additional security checks
            security_errors = self._check_security_constraints(tool_call)
            errors.extend(security_errors)
            
        except ValidationError as e:
            for error in e.errors():
                field = ".".join(str(loc) for loc in error["loc"])
                errors.append(f"{field}: {error['msg']}")
                
        except Exception as e:
            errors.append(f"Validation error: {str(e)}")
        
        return errors
    
    def _check_security_constraints(self, tool_call: ToolCall) -> List[str]:
        """Check additional security constraints"""
        errors = []
        
        # Check for PHI in arguments
        phi_violations = self._detect_phi(tool_call.args)
        if phi_violations:
            errors.extend([f"PHI detected: {violation}" for violation in phi_violations])
        
        # Check argument sizes
        if self._check_argument_size(tool_call.args):
            errors.append("Arguments exceed maximum size limit")
        
        # Tool-specific security checks
        if tool_call.tool == "insert_nodes":
            node_errors = self._validate_node_security(tool_call.args)
            errors.extend(node_errors)
        
        elif tool_call.tool == "insert_image_node":
            image_errors = self._validate_image_security(tool_call.args)
            errors.extend(image_errors)
        
        return errors
    
    def _detect_phi(self, data: Any) -> List[str]:
        """Detect potential PHI in data"""
        import re
        violations = []
        
        # Convert data to string for pattern matching
        data_str = json.dumps(data) if not isinstance(data, str) else data
        
        for pattern in self.phi_patterns:
            matches = re.findall(pattern, data_str)
            if matches:
                violations.append(f"Pattern match: {pattern}")
        
        return violations
    
    def _check_argument_size(self, args: Dict[str, Any]) -> bool:
        """Check if arguments exceed size limits"""
        max_size = 1024 * 1024  # 1MB limit
        args_str = json.dumps(args)
        return len(args_str.encode('utf-8')) > max_size
    
    def _validate_node_security(self, args: Dict[str, Any]) -> List[str]:
        """Validate security for node insertion"""
        errors = []
        
        nodes = args.get("nodes", [])
        if len(nodes) > 100:  # Limit batch size
            errors.append("Too many nodes in single request (max 100)")
        
        for node in nodes:
            # Check for suspicious metadata
            metadata = node.get("metadata", {})
            if "admin" in str(metadata).lower():
                errors.append("Suspicious metadata detected")
        
        return errors
    
    def _validate_image_security(self, args: Dict[str, Any]) -> List[str]:
        """Validate security for image operations"""
        errors = []
        
        image_data = args.get("image_data", "")
        
        # Check image size (base64 encoded)
        max_image_size = 10 * 1024 * 1024  # 10MB
        if len(image_data) > max_image_size:
            errors.append("Image too large (max 10MB)")
        
        # Basic format validation
        if not image_data.startswith(('data:image/', '/9j/', 'iVBORw0KGgo')):
            errors.append("Invalid image format")
        
        return errors
    
    async def log_violation(
        self,
        violation_type: str,
        tool: Optional[str] = None,
        source_ip: str = "unknown",
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> str:
        """Log a security violation"""
        
        violation = SecurityViolation(
            violation_id=str(uuid.uuid4()),
            violation_type=violation_type,
            tool=tool,
            source_ip=source_ip,
            user_agent=user_agent,
            details=details or {},
            severity=self._get_violation_severity(violation_type)
        )
        
        # Log to structured logger
        logger.warning(
            "Security violation",
            violation_id=violation.violation_id,
            type=violation_type,
            tool=tool,
            source_ip=source_ip,
            severity=violation.severity
        )
        
        # Store in Redis for tracking
        try:
            redis_client = redis.from_url(self.settings.redis_url)
            await redis_client.lpush(
                "security:violations",
                violation.json()
            )
            # Keep only last 1000 violations
            await redis_client.ltrim("security:violations", 0, 999)
            await redis_client.close()
        except Exception as e:
            logger.error("Failed to store violation", error=str(e))
        
        return violation.violation_id
    
    def _get_violation_severity(self, violation_type: str) -> str:
        """Determine violation severity"""
        high_severity = {
            "jwt_error", "unauthorized_tool", "phi_detected",
            "injection_attempt", "admin_escalation"
        }
        
        if violation_type in high_severity:
            return "high"
        elif violation_type in {"schema_error", "size_limit"}:
            return "medium"
        else:
            return "low"
    
    async def get_violations(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """Get recent security violations"""
        try:
            redis_client = redis.from_url(self.settings.redis_url)
            violations_json = await redis_client.lrange(
                "security:violations",
                offset,
                offset + limit - 1
            )
            await redis_client.close()
            
            violations = []
            for violation_json in violations_json:
                try:
                    violation = json.loads(violation_json)
                    violations.append(violation)
                except json.JSONDecodeError:
                    continue
            
            return violations
            
        except Exception as e:
            logger.error("Failed to retrieve violations", error=str(e))
            return []
    
    def create_jwt_token(self, tool: str, args: Dict[str, Any], expires_in: int = 300) -> str:
        """
        Create a JWT token for testing purposes.
        
        Args:
            tool: Tool name
            args: Tool arguments
            expires_in: Token expiration in seconds
            
        Returns:
            JWT token string
        """
        payload = {
            "tool": tool,
            "args": args,
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(seconds=expires_in),
            "iss": "symptomos-validator"
        }
        
        return jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
