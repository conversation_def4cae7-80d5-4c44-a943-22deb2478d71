"""
Secure Validator Service - JWT Command Envelope Validator

Validates and routes agent commands with schema validation and action allow-lists.
Provides medical-grade security for the Granite agent pipeline.

Author: SymptomOS Team
Version: 0.1
"""

import json
import jwt
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from enum import Enum

from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, ValidationError
import structlog
import redis.asyncio as redis
from contextlib import asynccontextmanager

from .config import Settings
from .schemas import CommandEnvelope, ValidationResult, ToolCall
from .security import SecurityManager
from .routing import AgentRouter

logger = structlog.get_logger()

# Global state
app_state = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    settings = Settings()
    app_state["settings"] = settings
    app_state["redis"] = redis.from_url(settings.redis_url)
    app_state["security"] = SecurityManager(settings)
    app_state["router"] = AgentRouter(settings)
    
    logger.info("Validator service started", port=settings.port)
    
    yield
    
    # Shutdown
    await app_state["redis"].close()
    logger.info("Validator service stopped")

app = FastAPI(
    title="SymptomOS Validator Service",
    description="Secure command validation and routing for Granite agents",
    version="0.1.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class CommandType(str, Enum):
    """Allowed command types"""
    INSERT_NODES = "insert_nodes"
    EXTRACT_ENTITIES = "extract_entities"
    MAKE_SUMMARY = "make_summary"
    INSERT_IMAGE_NODE = "insert_image_node"
    ANALYZE_MEDICAL_IMAGE = "analyze_medical_image"


class ValidatedCommand(BaseModel):
    """Validated command ready for routing"""
    agent: str
    tool: CommandType
    args: Dict[str, Any]
    metadata: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.utcnow)


@app.post("/v1/validate", response_model=ValidationResult)
async def validate_command(
    envelope: CommandEnvelope,
    request: Request
) -> ValidationResult:
    """
    Validate a JWT command envelope and route to appropriate agent.
    
    Supports both native JSON function calling and JWT-wrapped commands.
    """
    settings = app_state["settings"]
    security = app_state["security"]
    router = app_state["router"]
    
    try:
        # Step 1: Validate JWT signature and structure
        if envelope.is_jwt_format():
            payload = security.verify_jwt(envelope.command)
            tool_call = ToolCall(**payload)
        else:
            # Native JSON function calling
            tool_call = ToolCall(**envelope.command)
        
        # Step 2: Validate tool against allow-list
        if not security.is_tool_allowed(tool_call.tool):
            logger.warning(
                "Tool not in allow-list",
                tool=tool_call.tool,
                client_ip=request.client.host
            )
            return ValidationResult(
                valid=False,
                error="Tool not allowed",
                violation_type="unauthorized_tool"
            )
        
        # Step 3: Schema validation
        validation_errors = security.validate_schema(tool_call)
        if validation_errors:
            logger.warning(
                "Schema validation failed",
                tool=tool_call.tool,
                errors=validation_errors
            )
            return ValidationResult(
                valid=False,
                error=f"Schema validation failed: {validation_errors}",
                violation_type="schema_error"
            )
        
        # Step 4: Route to appropriate agent
        target_agent = router.get_agent_for_tool(tool_call.tool)
        if not target_agent:
            return ValidationResult(
                valid=False,
                error=f"No agent configured for tool: {tool_call.tool}",
                violation_type="routing_error"
            )
        
        # Step 5: Create validated command
        validated_cmd = ValidatedCommand(
            agent=target_agent,
            tool=CommandType(tool_call.tool),
            args=tool_call.args,
            metadata={
                "source_ip": request.client.host,
                "user_agent": request.headers.get("user-agent", "unknown"),
                "request_id": request.headers.get("x-request-id", "unknown")
            }
        )
        
        # Step 6: Queue for agent processing
        await router.queue_command(validated_cmd)
        
        logger.info(
            "Command validated and queued",
            tool=tool_call.tool,
            agent=target_agent,
            request_id=validated_cmd.metadata.get("request_id")
        )
        
        return ValidationResult(
            valid=True,
            agent=target_agent,
            command_id=validated_cmd.metadata.get("request_id"),
            queued_at=validated_cmd.timestamp
        )
        
    except jwt.InvalidTokenError as e:
        logger.error("JWT validation failed", error=str(e))
        return ValidationResult(
            valid=False,
            error="Invalid JWT token",
            violation_type="jwt_error"
        )
        
    except ValidationError as e:
        logger.error("Pydantic validation failed", error=str(e))
        return ValidationResult(
            valid=False,
            error=f"Validation error: {str(e)}",
            violation_type="validation_error"
        )
        
    except Exception as e:
        logger.error("Unexpected validation error", error=str(e))
        return ValidationResult(
            valid=False,
            error="Internal validation error",
            violation_type="internal_error"
        )


@app.post("/v1/validate/batch", response_model=List[ValidationResult])
async def validate_batch(
    envelopes: List[CommandEnvelope],
    request: Request
) -> List[ValidationResult]:
    """Validate multiple commands in batch"""
    results = []
    
    for envelope in envelopes:
        result = await validate_command(envelope, request)
        results.append(result)
        
        # Stop processing on first critical failure
        if not result.valid and result.violation_type in ["jwt_error", "internal_error"]:
            break
    
    return results


@app.get("/v1/tools", response_model=List[str])
async def list_allowed_tools() -> List[str]:
    """Get list of allowed tools"""
    security = app_state["security"]
    return security.get_allowed_tools()


@app.get("/v1/agents", response_model=Dict[str, str])
async def list_agents() -> Dict[str, str]:
    """Get agent to tool mapping"""
    router = app_state["router"]
    return router.get_agent_tool_mapping()


@app.get("/v1/violations")
async def get_violations(
    limit: int = 100,
    offset: int = 0
) -> Dict[str, Any]:
    """Get recent security violations"""
    security = app_state["security"]
    violations = await security.get_violations(limit, offset)
    
    return {
        "violations": violations,
        "total": len(violations),
        "limit": limit,
        "offset": offset
    }


@app.post("/v1/emergency/stop")
async def emergency_stop(
    reason: str,
    request: Request
) -> Dict[str, str]:
    """Emergency stop all agent processing"""
    logger.critical(
        "Emergency stop triggered",
        reason=reason,
        source_ip=request.client.host
    )
    
    router = app_state["router"]
    await router.emergency_stop(reason)
    
    return {
        "status": "stopped",
        "reason": reason,
        "timestamp": datetime.utcnow().isoformat()
    }


@app.get("/healthz")
async def health_check() -> Dict[str, str]:
    """Health check endpoint"""
    try:
        # Check Redis connection
        redis_client = app_state["redis"]
        await redis_client.ping()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "0.1.0"
        }
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


if __name__ == "__main__":
    import uvicorn
    settings = Settings()
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.port,
        reload=settings.debug
    )
