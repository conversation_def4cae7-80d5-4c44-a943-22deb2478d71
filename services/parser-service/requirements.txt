# FastAPI and web framework
fastapi==0.109.0
uvicorn[standard]==0.27.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP client for external services
httpx==0.26.0
requests==2.31.0

# Natural Language Processing
spacy==3.7.2
scispacy==0.5.3
transformers==4.36.0
torch==2.1.0
numpy==1.26.0

# Medical NLP models and data
# en_core_sci_sm  # Install via: python -m spacy download en_core_sci_sm
# en_ner_bc5cdr_md  # Install via: pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.3/en_ner_bc5cdr_md-0.5.3.tar.gz

# Text processing and regex
regex==2023.12.25
nltk==3.8.1
dateparser==1.2.0

# Environment and configuration
python-dotenv==1.0.0

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Database and caching
redis==5.0.1
asyncio-mqtt==0.16.1

# Async support
aiofiles==23.2.1

# Monitoring and logging
structlog==23.2.0
prometheus-client==0.19.0

# Testing
pytest==7.4.4
pytest-asyncio==0.23.2
pytest-cov==4.1.0
pytest-xdist==3.5.0
pytest-timeout==2.2.0
hypothesis==6.88.1
hypothesis-jsonschema==0.19.1
httpx==0.26.0

# Development
black==24.1.1
ruff==0.1.9
mypy==1.8.0
