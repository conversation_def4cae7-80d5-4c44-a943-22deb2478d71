#!/usr/bin/env python3
"""
Test runner for parser-service property-based test suite.

Validates test setup and runs a quick smoke test to ensure
Hypothesis, mocks, and coverage are working correctly.
"""

import sys
import subprocess
from pathlib import Path
import json


def run_command(cmd: list, cwd: Path = None) -> tuple[int, str, str]:
    """Run a command and return (returncode, stdout, stderr)."""
    try:
        result = subprocess.run(
            cmd, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            timeout=120
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return 1, "", "Command timed out after 120 seconds"


def check_dependencies():
    """Check that required testing dependencies are available."""
    print("🔍 Checking test dependencies...")
    
    required_packages = [
        "pytest", "hypothesis", "pytest_cov", "httpx", "pydantic"
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing.append(package)
    
    if missing:
        print(f"\n❌ Missing packages: {', '.join(missing)}")
        print("Install with: pip install " + " ".join(missing))
        return False
    
    print("✅ All dependencies available")
    return True


def validate_test_structure():
    """Validate test file structure."""
    print("\n🔍 Validating test structure...")
    
    test_dir = Path(__file__).parent / "tests"
    required_files = [
        "conftest.py",
        "test_parser_property.py", 
        "test_parser_red_team.py",
        "__init__.py"
    ]
    
    missing = []
    for file in required_files:
        file_path = test_dir / file
        if file_path.exists():
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file}")
            missing.append(file)
    
    if missing:
        print(f"\n❌ Missing test files: {', '.join(missing)}")
        return False
    
    print("✅ Test structure valid")
    return True


def validate_fixtures():
    """Validate Granite mock fixtures."""
    print("\n🔍 Validating fixtures...")
    
    fixtures_dir = Path(__file__).parent.parent.parent / "tests" / "fixtures" / "granite"
    required_fixtures = [
        "asr_ok.json",
        "vision_caption.json",
        "llm_parse_ok.json"
    ]
    
    missing = []
    for fixture in required_fixtures:
        fixture_path = fixtures_dir / fixture
        if fixture_path.exists():
            try:
                with open(fixture_path) as f:
                    json.load(f)
                print(f"  ✅ {fixture}")
            except json.JSONDecodeError:
                print(f"  ❌ {fixture} (invalid JSON)")
                missing.append(fixture)
        else:
            print(f"  ❌ {fixture}")
            missing.append(fixture)
    
    if missing:
        print(f"\n❌ Missing/invalid fixtures: {', '.join(missing)}")
        return False
    
    print("✅ Fixtures valid")
    return True


def run_smoke_test():
    """Run a quick smoke test to verify setup."""
    print("\n🔍 Running smoke test...")
    
    test_dir = Path(__file__).parent
    
    # Run a single property test with minimal examples
    cmd = [
        sys.executable, "-m", "pytest", 
        "tests/test_parser_property.py::TestParseTextEndpoint::test_parse_text_valid_requests",
        "-v", "--tb=short", 
        "--hypothesis-max-examples=5",
        "--no-cov"  # Skip coverage for smoke test
    ]
    
    returncode, stdout, stderr = run_command(cmd, test_dir)
    
    if returncode == 0:
        print("✅ Smoke test passed")
        return True
    else:
        print("❌ Smoke test failed")
        print("STDOUT:", stdout)
        print("STDERR:", stderr)
        return False


def run_security_test():
    """Run a quick security test."""
    print("\n🔍 Running security test sample...")
    
    test_dir = Path(__file__).parent
    
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/test_parser_red_team.py::TestRedTeamPrompts::test_malicious_prompts_blocked_or_sanitized",
        "-v", "--tb=short", "-k", "SQL",
        "--no-cov"
    ]
    
    returncode, stdout, stderr = run_command(cmd, test_dir)
    
    if returncode == 0:
        print("✅ Security test sample passed")
        return True
    else:
        print("❌ Security test sample failed")
        print("STDOUT:", stdout)
        print("STDERR:", stderr)
        return False


def main():
    """Main test validation routine."""
    print("🧪 Parser Service Test Suite Validation")
    print("=" * 50)
    
    all_passed = True
    
    # Check dependencies
    if not check_dependencies():
        all_passed = False
    
    # Validate structure
    if not validate_test_structure():
        all_passed = False
    
    # Validate fixtures
    if not validate_fixtures():
        all_passed = False
    
    # Run smoke tests
    if all_passed:
        if not run_smoke_test():
            all_passed = False
        
        if not run_security_test():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All validations passed!")
        print("\nTo run the full test suite:")
        print("  pytest -v --cov=app --cov-fail-under=90")
        print("\nTo run only property tests:")
        print("  pytest tests/test_parser_property.py -v")
        print("\nTo run only security tests:")
        print("  pytest tests/test_parser_red_team.py -v")
        return 0
    else:
        print("❌ Some validations failed!")
        print("Please fix the issues above before running the full test suite.")
        return 1


if __name__ == "__main__":
    sys.exit(main())