[tool:pytest]
# Pytest configuration for parser-service

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Async support
asyncio_mode = auto

# Output formatting
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=90
    --durations=10
    --maxfail=5

# Markers for test categorization
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    property: marks tests as property-based tests
    security: marks tests as security/red-team tests
    performance: marks tests as performance tests
    
# Coverage settings
[coverage:run]
source = app
omit = 
    */tests/*
    */venv/*
    */env/*
    */__pycache__/*
    */conftest.py
    */test_*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov

# Hypothesis settings
[hypothesis]
max_examples = 200
deadline = None
derandomize = true
verbosity = normal