"""
Tests for NLP Engine
"""

import pytest
from app.nlp_engine import NLPEngine, MedicalEntity
from app.config import Settings


@pytest.fixture
def settings():
    """Create test settings."""
    return Settings(
        secret_key="test-secret-key",
        spacy_model="en_core_web_sm",  # Use basic model for testing
        medical_ner_model="en_core_web_sm",
        enable_transformers=False,  # Disable for faster testing
        confidence_threshold=0.5
    )


@pytest.fixture
async def nlp_engine(settings):
    """Create and initialize NLP engine for testing."""
    engine = NLPEngine(settings)
    try:
        await engine.initialize()
        yield engine
    finally:
        await engine.cleanup()


@pytest.mark.asyncio
async def test_extract_basic_entities(nlp_engine):
    """Test basic entity extraction."""
    text = "I have a headache and took ibuprofen 400mg."
    entities = await nlp_engine.extract_entities(text)
    
    assert len(entities) > 0
    entity_types = [e.entity_type for e in entities]
    
    # Should find at least some medical entities
    assert any(entity_type in ["symptom", "medication", "dosage"] for entity_type in entity_types)


@pytest.mark.asyncio
async def test_extract_symptoms(nlp_engine):
    """Test symptom extraction."""
    text = "I have a severe headache rated 8 out of 10."
    entities = await nlp_engine.extract_entities(text)
    
    symptom_entities = [e for e in entities if e.entity_type == "symptom"]
    assert len(symptom_entities) > 0
    
    # Check that we found "headache"
    symptom_texts = [e.text.lower() for e in symptom_entities]
    assert any("headache" in text for text in symptom_texts)


@pytest.mark.asyncio
async def test_extract_medications(nlp_engine):
    """Test medication extraction."""
    text = "I took ibuprofen and aspirin for my pain."
    entities = await nlp_engine.extract_entities(text)
    
    medication_entities = [e for e in entities if e.entity_type == "medication"]
    assert len(medication_entities) > 0
    
    # Check that we found medications
    med_texts = [e.text.lower() for e in medication_entities]
    assert any("ibuprofen" in text or "aspirin" in text for text in med_texts)


@pytest.mark.asyncio
async def test_confidence_filtering(nlp_engine):
    """Test confidence threshold filtering."""
    text = "I have a headache."
    
    # Get all entities
    all_entities = await nlp_engine.extract_entities(text)
    
    # Filter by high confidence
    high_conf_entities = [e for e in all_entities if e.confidence >= 0.8]
    
    # Should have fewer high confidence entities
    assert len(high_conf_entities) <= len(all_entities)


@pytest.mark.asyncio
async def test_empty_text(nlp_engine):
    """Test handling of empty text."""
    entities = await nlp_engine.extract_entities("")
    assert len(entities) == 0


@pytest.mark.asyncio
async def test_long_text_truncation(nlp_engine):
    """Test handling of very long text."""
    # Create text longer than max_text_length
    long_text = "I have a headache. " * 1000
    
    # Should not raise an error
    entities = await nlp_engine.extract_entities(long_text)
    assert isinstance(entities, list)
