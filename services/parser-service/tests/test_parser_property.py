"""
Property-based tests for parser-service endpoints using Hypothesis.

Tests all POST endpoints to ensure they:
1. Return valid JSON matching OpenAPI schema
2. Never raise unhandled exceptions
3. Return status="partial" instead of 500 on malformed input
"""

import json
import pytest
import sys
import os
from typing import Dict, Any, List, Optional

# Handle missing dependencies gracefully for CI
try:
    import httpx
    from hypothesis import given, settings, strategies as st, Verbosity, Phase
    from hypothesis.strategies import composite
    from pydantic import ValidationError
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Missing dependencies for property tests: {e}")
    DEPENDENCIES_AVAILABLE = False
    # Create dummy decorators
    def given(strategy): return lambda f: f
    def settings(**kwargs): return lambda f: f
    class st:
        @staticmethod
        def text(**kwargs): return "dummy"
        @staticmethod  
        def booleans(): return False
        @staticmethod
        def one_of(*args): return None
        @staticmethod
        def none(): return None
        @staticmethod
        def floats(**kwargs): return 0.0
        @staticmethod
        def lists(*args, **kwargs): return []
        @staticmethod
        def sampled_from(seq): return seq[0] if seq else None
        @staticmethod
        def just(val): return val
        @staticmethod
        def characters(**kwargs): return "a"
    def composite(f): return f

# Handle app imports gracefully
try:
    from app.routers.parse import (
        ParseTextRequest, ParseTextResponse, ParseVoiceRequest, ParseVoiceResponse
    )
    from app.routers.extract import (
        ExtractEntitiesRequest, ExtractSymptomsRequest, ExtractMedicationsRequest,
        EntityExtractionResponse, SymptomExtractionResponse, MedicationExtractionResponse
    )
    APP_AVAILABLE = True
except ImportError as e:
    print(f"Warning: App modules not available: {e}")
    APP_AVAILABLE = False
    # Create dummy classes
    class ParseTextRequest:
        def __init__(self, **kwargs): pass
        def model_dump(self): return {}
    class ParseTextResponse: pass
    class ExtractEntitiesRequest: 
        def __init__(self, **kwargs): pass
        def model_dump(self): return {}
    class ExtractSymptomsRequest:
        def __init__(self, **kwargs): pass  
        def model_dump(self): return {}
    class ExtractMedicationsRequest:
        def __init__(self, **kwargs): pass
        def model_dump(self): return {}
    class EntityExtractionResponse: pass
    class SymptomExtractionResponse: pass
    class MedicationExtractionResponse: pass


# Test configuration for CI compatibility
if DEPENDENCIES_AVAILABLE:
    TEST_SETTINGS = settings(
        max_examples=200,
        deadline=None,
        verbosity=Verbosity.normal,
        phases=[Phase.generate, Phase.shrink],
        suppress_health_check=[],
        derandomize=True  # Deterministic for CI
    )
else:
    def TEST_SETTINGS(f): return f

# Skip decorator for when dependencies/app not available
skip_if_no_deps = pytest.mark.skipif(
    not DEPENDENCIES_AVAILABLE, 
    reason="Missing hypothesis/httpx dependencies"
)
skip_if_no_app = pytest.mark.skipif(
    not APP_AVAILABLE,
    reason="App modules not available"
)


def test_parser_property_structure():
    """Basic test to ensure property test structure is valid."""
    assert True, "Property test structure validated"

def test_parser_dependencies():
    """Test that property test dependencies are available or gracefully handled."""
    if DEPENDENCIES_AVAILABLE and APP_AVAILABLE:
        assert True, "All dependencies available for full property testing"
    else:
        pytest.skip("Dependencies not available - tests will be skipped")

@composite
def medical_text_strategy(draw):
    """Generate realistic medical text inputs."""
    base_texts = [
        "I have a headache",
        "My chest hurts",
        "I took ibuprofen 400mg",
        "Severe back pain rated 8/10",
        "Started feeling nauseous yesterday",
        "Took aspirin twice daily for my arthritis",
        "Sudden onset chest pain",
        "Mild fever for 2 days",
        "Sharp pain in my left knee",
        "Difficulty breathing since morning"
    ]
    
    # Choose base or generate random
    if draw(st.booleans()):
        text = draw(st.sampled_from(base_texts))
    else:
        text = draw(st.text(min_size=1, max_size=200, alphabet=st.characters(
            whitelist_categories=('Lu', 'Ll', 'Nd', 'Pc', 'Pd', 'Ps', 'Pe', 'Po'), 
            whitelist_characters=' .,!?;:()'
        )))
    
    # Add some medical terms occasionally
    medical_terms = ["pain", "medication", "symptom", "mg", "daily", "severe", "mild"]
    if draw(st.booleans()):
        term = draw(st.sampled_from(medical_terms))
        text = f"{text} {term}"
    
    return text[:10000]  # Respect max length


@composite
def parse_text_request_strategy(draw):
    """Generate ParseTextRequest objects."""
    return ParseTextRequest(
        text=draw(medical_text_strategy()),
        patient_id=draw(st.one_of(st.none(), st.text(min_size=1, max_size=50))),
        extract_symptoms=draw(st.booleans()),
        extract_medications=draw(st.booleans()),
        extract_temporal=draw(st.booleans()),
        extract_severity=draw(st.booleans()),
        confidence_threshold=draw(st.one_of(
            st.none(), 
            st.floats(min_value=0.0, max_value=1.0, allow_nan=False)
        ))
    )


@composite
def extract_entities_request_strategy(draw):
    """Generate ExtractEntitiesRequest objects."""
    entity_types = ["symptom", "medication", "dosage", "temporal", "severity"]
    return ExtractEntitiesRequest(
        text=draw(medical_text_strategy()),
        entity_types=draw(st.one_of(
            st.none(),
            st.lists(st.sampled_from(entity_types), min_size=1, max_size=3)
        )),
        confidence_threshold=draw(st.one_of(
            st.none(),
            st.floats(min_value=0.0, max_value=1.0, allow_nan=False)
        ))
    )


@composite
def extract_symptoms_request_strategy(draw):
    """Generate ExtractSymptomsRequest objects."""
    return ExtractSymptomsRequest(
        text=draw(medical_text_strategy()),
        include_severity=draw(st.booleans()),
        include_location=draw(st.booleans()),
        confidence_threshold=draw(st.one_of(
            st.none(),
            st.floats(min_value=0.0, max_value=1.0, allow_nan=False)
        ))
    )


@composite
def extract_medications_request_strategy(draw):
    """Generate ExtractMedicationsRequest objects."""
    return ExtractMedicationsRequest(
        text=draw(medical_text_strategy()),
        include_dosage=draw(st.booleans()),
        include_frequency=draw(st.booleans()),
        confidence_threshold=draw(st.one_of(
            st.none(),
            st.floats(min_value=0.0, max_value=1.0, allow_nan=False)
        ))
    )


@composite
def malformed_text_strategy(draw):
    """Generate malformed/edge case text inputs."""
    return draw(st.one_of(
        st.just(""),  # Empty string
        st.text(min_size=10001, max_size=15000),  # Too long
        st.text(alphabet=st.characters(blacklist_categories=('Cc', 'Cs'))),  # Special chars
        st.just("\x00\x01\x02"),  # Control characters
        st.just("∀∃∄∅∆∇∈∉∊∋∌∍∎∏"),  # Unicode symbols
        st.just("🔥💊🏥👨‍⚕️🩺"),  # Emojis
        st.just("A" * 50000),  # Very long string
    ))


@skip_if_no_deps
@skip_if_no_app
class TestParseTextEndpoint:
    """Property tests for /v1/parse/text endpoint."""
    
    @given(parse_text_request_strategy())
    @TEST_SETTINGS
    @pytest.mark.asyncio
    async def test_parse_text_valid_requests(self, client: httpx.AsyncClient, request_data: ParseTextRequest):
        """Valid requests should return proper JSON response."""
        response = await client.post(
            "/v1/parse/text",
            json=request_data.model_dump(),
            headers={"Content-Type": "application/json"}
        )
        
        # Should not raise unhandled exceptions
        assert response.status_code in [200, 400, 422, 500]
        
        # Response should be valid JSON
        try:
            json_data = response.json()
        except json.JSONDecodeError:
            pytest.fail("Response is not valid JSON")
        
        # On success, should match schema
        if response.status_code == 200:
            try:
                ParseTextResponse.model_validate(json_data)
            except ValidationError as e:
                pytest.fail(f"Response doesn't match schema: {e}")
            
            # Basic sanity checks
            assert json_data["success"] is True
            assert isinstance(json_data["entities"], list)
            assert json_data["entity_count"] >= 0
            assert json_data["processing_time_ms"] >= 0
            assert json_data["text_length"] == len(request_data.text)
    
    @given(malformed_text_strategy())
    @TEST_SETTINGS
    @pytest.mark.asyncio 
    async def test_parse_text_malformed_input_graceful_handling(self, client: httpx.AsyncClient, malformed_text: str):
        """Malformed input should return partial status, not 500 errors."""
        request_data = ParseTextRequest(text=malformed_text)
        
        response = await client.post(
            "/v1/parse/text",
            json=request_data.model_dump(),
            headers={"Content-Type": "application/json"}
        )
        
        # Should never return 500 for malformed input
        assert response.status_code != 500
        
        # Should be valid JSON
        json_data = response.json()
        
        # If processing failed gracefully, should indicate partial status
        if response.status_code == 200:
            # Should either succeed or mark as partial
            if not json_data.get("success", True):
                assert "partial" in json_data.get("status", "").lower()


@skip_if_no_deps
@skip_if_no_app
class TestExtractEntitiesEndpoint:
    """Property tests for /v1/extract/entities endpoint."""
    
    @given(extract_entities_request_strategy())
    @TEST_SETTINGS
    @pytest.mark.asyncio
    async def test_extract_entities_valid_requests(self, client: httpx.AsyncClient, request_data: ExtractEntitiesRequest):
        """Valid entity extraction requests should return proper responses."""
        response = await client.post(
            "/v1/extract/entities",
            json=request_data.model_dump(),
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code in [200, 400, 422, 500]
        json_data = response.json()
        
        if response.status_code == 200:
            try:
                EntityExtractionResponse.model_validate(json_data)
            except ValidationError as e:
                pytest.fail(f"Response doesn't match schema: {e}")
            
            assert json_data["success"] is True
            assert isinstance(json_data["entities"], list)
            assert json_data["entity_count"] >= 0
            assert json_data["processing_time_ms"] >= 0
    
    @given(malformed_text_strategy())
    @TEST_SETTINGS  
    @pytest.mark.asyncio
    async def test_extract_entities_malformed_graceful(self, client: httpx.AsyncClient, malformed_text: str):
        """Malformed input should be handled gracefully."""
        request_data = ExtractEntitiesRequest(text=malformed_text)
        
        response = await client.post(
            "/v1/extract/entities", 
            json=request_data.model_dump(),
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code != 500
        json_data = response.json()


@skip_if_no_deps
@skip_if_no_app
class TestExtractSymptomsEndpoint:
    """Property tests for /v1/extract/symptoms endpoint."""
    
    @given(extract_symptoms_request_strategy())
    @TEST_SETTINGS
    @pytest.mark.asyncio
    async def test_extract_symptoms_valid_requests(self, client: httpx.AsyncClient, request_data: ExtractSymptomsRequest):
        """Valid symptom extraction requests should return proper responses."""
        response = await client.post(
            "/v1/extract/symptoms",
            json=request_data.model_dump(),
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code in [200, 400, 422, 500]
        json_data = response.json()
        
        if response.status_code == 200:
            try:
                SymptomExtractionResponse.model_validate(json_data)
            except ValidationError as e:
                pytest.fail(f"Response doesn't match schema: {e}")
            
            assert json_data["success"] is True
            assert isinstance(json_data["symptoms"], list)
            assert json_data["symptom_count"] >= 0
            
            # Validate symptom entities structure
            for symptom in json_data["symptoms"]:
                assert "text" in symptom
                assert "confidence" in symptom
                assert 0.0 <= symptom["confidence"] <= 1.0


@skip_if_no_deps
@skip_if_no_app
class TestExtractMedicationsEndpoint:
    """Property tests for /v1/extract/medications endpoint."""
    
    @given(extract_medications_request_strategy())
    @TEST_SETTINGS
    @pytest.mark.asyncio
    async def test_extract_medications_valid_requests(self, client: httpx.AsyncClient, request_data: ExtractMedicationsRequest):
        """Valid medication extraction requests should return proper responses.""" 
        response = await client.post(
            "/v1/extract/medications",
            json=request_data.model_dump(),
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code in [200, 400, 422, 500]
        json_data = response.json()
        
        if response.status_code == 200:
            try:
                MedicationExtractionResponse.model_validate(json_data)
            except ValidationError as e:
                pytest.fail(f"Response doesn't match schema: {e}")
            
            assert json_data["success"] is True
            assert isinstance(json_data["medications"], list)
            assert json_data["medication_count"] >= 0
            
            # Validate medication entities structure
            for medication in json_data["medications"]:
                assert "text" in medication
                assert "confidence" in medication
                assert 0.0 <= medication["confidence"] <= 1.0


@skip_if_no_deps
@skip_if_no_app
class TestBatchTextEndpoint:
    """Property tests for /v1/parse/batch-text endpoint."""
    
    @given(st.lists(medical_text_strategy(), min_size=1, max_size=10))
    @TEST_SETTINGS
    @pytest.mark.asyncio
    async def test_batch_text_valid_requests(self, client: httpx.AsyncClient, text_list: List[str]):
        """Valid batch text requests should return proper responses."""
        request_data = {
            "texts": text_list,
            "extract_symptoms": True,
            "extract_medications": True
        }
        
        response = await client.post(
            "/v1/parse/batch-text",
            json=request_data,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code in [200, 400, 422, 500]
        json_data = response.json()
        
        if response.status_code == 200:
            assert json_data["success"] is True
            assert "results" in json_data
            assert json_data["total_count"] == len(text_list)
            assert json_data["successful_count"] >= 0
            assert json_data["failed_count"] >= 0
            assert json_data["successful_count"] + json_data["failed_count"] == len(text_list)
    
    @given(st.lists(st.text(max_size=50), min_size=51, max_size=100))  # Too many texts
    @TEST_SETTINGS
    @pytest.mark.asyncio
    async def test_batch_text_size_limit(self, client: httpx.AsyncClient, large_text_list: List[str]):
        """Batch requests with too many texts should be rejected gracefully."""
        request_data = {
            "texts": large_text_list,
            "extract_symptoms": True
        }
        
        response = await client.post(
            "/v1/parse/batch-text",
            json=request_data,
            headers={"Content-Type": "application/json"}
        )
        
        # Should reject with 400, not crash with 500
        assert response.status_code == 400
        json_data = response.json()
        assert "too many" in json_data.get("detail", "").lower() or "maximum" in json_data.get("detail", "").lower()


@skip_if_no_deps
@skip_if_no_app
class TestCrossEndpointConsistency:
    """Tests that verify consistency across different endpoints."""
    
    @given(medical_text_strategy())
    @TEST_SETTINGS
    @pytest.mark.asyncio
    async def test_parse_vs_extract_consistency(self, client: httpx.AsyncClient, text: str):
        """Parse and extract endpoints should return consistent entity counts for the same text."""
        # Test parse/text
        parse_request = ParseTextRequest(text=text)
        parse_response = await client.post("/v1/parse/text", json=parse_request.model_dump())
        
        # Test extract/entities
        extract_request = ExtractEntitiesRequest(text=text)
        extract_response = await client.post("/v1/extract/entities", json=extract_request.model_dump())
        
        # Both should succeed or both should fail in similar ways
        if parse_response.status_code == 200 and extract_response.status_code == 200:
            parse_data = parse_response.json()
            extract_data = extract_response.json()
            
            # Entity counts should be reasonably close (accounting for different filtering)
            parse_count = parse_data["entity_count"]
            extract_count = extract_data["entity_count"]
            
            # Should not differ by more than a factor of 3 (allowing for different defaults)
            if max(parse_count, extract_count) > 0:
                ratio = max(parse_count, extract_count) / max(min(parse_count, extract_count), 1)
                assert ratio <= 3, f"Entity counts too different: parse={parse_count}, extract={extract_count}"


@skip_if_no_deps
@skip_if_no_app
class TestErrorHandling:
    """Tests for robust error handling."""
    
    @TEST_SETTINGS
    @pytest.mark.asyncio
    async def test_invalid_json_handling(self, client: httpx.AsyncClient):
        """Invalid JSON should return proper error responses."""
        response = await client.post(
            "/v1/parse/text",
            content="invalid json{",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422  # Unprocessable Entity
        json_data = response.json()
        assert "detail" in json_data
    
    @TEST_SETTINGS
    @pytest.mark.asyncio
    async def test_missing_required_fields(self, client: httpx.AsyncClient):
        """Missing required fields should return validation errors."""
        response = await client.post(
            "/v1/parse/text",
            json={},  # Missing required 'text' field
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422
        json_data = response.json()
        assert "detail" in json_data
    
    @given(st.floats(min_value=1.1, max_value=10.0))  # Invalid confidence values
    @TEST_SETTINGS
    @pytest.mark.asyncio
    async def test_invalid_confidence_threshold(self, client: httpx.AsyncClient, invalid_confidence: float):
        """Invalid confidence thresholds should be rejected."""
        request_data = ParseTextRequest(
            text="test text",
            confidence_threshold=invalid_confidence
        )
        
        response = await client.post(
            "/v1/parse/text",
            json=request_data.model_dump(),
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422  # Validation error