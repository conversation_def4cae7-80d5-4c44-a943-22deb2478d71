"""
Medical Patterns for Entity Extraction

Defines regex patterns for extracting medical entities from natural language text.
"""

import re
from typing import Dict


class MedicalPatterns:
    """Collection of regex patterns for medical entity extraction."""
    
    def __init__(self):
        self.symptom_patterns = self._build_symptom_patterns()
        self.medication_patterns = self._build_medication_patterns()
        self.dosage_patterns = self._build_dosage_patterns()
        self.temporal_patterns = self._build_temporal_patterns()
        self.severity_patterns = self._build_severity_patterns()
    
    def _build_symptom_patterns(self) -> Dict[str, str]:
        """Build regex patterns for symptom extraction."""
        
        # Common symptoms
        symptoms = [
            "headache", "migraine", "head pain",
            "fever", "temperature", "hot", "chills",
            "nausea", "nauseous", "sick to stomach",
            "vomiting", "throwing up", "vomit",
            "diarrhea", "loose stools",
            "constipation", "blocked up",
            "fatigue", "tired", "exhausted", "weakness",
            "dizziness", "dizzy", "lightheaded",
            "shortness of breath", "breathing difficulty", "breathless",
            "chest pain", "chest tightness", "chest pressure",
            "abdominal pain", "stomach pain", "belly pain",
            "back pain", "backache",
            "joint pain", "arthritis", "stiff joints",
            "muscle pain", "muscle ache", "sore muscles",
            "sore throat", "throat pain",
            "cough", "coughing", "hacking",
            "runny nose", "stuffy nose", "congestion",
            "rash", "skin irritation", "itchy skin",
            "swelling", "swollen", "inflammation",
            "pain", "ache", "hurt", "hurting", "sore",
            "burning", "stinging", "throbbing", "sharp pain",
            "anxiety", "worried", "nervous", "stressed",
            "depression", "sad", "down", "low mood",
            "insomnia", "can't sleep", "sleepless",
            "confusion", "confused", "disoriented"
        ]
        
        patterns = {}
        
        # Basic symptom pattern
        symptom_list = "|".join(symptoms)
        patterns["basic_symptoms"] = rf"\b(?:{symptom_list})\b"
        
        # Symptom with intensity
        patterns["symptom_with_intensity"] = rf"\b(?:severe|mild|moderate|intense|slight|bad|terrible|awful)\s+(?:{symptom_list})\b"
        
        # "I have" patterns
        patterns["i_have_symptom"] = rf"\bI\s+(?:have|am\s+having|experience|experiencing|feel|feeling)\s+(?:a\s+|an\s+|some\s+)?(?:{symptom_list})\b"
        
        # "Feeling" patterns
        patterns["feeling_symptom"] = rf"\bfeeling\s+(?:{symptom_list})\b"
        
        # Location-specific symptoms
        patterns["body_part_pain"] = r"\b(?:my\s+)?(?:head|chest|stomach|back|neck|shoulder|knee|ankle|wrist|elbow)\s+(?:hurts|aches|is\s+sore|pains?)\b"
        
        return patterns
    
    def _build_medication_patterns(self) -> Dict[str, str]:
        """Build regex patterns for medication extraction."""
        
        # Common medications
        medications = [
            "ibuprofen", "advil", "motrin",
            "acetaminophen", "tylenol", "paracetamol",
            "aspirin", "bayer",
            "naproxen", "aleve",
            "prednisone", "prednisolone",
            "amoxicillin", "penicillin",
            "lisinopril", "enalapril",
            "metformin", "glucophage",
            "atorvastatin", "lipitor",
            "omeprazole", "prilosec",
            "sertraline", "zoloft",
            "fluoxetine", "prozac",
            "lorazepam", "ativan",
            "alprazolam", "xanax",
            "hydrocodone", "vicodin",
            "oxycodone", "oxycontin",
            "tramadol", "ultram",
            "gabapentin", "neurontin",
            "warfarin", "coumadin",
            "insulin", "metoprolol",
            "amlodipine", "norvasc",
            "levothyroxine", "synthroid"
        ]
        
        patterns = {}
        
        # Basic medication pattern
        med_list = "|".join(medications)
        patterns["basic_medications"] = rf"\b(?:{med_list})\b"
        
        # "Taking" patterns
        patterns["taking_medication"] = rf"\b(?:taking|took|take|on|using|prescribed)\s+(?:{med_list})\b"
        
        # Generic drug patterns
        patterns["generic_drug"] = r"\b\w+(?:cillin|mycin|prazole|statin|pril|sartan|olol|pine|zole|ide)\b"
        
        # Medication with strength
        patterns["medication_with_strength"] = rf"\b(?:{med_list})\s+\d+\s*(?:mg|mcg|g|ml|units?)\b"
        
        return patterns
    
    def _build_dosage_patterns(self) -> Dict[str, str]:
        """Build regex patterns for dosage extraction."""
        
        patterns = {}
        
        # Basic dosage amounts
        patterns["dosage_amount"] = r"\b\d+(?:\.\d+)?\s*(?:mg|mcg|g|ml|cc|units?|tablets?|pills?|capsules?)\b"
        
        # Frequency patterns
        patterns["frequency"] = r"\b(?:once|twice|three times?|four times?|\d+\s*times?)\s+(?:a\s+|per\s+)?(?:day|daily|week|weekly|month|monthly|hour|hourly)\b"
        
        # "Every" patterns
        patterns["every_frequency"] = r"\bevery\s+(?:\d+\s+)?(?:hours?|days?|weeks?|months?|morning|evening|night)\b"
        
        # "As needed" patterns
        patterns["as_needed"] = r"\b(?:as\s+needed|prn|when\s+needed|if\s+needed)\b"
        
        # Route of administration
        patterns["route"] = r"\b(?:by\s+mouth|orally|oral|injection|IV|intravenous|topical|sublingual)\b"
        
        return patterns
    
    def _build_temporal_patterns(self) -> Dict[str, str]:
        """Build regex patterns for temporal information extraction."""
        
        patterns = {}
        
        # Relative time
        patterns["relative_time"] = r"\b(?:today|yesterday|tomorrow|this\s+morning|this\s+afternoon|this\s+evening|tonight|last\s+night)\b"
        
        # Time ago
        patterns["time_ago"] = r"\b(?:\d+\s+)?(?:minutes?|hours?|days?|weeks?|months?|years?)\s+ago\b"
        
        # Since/for duration
        patterns["duration"] = r"\b(?:since|for)\s+(?:the\s+)?(?:past\s+)?(?:\d+\s+)?(?:minutes?|hours?|days?|weeks?|months?|years?)\b"
        
        # Specific times
        patterns["specific_time"] = r"\b(?:at\s+)?\d{1,2}(?::\d{2})?\s*(?:am|pm|AM|PM)\b"
        
        # Days of week
        patterns["day_of_week"] = r"\b(?:monday|tuesday|wednesday|thursday|friday|saturday|sunday|mon|tue|wed|thu|fri|sat|sun)\b"
        
        # Dates
        patterns["date"] = r"\b(?:\d{1,2}[/-]\d{1,2}[/-]\d{2,4}|\d{1,2}\s+(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\w*\s+\d{2,4})\b"
        
        return patterns
    
    def _build_severity_patterns(self) -> Dict[str, str]:
        """Build regex patterns for severity indicators."""
        
        patterns = {}
        
        # Numeric scales
        patterns["numeric_scale"] = r"\b(?:\d+\s*(?:out\s+of\s+|/)\s*10|\d+\s*(?:out\s+of\s+|/)\s*5|scale\s+of\s+\d+|level\s+\d+)\b"
        
        # Severity descriptors
        patterns["severity_words"] = r"\b(?:severe|mild|moderate|intense|slight|bad|terrible|awful|excruciating|unbearable|manageable|tolerable)\b"
        
        # Pain scales
        patterns["pain_scale"] = r"\b(?:no\s+pain|mild\s+pain|moderate\s+pain|severe\s+pain|worst\s+pain)\b"
        
        # Intensity modifiers
        patterns["intensity"] = r"\b(?:very|extremely|really|quite|somewhat|a\s+little|a\s+bit)\s+(?:painful|sore|bad|uncomfortable)\b"
        
        # Comparative severity
        patterns["comparative"] = r"\b(?:worse|better|same|similar|different)\s+(?:than|as)\s+(?:yesterday|before|usual|normal)\b"
        
        return patterns
    
    def get_all_patterns(self) -> Dict[str, Dict[str, str]]:
        """Get all pattern categories."""
        return {
            "symptoms": self.symptom_patterns,
            "medications": self.medication_patterns,
            "dosages": self.dosage_patterns,
            "temporal": self.temporal_patterns,
            "severity": self.severity_patterns
        }
