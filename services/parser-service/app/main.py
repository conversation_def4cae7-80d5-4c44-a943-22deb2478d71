"""
Parser Service - Medical Entity Extraction

This service extracts structured medical information from natural language text,
including symptoms, medications, dosages, and temporal information.
"""

import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import structlog

from .config import get_settings
from .routers import parse, extract, health
from .middleware import LoggingMiddleware, MetricsMiddleware
from .auth import get_current_user
from .nlp_engine import NLPEngine

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    settings = get_settings()
    
    # Startup
    logger.info("Starting Parser Service", version="0.1.0")
    
    # Initialize NLP engine
    try:
        nlp_engine = NLPEngine(settings)
        await nlp_engine.initialize()
        app.state.nlp_engine = nlp_engine
        logger.info("NLP engine initialized successfully")
    except Exception as e:
        logger.error("Failed to initialize NLP engine", error=str(e))
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Parser Service")
    if hasattr(app.state, 'nlp_engine'):
        await app.state.nlp_engine.cleanup()


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title="SymptomOS Parser Service",
        description="Medical entity extraction and natural language processing service",
        version="0.1.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.allowed_hosts
    )
    
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(MetricsMiddleware)
    
    # Include routers
    app.include_router(health.router, prefix="/healthz", tags=["health"])
    app.include_router(
        parse.router, 
        prefix="/v1/parse", 
        tags=["parsing"],
        dependencies=[Depends(get_current_user)] if not settings.debug else []
    )
    app.include_router(
        extract.router, 
        prefix="/v1/extract", 
        tags=["extraction"],
        dependencies=[Depends(get_current_user)] if not settings.debug else []
    )
    
    # Global exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        logger.error(
            "Unhandled exception",
            path=request.url.path,
            method=request.method,
            error=str(exc),
            exc_info=True
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Internal server error"}
        )
    
    # Root endpoint
    @app.get("/")
    async def root():
        return {
            "service": "SymptomOS Parser Service",
            "version": "0.1.0",
            "status": "healthy",
            "endpoints": {
                "health": "/healthz",
                "parse_text": "/v1/parse/text",
                "parse_voice": "/v1/parse/voice",
                "extract_entities": "/v1/extract/entities",
                "extract_symptoms": "/v1/extract/symptoms",
                "extract_medications": "/v1/extract/medications",
                "docs": "/docs" if settings.debug else "disabled"
            }
        }
    
    return app


# Create the app instance
app = create_app()

if __name__ == "__main__":
    import uvicorn
    settings = get_settings()
    
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_config=None,  # Use structlog instead
    )
