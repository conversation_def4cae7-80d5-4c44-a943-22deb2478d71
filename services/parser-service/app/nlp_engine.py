"""
NLP Engine for Medical Entity Extraction

Handles the core natural language processing for extracting medical entities
from text using SpaCy, SciSpaCy, and transformer models.
"""

import re
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import spacy
import structlog
from dateparser import parse as parse_date

from .config import Settings
from .medical_patterns import MedicalPatterns

logger = structlog.get_logger()


class MedicalEntity:
    """Represents a medical entity extracted from text."""
    
    def __init__(
        self,
        text: str,
        entity_type: str,
        start: int,
        end: int,
        confidence: float,
        attributes: Optional[Dict[str, Any]] = None
    ):
        self.text = text
        self.entity_type = entity_type
        self.start = start
        self.end = end
        self.confidence = confidence
        self.attributes = attributes or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "text": self.text,
            "entity_type": self.entity_type,
            "start": self.start,
            "end": self.end,
            "confidence": self.confidence,
            "attributes": self.attributes
        }


class NLPEngine:
    """Core NLP engine for medical entity extraction."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.nlp = None
        self.medical_nlp = None
        self.patterns = MedicalPatterns()
        self.initialized = False
        
    async def initialize(self):
        """Initialize the NLP models."""
        try:
            logger.info("Initializing NLP models")
            
            # Load SpaCy models
            await self._load_spacy_models()
            
            # Load transformer models if enabled
            if self.settings.enable_transformers:
                await self._load_transformer_models()
            
            self.initialized = True
            logger.info("NLP engine initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize NLP engine", error=str(e))
            raise
    
    async def _load_spacy_models(self):
        """Load SpaCy models for medical NLP."""
        try:
            # Load general scientific model
            self.nlp = spacy.load(self.settings.spacy_model)
            logger.info("Loaded SpaCy model", model=self.settings.spacy_model)
            
            # Load medical NER model if available
            try:
                self.medical_nlp = spacy.load(self.settings.medical_ner_model)
                logger.info("Loaded medical NER model", model=self.settings.medical_ner_model)
            except OSError:
                logger.warning(
                    "Medical NER model not found, using general model",
                    model=self.settings.medical_ner_model
                )
                self.medical_nlp = self.nlp
                
        except OSError as e:
            logger.error("Failed to load SpaCy models", error=str(e))
            # Fallback to basic English model
            try:
                self.nlp = spacy.load("en_core_web_sm")
                self.medical_nlp = self.nlp
                logger.warning("Using fallback English model")
            except OSError:
                raise RuntimeError("No SpaCy models available. Please install required models.")
    
    async def _load_transformer_models(self):
        """Load transformer models for advanced medical NLP."""
        try:
            # TODO: Load transformer models for medical text
            # from transformers import AutoTokenizer, AutoModel
            # self.tokenizer = AutoTokenizer.from_pretrained(self.settings.transformer_model)
            # self.transformer_model = AutoModel.from_pretrained(self.settings.transformer_model)
            logger.info("Transformer models loading skipped (placeholder)")
        except Exception as e:
            logger.warning("Failed to load transformer models", error=str(e))
    
    async def cleanup(self):
        """Cleanup resources."""
        self.nlp = None
        self.medical_nlp = None
        self.initialized = False
        logger.info("NLP engine cleaned up")
    
    async def extract_entities(self, text: str) -> List[MedicalEntity]:
        """Extract all medical entities from text."""
        if not self.initialized:
            raise RuntimeError("NLP engine not initialized")
        
        if len(text) > self.settings.max_text_length:
            text = text[:self.settings.max_text_length]
            logger.warning("Text truncated to maximum length", max_length=self.settings.max_text_length)
        
        entities = []
        
        # Extract using SpaCy NER
        spacy_entities = await self._extract_spacy_entities(text)
        entities.extend(spacy_entities)
        
        # Extract using regex patterns as fallback
        if self.settings.enable_regex_fallback:
            regex_entities = await self._extract_regex_entities(text)
            entities.extend(regex_entities)
        
        # Remove duplicates and merge overlapping entities
        entities = self._merge_entities(entities)
        
        # Filter by confidence threshold
        entities = [e for e in entities if e.confidence >= self.settings.confidence_threshold]
        
        return entities
    
    async def _extract_spacy_entities(self, text: str) -> List[MedicalEntity]:
        """Extract entities using SpaCy models."""
        entities = []
        
        # Process with medical NLP model
        doc = self.medical_nlp(text)
        
        for ent in doc.ents:
            entity_type = self._map_spacy_label(ent.label_)
            if entity_type:
                entity = MedicalEntity(
                    text=ent.text,
                    entity_type=entity_type,
                    start=ent.start_char,
                    end=ent.end_char,
                    confidence=0.8,  # Default confidence for SpaCy entities
                    attributes={"spacy_label": ent.label_}
                )
                entities.append(entity)
        
        return entities
    
    async def _extract_regex_entities(self, text: str) -> List[MedicalEntity]:
        """Extract entities using regex patterns."""
        entities = []
        
        # Extract symptoms
        symptom_entities = self._extract_symptoms_regex(text)
        entities.extend(symptom_entities)
        
        # Extract medications
        medication_entities = self._extract_medications_regex(text)
        entities.extend(medication_entities)
        
        # Extract dosages
        dosage_entities = self._extract_dosages_regex(text)
        entities.extend(dosage_entities)
        
        # Extract temporal information
        temporal_entities = self._extract_temporal_regex(text)
        entities.extend(temporal_entities)
        
        # Extract severity indicators
        severity_entities = self._extract_severity_regex(text)
        entities.extend(severity_entities)
        
        return entities
    
    def _extract_symptoms_regex(self, text: str) -> List[MedicalEntity]:
        """Extract symptoms using regex patterns."""
        entities = []
        
        for pattern_name, pattern in self.patterns.symptom_patterns.items():
            for match in re.finditer(pattern, text, re.IGNORECASE):
                entity = MedicalEntity(
                    text=match.group(),
                    entity_type="symptom",
                    start=match.start(),
                    end=match.end(),
                    confidence=0.7,
                    attributes={"pattern": pattern_name}
                )
                entities.append(entity)
        
        return entities
    
    def _extract_medications_regex(self, text: str) -> List[MedicalEntity]:
        """Extract medications using regex patterns."""
        entities = []
        
        for pattern_name, pattern in self.patterns.medication_patterns.items():
            for match in re.finditer(pattern, text, re.IGNORECASE):
                entity = MedicalEntity(
                    text=match.group(),
                    entity_type="medication",
                    start=match.start(),
                    end=match.end(),
                    confidence=0.8,
                    attributes={"pattern": pattern_name}
                )
                entities.append(entity)
        
        return entities
    
    def _extract_dosages_regex(self, text: str) -> List[MedicalEntity]:
        """Extract dosage information using regex patterns."""
        entities = []
        
        for pattern_name, pattern in self.patterns.dosage_patterns.items():
            for match in re.finditer(pattern, text, re.IGNORECASE):
                entity = MedicalEntity(
                    text=match.group(),
                    entity_type="dosage",
                    start=match.start(),
                    end=match.end(),
                    confidence=0.9,
                    attributes={"pattern": pattern_name}
                )
                entities.append(entity)
        
        return entities
    
    def _extract_temporal_regex(self, text: str) -> List[MedicalEntity]:
        """Extract temporal information using regex patterns."""
        entities = []
        
        for pattern_name, pattern in self.patterns.temporal_patterns.items():
            for match in re.finditer(pattern, text, re.IGNORECASE):
                # Try to parse the temporal expression
                parsed_time = parse_date(match.group())
                
                entity = MedicalEntity(
                    text=match.group(),
                    entity_type="temporal",
                    start=match.start(),
                    end=match.end(),
                    confidence=0.8,
                    attributes={
                        "pattern": pattern_name,
                        "parsed_time": parsed_time.isoformat() if parsed_time else None
                    }
                )
                entities.append(entity)
        
        return entities
    
    def _extract_severity_regex(self, text: str) -> List[MedicalEntity]:
        """Extract severity indicators using regex patterns."""
        entities = []
        
        for pattern_name, pattern in self.patterns.severity_patterns.items():
            for match in re.finditer(pattern, text, re.IGNORECASE):
                # Extract severity score if present
                severity_score = self._extract_severity_score(match.group())
                
                entity = MedicalEntity(
                    text=match.group(),
                    entity_type="severity",
                    start=match.start(),
                    end=match.end(),
                    confidence=0.9,
                    attributes={
                        "pattern": pattern_name,
                        "severity_score": severity_score
                    }
                )
                entities.append(entity)
        
        return entities
    
    def _extract_severity_score(self, text: str) -> Optional[int]:
        """Extract numeric severity score from text."""
        # Look for patterns like "8/10", "7 out of 10", "scale of 5"
        score_patterns = [
            r'(\d+)\s*/\s*10',
            r'(\d+)\s+out\s+of\s+10',
            r'scale\s+of\s+(\d+)',
            r'level\s+(\d+)'
        ]
        
        for pattern in score_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    return int(match.group(1))
                except (ValueError, IndexError):
                    continue
        
        return None
    
    def _map_spacy_label(self, label: str) -> Optional[str]:
        """Map SpaCy entity labels to our entity types."""
        label_mapping = {
            "DISEASE": "symptom",
            "CHEMICAL": "medication",
            "DRUG": "medication",
            "DOSAGE": "dosage",
            "TIME": "temporal",
            "DATE": "temporal",
            "CARDINAL": "severity",  # For numeric values
        }
        return label_mapping.get(label)
    
    def _merge_entities(self, entities: List[MedicalEntity]) -> List[MedicalEntity]:
        """Merge overlapping entities and remove duplicates."""
        if not entities:
            return entities
        
        # Sort by start position
        entities.sort(key=lambda e: e.start)
        
        merged = []
        current = entities[0]
        
        for next_entity in entities[1:]:
            # Check for overlap
            if next_entity.start <= current.end:
                # Merge entities - keep the one with higher confidence
                if next_entity.confidence > current.confidence:
                    current = next_entity
            else:
                merged.append(current)
                current = next_entity
        
        merged.append(current)
        return merged
