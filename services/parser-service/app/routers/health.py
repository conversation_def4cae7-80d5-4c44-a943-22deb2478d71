"""
Health Check Router for Parser Service

Provides health check endpoints for monitoring and load balancing.
"""

import time
import psutil
from typing import Dict, Any
from fastapi import APIRouter, Depends, Request
from pydantic import BaseModel
import structlog

from ..config import get_settings
from ..nlp_engine import NLPEngine

logger = structlog.get_logger()

router = APIRouter()


class HealthStatus(BaseModel):
    """Health status response model."""
    status: str
    timestamp: float
    version: str
    uptime_seconds: float


class DetailedHealthStatus(BaseModel):
    """Detailed health status with system metrics."""
    status: str
    timestamp: float
    version: str
    uptime_seconds: float
    system: Dict[str, Any]
    nlp_engine: Dict[str, Any]
    configuration: Dict[str, Any]


# Track service start time
SERVICE_START_TIME = time.time()


def get_nlp_engine(request: Request) -> NLPEngine:
    """Get the NLP engine from app state."""
    return getattr(request.app.state, 'nlp_engine', None)


def get_system_metrics() -> Dict[str, Any]:
    """Get system performance metrics."""
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "cpu_usage_percent": cpu_percent,
            "memory": {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_percent": memory.percent
            },
            "disk": {
                "total_gb": round(disk.total / (1024**3), 2),
                "free_gb": round(disk.free / (1024**3), 2),
                "used_percent": round((disk.used / disk.total) * 100, 2)
            }
        }
    except Exception as e:
        logger.warning("Failed to get system metrics", error=str(e))
        return {"error": "Unable to retrieve system metrics"}


@router.get("/", response_model=HealthStatus)
async def health_check():
    """
    Basic health check endpoint.
    
    Returns simple status for load balancers and monitoring systems.
    """
    current_time = time.time()
    uptime = current_time - SERVICE_START_TIME
    
    return HealthStatus(
        status="healthy",
        timestamp=current_time,
        version="0.1.0",
        uptime_seconds=uptime
    )


@router.get("/detailed", response_model=DetailedHealthStatus)
async def detailed_health_check(
    nlp_engine: NLPEngine = Depends(get_nlp_engine)
):
    """
    Detailed health check with system metrics and NLP engine status.
    
    Includes system performance, NLP model status, and configuration.
    """
    settings = get_settings()
    current_time = time.time()
    uptime = current_time - SERVICE_START_TIME
    
    # Get system metrics
    system_metrics = get_system_metrics()
    
    # Check NLP engine status
    nlp_status = {}
    if nlp_engine:
        try:
            # Test basic NLP functionality
            test_text = "I have a headache with severity 7 out of 10."
            entities = await nlp_engine.extract_entities(test_text)
            
            nlp_status = {
                "initialized": nlp_engine.initialized,
                "spacy_model_loaded": nlp_engine.nlp is not None,
                "medical_nlp_loaded": nlp_engine.medical_nlp is not None,
                "test_extraction_working": len(entities) > 0,
                "test_entities_found": len(entities),
                "status": "healthy"
            }
        except Exception as e:
            nlp_status = {
                "initialized": False,
                "status": "unhealthy",
                "error": str(e)
            }
    else:
        nlp_status = {
            "initialized": False,
            "status": "not_initialized"
        }
    
    # Configuration status
    config_status = {
        "debug_mode": settings.debug,
        "environment": "development" if settings.debug else "production",
        "spacy_model": settings.spacy_model,
        "medical_ner_model": settings.medical_ner_model,
        "transformers_enabled": settings.enable_transformers,
        "cache_enabled": settings.enable_cache,
        "metrics_enabled": settings.enable_metrics,
        "confidence_threshold": settings.confidence_threshold
    }
    
    # Determine overall status
    overall_status = "healthy"
    if nlp_status.get("status") != "healthy":
        overall_status = "degraded"
    
    return DetailedHealthStatus(
        status=overall_status,
        timestamp=current_time,
        version="0.1.0",
        uptime_seconds=uptime,
        system=system_metrics,
        nlp_engine=nlp_status,
        configuration=config_status
    )


@router.get("/ready")
async def readiness_check(
    nlp_engine: NLPEngine = Depends(get_nlp_engine)
):
    """
    Readiness check for Kubernetes deployments.
    
    Returns 200 if service is ready to accept traffic, 503 otherwise.
    """
    try:
        # Check if NLP engine is initialized and working
        if not nlp_engine or not nlp_engine.initialized:
            return {"status": "not_ready", "reason": "NLP engine not initialized"}, 503
        
        # Quick functionality test
        test_text = "headache"
        entities = await nlp_engine.extract_entities(test_text)
        
        # Should be able to extract at least basic entities
        if len(entities) == 0:
            logger.warning("NLP engine not extracting entities properly")
        
        return {"status": "ready", "timestamp": time.time()}
        
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        return {"status": "not_ready", "reason": str(e)}, 503


@router.get("/live")
async def liveness_check():
    """
    Liveness check for Kubernetes deployments.
    
    Returns 200 if service is alive, 503 if it should be restarted.
    """
    try:
        # Basic checks to ensure service is functioning
        current_time = time.time()
        uptime = current_time - SERVICE_START_TIME
        
        # Check if service has been running too long (optional restart trigger)
        max_uptime = 24 * 60 * 60  # 24 hours
        if uptime > max_uptime:
            logger.warning("Service uptime exceeds maximum", uptime_hours=uptime/3600)
        
        # Check system resources
        try:
            memory = psutil.virtual_memory()
            if memory.percent > 95:  # Critical memory usage
                logger.error("Critical memory usage detected", memory_percent=memory.percent)
                return {"status": "unhealthy", "reason": "Critical memory usage"}, 503
        except:
            pass  # Don't fail liveness check if we can't get metrics
        
        return {"status": "alive", "timestamp": current_time, "uptime_seconds": uptime}
        
    except Exception as e:
        logger.error("Liveness check failed", error=str(e))
        return {"status": "dead", "reason": str(e)}, 503


@router.get("/models")
async def model_status(nlp_engine: NLPEngine = Depends(get_nlp_engine)):
    """
    Get status of loaded NLP models.
    """
    if not nlp_engine:
        return {"error": "NLP engine not initialized"}
    
    settings = get_settings()
    
    return {
        "models": {
            "spacy_model": {
                "name": settings.spacy_model,
                "loaded": nlp_engine.nlp is not None,
                "type": "scientific"
            },
            "medical_ner_model": {
                "name": settings.medical_ner_model,
                "loaded": nlp_engine.medical_nlp is not None,
                "type": "medical_ner"
            },
            "transformer_model": {
                "name": settings.transformer_model if settings.enable_transformers else None,
                "loaded": False,  # TODO: Check transformer model status
                "type": "transformer",
                "enabled": settings.enable_transformers
            }
        },
        "configuration": settings.get_model_config(),
        "extraction_config": settings.get_extraction_config()
    }


@router.get("/version")
async def version_info():
    """
    Service version information.
    """
    return {
        "service": "SymptomOS Parser Service",
        "version": "0.1.0",
        "build_time": "2024-01-15T00:00:00Z",  # TODO: Set during build
        "git_commit": "unknown",  # TODO: Set during build
        "python_version": "3.11+",
        "dependencies": {
            "fastapi": "0.109.0",
            "spacy": "3.7.2",
            "scispacy": "0.5.3",
            "transformers": "4.36.0"
        }
    }
