"""
Parse Router - Main text and voice parsing endpoints

Handles parsing of medical text and voice recordings to extract structured information.
"""

import time
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Request, UploadFile, File
from pydantic import BaseModel, Field
import structlog
import httpx

from ..config import get_settings
from ..nlp_engine import NLPEngine, MedicalEntity

logger = structlog.get_logger()

router = APIRouter()


class ParseTextRequest(BaseModel):
    """Request model for text parsing."""
    text: str = Field(..., min_length=1, max_length=10000, description="Text to parse")
    patient_id: Optional[str] = Field(default=None, description="Patient identifier")
    extract_symptoms: bool = Field(default=True, description="Extract symptom entities")
    extract_medications: bool = Field(default=True, description="Extract medication entities")
    extract_temporal: bool = Field(default=True, description="Extract temporal information")
    extract_severity: bool = Field(default=True, description="Extract severity indicators")
    confidence_threshold: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="Minimum confidence threshold")


class ParsedEntity(BaseModel):
    """Parsed medical entity."""
    text: str = Field(description="Entity text")
    entity_type: str = Field(description="Type of entity")
    start: int = Field(description="Start position in text")
    end: int = Field(description="End position in text")
    confidence: float = Field(description="Confidence score (0-1)")
    attributes: Dict[str, Any] = Field(default={}, description="Additional attributes")


class ParseTextResponse(BaseModel):
    """Response model for text parsing."""
    success: bool = Field(description="Whether parsing was successful")
    entities: List[ParsedEntity] = Field(description="Extracted entities")
    entity_count: int = Field(description="Total number of entities")
    processing_time_ms: float = Field(description="Processing time in milliseconds")
    text_length: int = Field(description="Length of input text")
    confidence_threshold: float = Field(description="Confidence threshold used")


class ParseVoiceRequest(BaseModel):
    """Request model for voice parsing metadata."""
    patient_id: Optional[str] = Field(default=None, description="Patient identifier")
    language_code: str = Field(default="en-US", description="Language code")
    extract_entities: bool = Field(default=True, description="Extract entities from transcription")
    confidence_threshold: Optional[float] = Field(default=None, description="Minimum confidence threshold")


class ParseVoiceResponse(BaseModel):
    """Response model for voice parsing."""
    success: bool = Field(description="Whether parsing was successful")
    transcription: str = Field(description="Voice transcription")
    transcription_confidence: float = Field(description="Transcription confidence")
    entities: List[ParsedEntity] = Field(description="Extracted entities")
    entity_count: int = Field(description="Total number of entities")
    processing_time_ms: float = Field(description="Total processing time in milliseconds")
    audio_duration_ms: Optional[int] = Field(default=None, description="Audio duration in milliseconds")


def get_nlp_engine(request: Request) -> NLPEngine:
    """Get the NLP engine from app state."""
    return request.app.state.nlp_engine


@router.post("/text", response_model=ParseTextResponse)
async def parse_text(
    request: ParseTextRequest,
    nlp_engine: NLPEngine = Depends(get_nlp_engine)
):
    """
    Parse medical text and extract structured entities.
    
    Extracts symptoms, medications, dosages, temporal information,
    and severity indicators from natural language text.
    """
    settings = get_settings()
    
    try:
        logger.info(
            "Processing text parsing request",
            text_length=len(request.text),
            patient_id=request.patient_id,
            extract_symptoms=request.extract_symptoms,
            extract_medications=request.extract_medications
        )
        
        start_time = time.time()
        
        # Extract entities using NLP engine
        entities = await nlp_engine.extract_entities(request.text)
        
        # Filter entities based on request preferences
        filtered_entities = []
        for entity in entities:
            # Apply confidence threshold
            threshold = request.confidence_threshold or settings.confidence_threshold
            if entity.confidence < threshold:
                continue
            
            # Apply entity type filters
            if entity.entity_type == "symptom" and not request.extract_symptoms:
                continue
            if entity.entity_type == "medication" and not request.extract_medications:
                continue
            if entity.entity_type == "temporal" and not request.extract_temporal:
                continue
            if entity.entity_type == "severity" and not request.extract_severity:
                continue
            
            filtered_entities.append(entity)
        
        processing_time = (time.time() - start_time) * 1000
        
        # Convert to response format
        parsed_entities = [
            ParsedEntity(
                text=entity.text,
                entity_type=entity.entity_type,
                start=entity.start,
                end=entity.end,
                confidence=entity.confidence,
                attributes=entity.attributes
            )
            for entity in filtered_entities
        ]
        
        response = ParseTextResponse(
            success=True,
            entities=parsed_entities,
            entity_count=len(parsed_entities),
            processing_time_ms=processing_time,
            text_length=len(request.text),
            confidence_threshold=request.confidence_threshold or settings.confidence_threshold
        )
        
        logger.info(
            "Text parsing completed",
            entities_found=len(parsed_entities),
            processing_time_ms=processing_time,
            patient_id=request.patient_id
        )
        
        return response
        
    except Exception as e:
        logger.error("Error during text parsing", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=f"Text parsing failed: {str(e)}")


@router.post("/voice", response_model=ParseVoiceResponse)
async def parse_voice(
    audio_file: UploadFile = File(..., description="Audio file to parse"),
    request_data: ParseVoiceRequest = Depends(),
    nlp_engine: NLPEngine = Depends(get_nlp_engine)
):
    """
    Parse voice recording and extract structured medical entities.
    
    First transcribes the audio using the Granite Gateway service,
    then extracts medical entities from the transcription.
    """
    settings = get_settings()
    
    try:
        logger.info(
            "Processing voice parsing request",
            filename=audio_file.filename,
            content_type=audio_file.content_type,
            patient_id=request_data.patient_id
        )
        
        start_time = time.time()
        
        # Step 1: Transcribe audio using Granite Gateway
        transcription_result = await _transcribe_audio(audio_file, request_data.language_code)
        
        transcription = transcription_result.get("transcription", "")
        transcription_confidence = transcription_result.get("confidence", 0.0)
        
        if not transcription:
            raise HTTPException(status_code=400, detail="Failed to transcribe audio")
        
        # Step 2: Extract entities from transcription
        entities = []
        if request_data.extract_entities and transcription:
            entities = await nlp_engine.extract_entities(transcription)
            
            # Apply confidence threshold
            threshold = request_data.confidence_threshold or settings.confidence_threshold
            entities = [e for e in entities if e.confidence >= threshold]
        
        processing_time = (time.time() - start_time) * 1000
        
        # Convert to response format
        parsed_entities = [
            ParsedEntity(
                text=entity.text,
                entity_type=entity.entity_type,
                start=entity.start,
                end=entity.end,
                confidence=entity.confidence,
                attributes=entity.attributes
            )
            for entity in entities
        ]
        
        response = ParseVoiceResponse(
            success=True,
            transcription=transcription,
            transcription_confidence=transcription_confidence,
            entities=parsed_entities,
            entity_count=len(parsed_entities),
            processing_time_ms=processing_time,
            audio_duration_ms=transcription_result.get("duration_ms")
        )
        
        logger.info(
            "Voice parsing completed",
            transcription_length=len(transcription),
            entities_found=len(parsed_entities),
            processing_time_ms=processing_time,
            patient_id=request_data.patient_id
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error during voice parsing", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=f"Voice parsing failed: {str(e)}")


async def _transcribe_audio(audio_file: UploadFile, language_code: str) -> Dict[str, Any]:
    """Transcribe audio using the Granite Gateway service."""
    settings = get_settings()
    
    try:
        # Prepare the request to Granite Gateway
        files = {"audio_file": (audio_file.filename, await audio_file.read(), audio_file.content_type)}
        data = {
            "language_code": language_code,
            "enable_medical_terms": True,
            "extract_entities": False  # We'll do entity extraction ourselves
        }
        
        async with httpx.AsyncClient(timeout=settings.request_timeout) as client:
            response = await client.post(
                f"{settings.granite_gateway_url}/v1/asr/",
                files=files,
                data=data,
                headers={"Authorization": "Bearer sk-symptomos-parser-service"}  # API key auth
            )
            
            if response.status_code != 200:
                logger.error(
                    "Granite Gateway transcription failed",
                    status_code=response.status_code,
                    response=response.text
                )
                raise HTTPException(
                    status_code=502,
                    detail=f"Transcription service error: {response.status_code}"
                )
            
            return response.json()
            
    except httpx.RequestError as e:
        logger.error("Failed to connect to Granite Gateway", error=str(e))
        raise HTTPException(
            status_code=503,
            detail="Transcription service unavailable"
        )


@router.post("/batch-text")
async def parse_batch_text(
    texts: List[str] = Field(..., max_items=50, description="List of texts to parse"),
    extract_symptoms: bool = Field(default=True, description="Extract symptom entities"),
    extract_medications: bool = Field(default=True, description="Extract medication entities"),
    confidence_threshold: Optional[float] = Field(default=None, description="Minimum confidence threshold"),
    nlp_engine: NLPEngine = Depends(get_nlp_engine)
):
    """
    Parse multiple texts in batch for improved efficiency.
    
    Useful for processing multiple symptom reports or medical notes at once.
    """
    settings = get_settings()
    
    if len(texts) > 50:
        raise HTTPException(status_code=400, detail="Too many texts in batch. Maximum: 50")
    
    try:
        logger.info("Processing batch text parsing", batch_size=len(texts))
        
        start_time = time.time()
        results = []
        
        for i, text in enumerate(texts):
            try:
                # Create individual request
                request = ParseTextRequest(
                    text=text,
                    extract_symptoms=extract_symptoms,
                    extract_medications=extract_medications,
                    confidence_threshold=confidence_threshold
                )
                
                # Parse individual text
                result = await parse_text(request, nlp_engine)
                results.append({
                    "index": i,
                    "success": True,
                    "result": result
                })
                
            except Exception as e:
                logger.warning(f"Failed to parse text {i}", error=str(e))
                results.append({
                    "index": i,
                    "success": False,
                    "error": str(e)
                })
        
        processing_time = (time.time() - start_time) * 1000
        
        successful_count = len([r for r in results if r["success"]])
        
        logger.info(
            "Batch text parsing completed",
            total_texts=len(texts),
            successful=successful_count,
            failed=len(texts) - successful_count,
            processing_time_ms=processing_time
        )
        
        return {
            "success": True,
            "results": results,
            "total_count": len(texts),
            "successful_count": successful_count,
            "failed_count": len(texts) - successful_count,
            "processing_time_ms": processing_time
        }
        
    except Exception as e:
        logger.error("Error during batch text parsing", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=f"Batch parsing failed: {str(e)}")


@router.get("/health")
async def parse_health_check(nlp_engine: NLPEngine = Depends(get_nlp_engine)):
    """Health check for parsing service."""
    try:
        # Test basic NLP functionality
        test_text = "I have a headache with severity 7 out of 10."
        entities = await nlp_engine.extract_entities(test_text)
        
        return {
            "service": "Parser",
            "status": "healthy",
            "nlp_engine_initialized": nlp_engine.initialized,
            "test_entities_found": len(entities),
            "models_loaded": {
                "spacy": nlp_engine.nlp is not None,
                "medical_nlp": nlp_engine.medical_nlp is not None
            }
        }
    except Exception as e:
        logger.error("Parse health check failed", error=str(e))
        return {
            "service": "Parser",
            "status": "unhealthy",
            "error": str(e)
        }
