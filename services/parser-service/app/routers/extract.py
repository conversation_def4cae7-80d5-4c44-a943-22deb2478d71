"""
Extract Router - Specialized entity extraction endpoints

Provides specialized endpoints for extracting specific types of medical entities.
"""

import time
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field
import structlog

from ..config import get_settings
from ..nlp_engine import NLPEngine, MedicalEntity

logger = structlog.get_logger()

router = APIRouter()


class ExtractEntitiesRequest(BaseModel):
    """Request model for general entity extraction."""
    text: str = Field(..., min_length=1, max_length=10000, description="Text to analyze")
    entity_types: Optional[List[str]] = Field(
        default=None,
        description="Specific entity types to extract (symptom, medication, dosage, temporal, severity)"
    )
    confidence_threshold: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="Minimum confidence")


class ExtractSymptomsRequest(BaseModel):
    """Request model for symptom extraction."""
    text: str = Field(..., min_length=1, max_length=10000, description="Text to analyze")
    include_severity: bool = Field(default=True, description="Include severity information")
    include_location: bool = Field(default=True, description="Include body location information")
    confidence_threshold: Optional[float] = Field(default=None, description="Minimum confidence")


class ExtractMedicationsRequest(BaseModel):
    """Request model for medication extraction."""
    text: str = Field(..., min_length=1, max_length=10000, description="Text to analyze")
    include_dosage: bool = Field(default=True, description="Include dosage information")
    include_frequency: bool = Field(default=True, description="Include frequency information")
    confidence_threshold: Optional[float] = Field(default=None, description="Minimum confidence")


class SymptomEntity(BaseModel):
    """Symptom entity with medical-specific attributes."""
    text: str = Field(description="Symptom text")
    start: int = Field(description="Start position")
    end: int = Field(description="End position")
    confidence: float = Field(description="Confidence score")
    severity_score: Optional[int] = Field(default=None, description="Severity score (1-10)")
    body_location: Optional[str] = Field(default=None, description="Body location")
    duration: Optional[str] = Field(default=None, description="Duration information")
    onset: Optional[str] = Field(default=None, description="Onset information")


class MedicationEntity(BaseModel):
    """Medication entity with medical-specific attributes."""
    text: str = Field(description="Medication text")
    start: int = Field(description="Start position")
    end: int = Field(description="End position")
    confidence: float = Field(description="Confidence score")
    dosage: Optional[str] = Field(default=None, description="Dosage amount")
    frequency: Optional[str] = Field(default=None, description="Frequency of administration")
    route: Optional[str] = Field(default=None, description="Route of administration")
    indication: Optional[str] = Field(default=None, description="What it's for")


class EntityExtractionResponse(BaseModel):
    """Response model for entity extraction."""
    success: bool = Field(description="Whether extraction was successful")
    entities: List[Dict[str, Any]] = Field(description="Extracted entities")
    entity_count: int = Field(description="Total number of entities")
    processing_time_ms: float = Field(description="Processing time in milliseconds")
    confidence_threshold: float = Field(description="Confidence threshold used")


class SymptomExtractionResponse(BaseModel):
    """Response model for symptom extraction."""
    success: bool = Field(description="Whether extraction was successful")
    symptoms: List[SymptomEntity] = Field(description="Extracted symptoms")
    symptom_count: int = Field(description="Total number of symptoms")
    processing_time_ms: float = Field(description="Processing time in milliseconds")


class MedicationExtractionResponse(BaseModel):
    """Response model for medication extraction."""
    success: bool = Field(description="Whether extraction was successful")
    medications: List[MedicationEntity] = Field(description="Extracted medications")
    medication_count: int = Field(description="Total number of medications")
    processing_time_ms: float = Field(description="Processing time in milliseconds")


def get_nlp_engine(request: Request) -> NLPEngine:
    """Get the NLP engine from app state."""
    return request.app.state.nlp_engine


@router.post("/entities", response_model=EntityExtractionResponse)
async def extract_entities(
    request: ExtractEntitiesRequest,
    nlp_engine: NLPEngine = Depends(get_nlp_engine)
):
    """
    Extract all medical entities from text.
    
    General-purpose entity extraction that can be filtered by entity type.
    """
    settings = get_settings()
    
    try:
        logger.info(
            "Processing entity extraction request",
            text_length=len(request.text),
            entity_types=request.entity_types
        )
        
        start_time = time.time()
        
        # Extract all entities
        entities = await nlp_engine.extract_entities(request.text)
        
        # Filter by entity types if specified
        if request.entity_types:
            entities = [e for e in entities if e.entity_type in request.entity_types]
        
        # Apply confidence threshold
        threshold = request.confidence_threshold or settings.confidence_threshold
        entities = [e for e in entities if e.confidence >= threshold]
        
        processing_time = (time.time() - start_time) * 1000
        
        # Convert to response format
        entity_dicts = [entity.to_dict() for entity in entities]
        
        response = EntityExtractionResponse(
            success=True,
            entities=entity_dicts,
            entity_count=len(entity_dicts),
            processing_time_ms=processing_time,
            confidence_threshold=threshold
        )
        
        logger.info(
            "Entity extraction completed",
            entities_found=len(entity_dicts),
            processing_time_ms=processing_time
        )
        
        return response
        
    except Exception as e:
        logger.error("Error during entity extraction", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=f"Entity extraction failed: {str(e)}")


@router.post("/symptoms", response_model=SymptomExtractionResponse)
async def extract_symptoms(
    request: ExtractSymptomsRequest,
    nlp_engine: NLPEngine = Depends(get_nlp_engine)
):
    """
    Extract symptom entities with medical-specific attributes.
    
    Specialized extraction for symptoms including severity, location, and duration.
    """
    settings = get_settings()
    
    try:
        logger.info(
            "Processing symptom extraction request",
            text_length=len(request.text),
            include_severity=request.include_severity,
            include_location=request.include_location
        )
        
        start_time = time.time()
        
        # Extract all entities
        all_entities = await nlp_engine.extract_entities(request.text)
        
        # Filter for symptoms and related entities
        symptom_entities = [e for e in all_entities if e.entity_type == "symptom"]
        severity_entities = [e for e in all_entities if e.entity_type == "severity"]
        
        # Apply confidence threshold
        threshold = request.confidence_threshold or settings.confidence_threshold
        symptom_entities = [e for e in symptom_entities if e.confidence >= threshold]
        
        # Enhance symptoms with additional attributes
        enhanced_symptoms = []
        for symptom in symptom_entities:
            # Find related severity information
            severity_score = None
            if request.include_severity:
                severity_score = _find_related_severity(symptom, severity_entities, request.text)
            
            # Find body location
            body_location = None
            if request.include_location:
                body_location = _extract_body_location(symptom, request.text)
            
            # Extract duration and onset
            duration = _extract_duration(symptom, request.text)
            onset = _extract_onset(symptom, request.text)
            
            enhanced_symptom = SymptomEntity(
                text=symptom.text,
                start=symptom.start,
                end=symptom.end,
                confidence=symptom.confidence,
                severity_score=severity_score,
                body_location=body_location,
                duration=duration,
                onset=onset
            )
            enhanced_symptoms.append(enhanced_symptom)
        
        processing_time = (time.time() - start_time) * 1000
        
        response = SymptomExtractionResponse(
            success=True,
            symptoms=enhanced_symptoms,
            symptom_count=len(enhanced_symptoms),
            processing_time_ms=processing_time
        )
        
        logger.info(
            "Symptom extraction completed",
            symptoms_found=len(enhanced_symptoms),
            processing_time_ms=processing_time
        )
        
        return response
        
    except Exception as e:
        logger.error("Error during symptom extraction", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=f"Symptom extraction failed: {str(e)}")


@router.post("/medications", response_model=MedicationExtractionResponse)
async def extract_medications(
    request: ExtractMedicationsRequest,
    nlp_engine: NLPEngine = Depends(get_nlp_engine)
):
    """
    Extract medication entities with dosage and frequency information.
    
    Specialized extraction for medications including dosage, frequency, and route.
    """
    settings = get_settings()
    
    try:
        logger.info(
            "Processing medication extraction request",
            text_length=len(request.text),
            include_dosage=request.include_dosage,
            include_frequency=request.include_frequency
        )
        
        start_time = time.time()
        
        # Extract all entities
        all_entities = await nlp_engine.extract_entities(request.text)
        
        # Filter for medications and related entities
        medication_entities = [e for e in all_entities if e.entity_type == "medication"]
        dosage_entities = [e for e in all_entities if e.entity_type == "dosage"]
        
        # Apply confidence threshold
        threshold = request.confidence_threshold or settings.confidence_threshold
        medication_entities = [e for e in medication_entities if e.confidence >= threshold]
        
        # Enhance medications with additional attributes
        enhanced_medications = []
        for medication in medication_entities:
            # Find related dosage information
            dosage = None
            if request.include_dosage:
                dosage = _find_related_dosage(medication, dosage_entities, request.text)
            
            # Find frequency information
            frequency = None
            if request.include_frequency:
                frequency = _extract_frequency(medication, request.text)
            
            # Extract route and indication
            route = _extract_route(medication, request.text)
            indication = _extract_indication(medication, request.text)
            
            enhanced_medication = MedicationEntity(
                text=medication.text,
                start=medication.start,
                end=medication.end,
                confidence=medication.confidence,
                dosage=dosage,
                frequency=frequency,
                route=route,
                indication=indication
            )
            enhanced_medications.append(enhanced_medication)
        
        processing_time = (time.time() - start_time) * 1000
        
        response = MedicationExtractionResponse(
            success=True,
            medications=enhanced_medications,
            medication_count=len(enhanced_medications),
            processing_time_ms=processing_time
        )
        
        logger.info(
            "Medication extraction completed",
            medications_found=len(enhanced_medications),
            processing_time_ms=processing_time
        )
        
        return response
        
    except Exception as e:
        logger.error("Error during medication extraction", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=f"Medication extraction failed: {str(e)}")


def _find_related_severity(symptom: MedicalEntity, severity_entities: List[MedicalEntity], text: str) -> Optional[int]:
    """Find severity score related to a symptom."""
    # Look for severity entities near the symptom
    for severity in severity_entities:
        # Check if severity is within 50 characters of the symptom
        if abs(severity.start - symptom.end) <= 50 or abs(symptom.start - severity.end) <= 50:
            # Extract numeric score from severity attributes
            return severity.attributes.get("severity_score")
    
    # Fallback: look for numeric patterns near the symptom
    import re
    symptom_context = text[max(0, symptom.start-50):symptom.end+50]
    score_match = re.search(r'(\d+)\s*(?:out\s+of\s+|/)\s*10', symptom_context)
    if score_match:
        return int(score_match.group(1))
    
    return None


def _extract_body_location(symptom: MedicalEntity, text: str) -> Optional[str]:
    """Extract body location related to a symptom."""
    import re
    
    body_parts = [
        "head", "neck", "chest", "back", "stomach", "abdomen", "arm", "leg",
        "shoulder", "knee", "ankle", "wrist", "elbow", "hip", "foot", "hand"
    ]
    
    # Look for body parts near the symptom
    symptom_context = text[max(0, symptom.start-30):symptom.end+30].lower()
    for part in body_parts:
        if part in symptom_context:
            return part
    
    return None


def _extract_duration(symptom: MedicalEntity, text: str) -> Optional[str]:
    """Extract duration information for a symptom."""
    import re
    
    symptom_context = text[max(0, symptom.start-50):symptom.end+50]
    duration_patterns = [
        r'for\s+(\d+\s+(?:minutes?|hours?|days?|weeks?))',
        r'since\s+(\w+)',
        r'(\d+\s+(?:minutes?|hours?|days?|weeks?))\s+ago'
    ]
    
    for pattern in duration_patterns:
        match = re.search(pattern, symptom_context, re.IGNORECASE)
        if match:
            return match.group(1)
    
    return None


def _extract_onset(symptom: MedicalEntity, text: str) -> Optional[str]:
    """Extract onset information for a symptom."""
    import re
    
    symptom_context = text[max(0, symptom.start-50):symptom.end+50]
    onset_patterns = [
        r'started\s+(\w+)',
        r'began\s+(\w+)',
        r'sudden(?:ly)?',
        r'gradual(?:ly)?'
    ]
    
    for pattern in onset_patterns:
        match = re.search(pattern, symptom_context, re.IGNORECASE)
        if match:
            return match.group(0)
    
    return None


def _find_related_dosage(medication: MedicalEntity, dosage_entities: List[MedicalEntity], text: str) -> Optional[str]:
    """Find dosage information related to a medication."""
    # Look for dosage entities near the medication
    for dosage in dosage_entities:
        if abs(dosage.start - medication.end) <= 30 or abs(medication.start - dosage.end) <= 30:
            return dosage.text
    
    return None


def _extract_frequency(medication: MedicalEntity, text: str) -> Optional[str]:
    """Extract frequency information for a medication."""
    import re
    
    med_context = text[max(0, medication.start-50):medication.end+50]
    frequency_patterns = [
        r'(?:once|twice|three times?)\s+(?:a\s+)?day',
        r'every\s+\d+\s+hours?',
        r'as\s+needed',
        r'daily',
        r'weekly'
    ]
    
    for pattern in frequency_patterns:
        match = re.search(pattern, med_context, re.IGNORECASE)
        if match:
            return match.group(0)
    
    return None


def _extract_route(medication: MedicalEntity, text: str) -> Optional[str]:
    """Extract route of administration for a medication."""
    import re
    
    med_context = text[max(0, medication.start-30):medication.end+30]
    route_patterns = [
        r'by\s+mouth',
        r'orally?',
        r'injection',
        r'IV',
        r'topical'
    ]
    
    for pattern in route_patterns:
        match = re.search(pattern, med_context, re.IGNORECASE)
        if match:
            return match.group(0)
    
    return None


def _extract_indication(medication: MedicalEntity, text: str) -> Optional[str]:
    """Extract indication (what the medication is for) from context."""
    import re
    
    med_context = text[max(0, medication.start-100):medication.end+50]
    indication_patterns = [
        r'for\s+(?:my\s+)?(\w+(?:\s+\w+)?)',
        r'to\s+treat\s+(\w+(?:\s+\w+)?)',
        r'because\s+of\s+(\w+(?:\s+\w+)?)'
    ]
    
    for pattern in indication_patterns:
        match = re.search(pattern, med_context, re.IGNORECASE)
        if match:
            return match.group(1)
    
    return None


@router.get("/health")
async def extract_health_check(nlp_engine: NLPEngine = Depends(get_nlp_engine)):
    """Health check for extraction service."""
    try:
        # Test extraction functionality
        test_text = "I took 400mg ibuprofen for my severe headache rated 8/10."
        entities = await nlp_engine.extract_entities(test_text)
        
        return {
            "service": "Extract",
            "status": "healthy",
            "nlp_engine_initialized": nlp_engine.initialized,
            "test_entities_found": len(entities),
            "entity_types_found": list(set(e.entity_type for e in entities))
        }
    except Exception as e:
        logger.error("Extract health check failed", error=str(e))
        return {
            "service": "Extract",
            "status": "unhealthy",
            "error": str(e)
        }
