"""
Configuration settings for Parser Service
"""

from functools import lru_cache
from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )
    
    # Application settings
    app_name: str = "SymptomOS Parser Service"
    debug: bool = Field(default=False, description="Enable debug mode")
    host: str = Field(default="0.0.0.0", description="Host to bind to")
    port: int = Field(default=8001, description="Port to bind to")
    
    # Security settings
    secret_key: str = Field(..., description="Secret key for JWT tokens")
    algorithm: str = Field(default="HS256", description="JWT algorithm")
    access_token_expire_minutes: int = Field(default=30, description="Token expiration time")
    
    # CORS settings
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:5173", "http://localhost:8000"],
        description="Allowed CORS origins"
    )
    allowed_hosts: List[str] = Field(
        default=["localhost", "127.0.0.1", "0.0.0.0"],
        description="Allowed hosts"
    )
    
    # External service URLs
    granite_gateway_url: str = Field(
        default="http://localhost:8000",
        description="Granite Gateway service URL"
    )
    ogce_graph_url: str = Field(
        default="http://localhost:8002",
        description="OGCE Graph service URL"
    )
    
    # NLP Model settings
    spacy_model: str = Field(
        default="en_core_sci_sm",
        description="SpaCy model for medical NLP"
    )
    medical_ner_model: str = Field(
        default="en_ner_bc5cdr_md",
        description="Medical NER model"
    )
    enable_transformers: bool = Field(
        default=True,
        description="Enable transformer-based models"
    )
    transformer_model: str = Field(
        default="emilyalsentzer/Bio_ClinicalBERT",
        description="Transformer model for medical text"
    )
    
    # Processing settings
    max_text_length: int = Field(default=10000, description="Maximum text length to process")
    batch_size: int = Field(default=32, description="Batch size for processing")
    confidence_threshold: float = Field(default=0.7, description="Minimum confidence for entities")
    
    # Cache settings
    enable_cache: bool = Field(default=True, description="Enable result caching")
    cache_ttl: int = Field(default=3600, description="Cache TTL in seconds")
    redis_url: str = Field(default="redis://localhost:6379", description="Redis URL for caching")
    
    # Timeout settings
    request_timeout: int = Field(default=30, description="Request timeout in seconds")
    model_timeout: int = Field(default=60, description="Model processing timeout in seconds")
    
    # Monitoring settings
    enable_metrics: bool = Field(default=True, description="Enable Prometheus metrics")
    metrics_port: int = Field(default=8011, description="Metrics server port")
    
    # Logging settings
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format (json or text)")
    
    # Medical entity extraction settings
    extract_symptoms: bool = Field(default=True, description="Extract symptom entities")
    extract_medications: bool = Field(default=True, description="Extract medication entities")
    extract_dosages: bool = Field(default=True, description="Extract dosage information")
    extract_temporal: bool = Field(default=True, description="Extract temporal information")
    extract_severity: bool = Field(default=True, description="Extract severity indicators")
    
    # Regex patterns for medical entities
    enable_regex_fallback: bool = Field(
        default=True,
        description="Enable regex-based extraction as fallback"
    )
    
    # Rate limiting
    rate_limit_requests: int = Field(default=200, description="Requests per minute")
    rate_limit_window: int = Field(default=60, description="Rate limit window in seconds")
    
    # Health check settings
    health_check_interval: int = Field(default=30, description="Health check interval in seconds")
    
    @property
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return not self.debug
    
    def get_model_config(self) -> dict:
        """Get NLP model configuration."""
        return {
            "spacy_model": self.spacy_model,
            "medical_ner_model": self.medical_ner_model,
            "transformer_model": self.transformer_model if self.enable_transformers else None,
            "confidence_threshold": self.confidence_threshold,
            "batch_size": self.batch_size
        }
    
    def get_extraction_config(self) -> dict:
        """Get entity extraction configuration."""
        return {
            "extract_symptoms": self.extract_symptoms,
            "extract_medications": self.extract_medications,
            "extract_dosages": self.extract_dosages,
            "extract_temporal": self.extract_temporal,
            "extract_severity": self.extract_severity,
            "enable_regex_fallback": self.enable_regex_fallback
        }


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()
