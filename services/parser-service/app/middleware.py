"""
Middleware for Parser Service

Provides logging, metrics, and request processing middleware.
"""

import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import structlog

logger = structlog.get_logger()


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for structured request/response logging."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID
        request_id = str(uuid.uuid4())
        
        # Add request ID to context
        with structlog.contextvars.bound_contextvars(request_id=request_id):
            start_time = time.time()
            
            # Log request
            logger.info(
                "Request started",
                method=request.method,
                url=str(request.url),
                client_ip=request.client.host if request.client else None,
                user_agent=request.headers.get("user-agent"),
                content_length=request.headers.get("content-length")
            )
            
            # Process request
            try:
                response = await call_next(request)
                
                # Calculate processing time
                processing_time = time.time() - start_time
                
                # Log response
                logger.info(
                    "Request completed",
                    status_code=response.status_code,
                    processing_time_ms=processing_time * 1000,
                    response_size=response.headers.get("content-length")
                )
                
                # Add request ID to response headers
                response.headers["X-Request-ID"] = request_id
                response.headers["X-Processing-Time"] = f"{processing_time:.3f}s"
                
                return response
                
            except Exception as e:
                processing_time = time.time() - start_time
                
                logger.error(
                    "Request failed",
                    error=str(e),
                    processing_time_ms=processing_time * 1000,
                    exc_info=True
                )
                
                raise


class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting request metrics."""
    
    def __init__(self, app):
        super().__init__(app)
        # In a real implementation, you'd initialize Prometheus metrics here
        self.request_count = 0
        self.request_duration_sum = 0.0
        self.error_count = 0
        self.entity_extraction_count = 0
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        try:
            response = await call_next(request)
            
            # Record metrics
            processing_time = time.time() - start_time
            self.request_count += 1
            self.request_duration_sum += processing_time
            
            # Track entity extraction requests
            if "/extract/" in str(request.url) or "/parse/" in str(request.url):
                self.entity_extraction_count += 1
            
            # TODO: Record Prometheus metrics
            # - Request count by method, path, status code
            # - Request duration histogram
            # - Entity extraction metrics
            # - NLP model performance metrics
            
            # Add metrics headers for debugging
            response.headers["X-Request-Count"] = str(self.request_count)
            response.headers["X-Entity-Extractions"] = str(self.entity_extraction_count)
            
            return response
            
        except Exception as e:
            # Record error metrics
            self.error_count += 1
            
            # TODO: Record error metrics in Prometheus
            
            raise
