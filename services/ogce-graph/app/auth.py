"""
Authentication for OGCE Graph Service

Simple authentication using API keys and JWT tokens.
"""

from typing import Optional, Dict, Any
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
import structlog

from .config import get_settings

logger = structlog.get_logger()

# HTTP Bearer token scheme
security = HTTPBearer()


class User:
    """User model."""
    def __init__(self, username: str, scopes: list[str]):
        self.username = username
        self.scopes = scopes


class APIKeyAuth:
    """Simple API key authentication for service-to-service communication."""
    
    def __init__(self):
        self.api_keys = {
            "sk-symptomos-granite-gateway": {
                "name": "Granite Gateway Service",
                "scopes": ["read", "write"],
                "active": True
            },
            "sk-symptomos-parser-service": {
                "name": "Parser Service", 
                "scopes": ["read", "write"],
                "active": True
            },
            "sk-symptomos-mobile-app": {
                "name": "Mobile App",
                "scopes": ["read"],
                "active": True
            },
            "sk-symptomos-doctor-desktop": {
                "name": "Doctor Desktop App",
                "scopes": ["read", "write"],
                "active": True
            }
        }
    
    def validate_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Validate an API key and return associated metadata."""
        if api_key in self.api_keys:
            key_data = self.api_keys[api_key]
            if key_data["active"]:
                return key_data
        return None


api_key_auth = APIKeyAuth()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """Get the current authenticated user from JWT token or API key."""
    settings = get_settings()
    
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    token = credentials.credentials
    
    # Check if it's an API key (starts with 'sk-')
    if token.startswith('sk-'):
        key_data = api_key_auth.validate_api_key(token)
        if key_data:
            return User(
                username=key_data["name"],
                scopes=key_data["scopes"]
            )
        else:
            logger.warning("Invalid API key used", api_key_prefix=token[:10])
            raise credentials_exception
    
    # Try JWT authentication
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        
        scopes = payload.get("scopes", ["read"])
        return User(username=username, scopes=scopes)
        
    except JWTError as e:
        logger.warning("JWT validation failed", error=str(e))
        raise credentials_exception
