"""
Middleware for OGCE Graph Service

Provides logging, metrics, and request processing middleware.
"""

import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import structlog

logger = structlog.get_logger()


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for structured request/response logging."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID
        request_id = str(uuid.uuid4())
        
        # Add request ID to context
        with structlog.contextvars.bound_contextvars(request_id=request_id):
            start_time = time.time()
            
            # Log request
            logger.info(
                "Request started",
                method=request.method,
                url=str(request.url),
                client_ip=request.client.host if request.client else None,
                user_agent=request.headers.get("user-agent")
            )
            
            # Process request
            try:
                response = await call_next(request)
                
                # Calculate processing time
                processing_time = time.time() - start_time
                
                # Log response
                logger.info(
                    "Request completed",
                    status_code=response.status_code,
                    processing_time_ms=processing_time * 1000
                )
                
                # Add request ID to response headers
                response.headers["X-Request-ID"] = request_id
                response.headers["X-Processing-Time"] = f"{processing_time:.3f}s"
                
                return response
                
            except Exception as e:
                processing_time = time.time() - start_time
                
                logger.error(
                    "Request failed",
                    error=str(e),
                    processing_time_ms=processing_time * 1000,
                    exc_info=True
                )
                
                raise


class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting request metrics."""
    
    def __init__(self, app):
        super().__init__(app)
        self.request_count = 0
        self.graph_operations = 0
        self.curvature_calculations = 0
        self.error_count = 0
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        try:
            response = await call_next(request)
            
            # Record metrics
            processing_time = time.time() - start_time
            self.request_count += 1
            
            # Track specific operations
            path = str(request.url.path)
            if "/graph/" in path or "/nodes/" in path or "/edges/" in path:
                self.graph_operations += 1
            elif "/curvature/" in path:
                self.curvature_calculations += 1
            
            # Add metrics headers for debugging
            response.headers["X-Request-Count"] = str(self.request_count)
            response.headers["X-Graph-Operations"] = str(self.graph_operations)
            
            return response
            
        except Exception as e:
            self.error_count += 1
            raise
