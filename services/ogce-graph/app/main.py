"""
OGCE Graph Service - OnionGraphContextEngine Integration

This service provides temporal graph database capabilities using the OnionGraphContextEngine
for storing and analyzing medical data with Ricci curvature-based anomaly detection.
"""

import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON>AP<PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import structlog

from .config import get_settings
from .routers import graph, nodes, edges, temporal, curvature, health, websocket
from .middleware import LoggingMiddleware, MetricsMiddleware
from .auth import get_current_user
from .graph_manager import GraphManager

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    settings = get_settings()
    
    # Startup
    logger.info("Starting OGCE Graph Service", version="0.1.0")
    
    # Initialize Graph Manager with OnionGraphContextEngine
    try:
        graph_manager = GraphManager(settings)
        await graph_manager.initialize()
        app.state.graph_manager = graph_manager
        logger.info("OnionGraphContextEngine initialized successfully")
    except Exception as e:
        logger.error("Failed to initialize OnionGraphContextEngine", error=str(e))
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down OGCE Graph Service")
    if hasattr(app.state, 'graph_manager'):
        await app.state.graph_manager.cleanup()


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title="SymptomOS OGCE Graph Service",
        description="Temporal graph database service using OnionGraphContextEngine for medical data analysis",
        version="0.1.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.allowed_hosts
    )
    
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(MetricsMiddleware)
    
    # Include routers
    app.include_router(health.router, prefix="/healthz", tags=["health"])
    app.include_router(
        graph.router, 
        prefix="/v1/graph", 
        tags=["graph"],
        dependencies=[Depends(get_current_user)] if not settings.debug else []
    )
    app.include_router(
        nodes.router, 
        prefix="/v1/nodes", 
        tags=["nodes"],
        dependencies=[Depends(get_current_user)] if not settings.debug else []
    )
    app.include_router(
        edges.router, 
        prefix="/v1/edges", 
        tags=["edges"],
        dependencies=[Depends(get_current_user)] if not settings.debug else []
    )
    app.include_router(
        temporal.router, 
        prefix="/v1/temporal", 
        tags=["temporal"],
        dependencies=[Depends(get_current_user)] if not settings.debug else []
    )
    app.include_router(
        curvature.router,
        prefix="/v1/curvature",
        tags=["curvature"],
        dependencies=[Depends(get_current_user)] if not settings.debug else []
    )
    app.include_router(
        websocket.router,
        prefix="/ws/graph",
        tags=["websocket"]
    )
    
    # Global exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        logger.error(
            "Unhandled exception",
            path=request.url.path,
            method=request.method,
            error=str(exc),
            exc_info=True
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Internal server error"}
        )
    
    # Root endpoint
    @app.get("/")
    async def root():
        return {
            "service": "SymptomOS OGCE Graph Service",
            "version": "0.1.0",
            "status": "healthy",
            "description": "Temporal graph database with OnionGraphContextEngine",
            "endpoints": {
                "health": "/healthz",
                "graph_operations": "/v1/graph",
                "nodes": "/v1/nodes",
                "edges": "/v1/edges",
                "temporal_layers": "/v1/temporal",
                "curvature_analysis": "/v1/curvature",
                "docs": "/docs" if settings.debug else "disabled"
            },
            "features": [
                "Temporal graph storage",
                "Ricci curvature analysis",
                "Medical entity relationships",
                "Anomaly detection",
                "Time-based queries",
                "CRDT synchronization"
            ]
        }
    
    return app


# Create the app instance
app = create_app()

if __name__ == "__main__":
    import uvicorn
    settings = get_settings()
    
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_config=None,  # Use structlog instead
    )
