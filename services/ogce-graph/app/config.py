"""
Configuration settings for OGCE Graph Service
"""

from functools import lru_cache
from typing import List, Optional, Dict, Any
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )
    
    # Application settings
    app_name: str = "SymptomOS OGCE Graph Service"
    debug: bool = Field(default=False, description="Enable debug mode")
    host: str = Field(default="0.0.0.0", description="Host to bind to")
    port: int = Field(default=8002, description="Port to bind to")
    
    # Security settings
    secret_key: str = Field(..., description="Secret key for JWT tokens")
    algorithm: str = Field(default="HS256", description="JWT algorithm")
    access_token_expire_minutes: int = Field(default=30, description="Token expiration time")
    
    # CORS settings
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:5173", "http://localhost:8000", "http://localhost:8001"],
        description="Allowed CORS origins"
    )
    allowed_hosts: List[str] = Field(
        default=["localhost", "127.0.0.1", "0.0.0.0"],
        description="Allowed hosts"
    )
    
    # External service URLs
    granite_gateway_url: str = Field(
        default="http://localhost:8000",
        description="Granite Gateway service URL"
    )
    parser_service_url: str = Field(
        default="http://localhost:8001",
        description="Parser service URL"
    )
    
    # Database settings
    database_url: str = Field(
        default="postgresql+asyncpg://postgres:password@localhost:5432/symptomos_graph",
        description="Database URL for persistence"
    )
    redis_url: str = Field(
        default="redis://localhost:6379",
        description="Redis URL for caching"
    )
    
    # OnionGraphContextEngine settings
    ogce_config: Dict[str, Any] = Field(
        default={
            "temporal_layers": True,
            "curvature_analysis": True,
            "crdt_sync": True,
            "persistence": True,
            "max_nodes": 100000,
            "max_edges": 500000
        },
        description="OnionGraphContextEngine configuration"
    )
    
    # Graph processing settings
    max_nodes_per_query: int = Field(default=1000, description="Maximum nodes per query")
    max_edges_per_query: int = Field(default=5000, description="Maximum edges per query")
    query_timeout: int = Field(default=30, description="Query timeout in seconds")
    
    # Curvature analysis settings
    curvature_calculation_method: str = Field(
        default="ricci",
        description="Curvature calculation method (ricci, scalar, gaussian)"
    )
    anomaly_threshold: float = Field(
        default=0.5,
        description="Threshold for anomaly detection based on curvature"
    )
    enable_real_time_analysis: bool = Field(
        default=True,
        description="Enable real-time curvature analysis"
    )
    
    # Temporal settings
    temporal_layer_duration: int = Field(
        default=3600,
        description="Duration of each temporal layer in seconds (1 hour)"
    )
    max_temporal_layers: int = Field(
        default=168,
        description="Maximum number of temporal layers to keep (1 week)"
    )
    enable_temporal_compression: bool = Field(
        default=True,
        description="Enable compression of old temporal layers"
    )
    
    # Cache settings
    enable_cache: bool = Field(default=True, description="Enable result caching")
    cache_ttl: int = Field(default=300, description="Cache TTL in seconds")
    cache_max_size: int = Field(default=1000, description="Maximum cache entries")
    
    # Timeout settings
    request_timeout: int = Field(default=30, description="Request timeout in seconds")
    graph_operation_timeout: int = Field(default=60, description="Graph operation timeout in seconds")
    
    # Monitoring settings
    enable_metrics: bool = Field(default=True, description="Enable Prometheus metrics")
    metrics_port: int = Field(default=8012, description="Metrics server port")
    
    # Logging settings
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format (json or text)")
    
    # Medical entity settings
    medical_entity_types: List[str] = Field(
        default=["patient", "symptom", "medication", "diagnosis", "vital_sign", "treatment"],
        description="Supported medical entity types"
    )
    
    # Relationship types
    medical_relationship_types: List[str] = Field(
        default=["has_symptom", "takes_medication", "diagnosed_with", "treated_with", "related_to", "caused_by"],
        description="Supported medical relationship types"
    )
    
    # Synchronization settings
    enable_crdt_sync: bool = Field(default=True, description="Enable CRDT-based synchronization")
    sync_interval: int = Field(default=60, description="Synchronization interval in seconds")
    conflict_resolution: str = Field(
        default="timestamp",
        description="Conflict resolution strategy (timestamp, vector_clock, manual)"
    )
    
    # Rate limiting
    rate_limit_requests: int = Field(default=500, description="Requests per minute")
    rate_limit_window: int = Field(default=60, description="Rate limit window in seconds")
    
    # Health check settings
    health_check_interval: int = Field(default=30, description="Health check interval in seconds")
    
    @property
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return not self.debug
    
    def get_ogce_config(self) -> Dict[str, Any]:
        """Get OnionGraphContextEngine configuration."""
        return {
            **self.ogce_config,
            "database_url": self.database_url,
            "redis_url": self.redis_url,
            "temporal_layer_duration": self.temporal_layer_duration,
            "max_temporal_layers": self.max_temporal_layers,
            "curvature_method": self.curvature_calculation_method,
            "anomaly_threshold": self.anomaly_threshold,
            "enable_crdt": self.enable_crdt_sync
        }
    
    def get_graph_limits(self) -> Dict[str, int]:
        """Get graph operation limits."""
        return {
            "max_nodes_per_query": self.max_nodes_per_query,
            "max_edges_per_query": self.max_edges_per_query,
            "query_timeout": self.query_timeout,
            "max_nodes": self.ogce_config.get("max_nodes", 100000),
            "max_edges": self.ogce_config.get("max_edges", 500000)
        }
    
    def get_medical_config(self) -> Dict[str, List[str]]:
        """Get medical entity and relationship configuration."""
        return {
            "entity_types": self.medical_entity_types,
            "relationship_types": self.medical_relationship_types
        }


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()
