"""
Curvature Router - Ricci curvature analysis and anomaly detection

Handles curvature calculations and anomaly detection using the OnionGraphContextEngine.
"""

import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Request, Query
from pydantic import BaseModel, Field
import structlog

from ..config import get_settings
from ..graph_manager import GraphManager

logger = structlog.get_logger()

router = APIRouter()


class CurvatureRequest(BaseModel):
    """Request model for curvature calculation."""
    node_id: str = Field(..., description="Node ID to calculate curvature for")
    method: str = Field(default="ricci", description="Curvature calculation method")


class CurvatureResponse(BaseModel):
    """Response model for curvature calculation."""
    node_id: str = Field(description="Node identifier")
    curvature: float = Field(description="Calculated curvature value")
    method: str = Field(description="Calculation method used")
    processing_time_ms: float = Field(description="Processing time in milliseconds")
    timestamp: str = Field(description="Calculation timestamp")


class AnomalyDetectionRequest(BaseModel):
    """Request model for anomaly detection."""
    threshold: Optional[float] = Field(default=None, description="Anomaly threshold")
    entity_types: Optional[List[str]] = Field(default=None, description="Filter by entity types")
    patient_id: Optional[str] = Field(default=None, description="Filter by patient")
    time_range_hours: Optional[int] = Field(default=24, description="Time range for analysis")


class AnomalyResponse(BaseModel):
    """Response model for detected anomalies."""
    anomaly_id: str = Field(description="Anomaly identifier")
    node_id: str = Field(description="Affected node ID")
    anomaly_type: str = Field(description="Type of anomaly")
    severity: float = Field(description="Anomaly severity score")
    confidence: float = Field(description="Detection confidence")
    description: str = Field(description="Anomaly description")
    detected_at: str = Field(description="Detection timestamp")
    metadata: Dict[str, Any] = Field(default={}, description="Additional metadata")


class AnomalyDetectionResponse(BaseModel):
    """Response model for anomaly detection."""
    success: bool = Field(description="Whether detection was successful")
    anomalies: List[AnomalyResponse] = Field(description="Detected anomalies")
    anomaly_count: int = Field(description="Total number of anomalies")
    threshold_used: float = Field(description="Threshold used for detection")
    processing_time_ms: float = Field(description="Processing time in milliseconds")


class TemporalCurvatureRequest(BaseModel):
    """Request model for temporal curvature analysis."""
    start_time: datetime = Field(..., description="Start time for analysis")
    end_time: datetime = Field(..., description="End time for analysis")
    node_id: Optional[str] = Field(default=None, description="Specific node to analyze")
    patient_id: Optional[str] = Field(default=None, description="Patient to analyze")


class TemporalCurvatureResponse(BaseModel):
    """Response model for temporal curvature analysis."""
    start_time: str = Field(description="Analysis start time")
    end_time: str = Field(description="Analysis end time")
    curvature_trend: str = Field(description="Overall curvature trend")
    anomaly_count: int = Field(description="Number of anomalies detected")
    average_curvature: float = Field(description="Average curvature over time")
    curvature_variance: float = Field(description="Curvature variance")
    processing_time_ms: float = Field(description="Processing time in milliseconds")
    metadata: Dict[str, Any] = Field(default={}, description="Additional analysis data")


def get_graph_manager(request: Request) -> GraphManager:
    """Get the graph manager from app state."""
    return request.app.state.graph_manager


@router.post("/calculate", response_model=CurvatureResponse)
async def calculate_curvature(
    request: CurvatureRequest,
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """
    Calculate Ricci curvature for a specific node.
    
    Computes the curvature value which can indicate anomalous patterns
    in the medical data graph structure.
    """
    try:
        logger.info(
            "Calculating node curvature",
            node_id=request.node_id,
            method=request.method
        )
        
        start_time = time.time()
        
        # Calculate curvature using the graph manager
        curvature = await graph_manager.calculate_node_curvature(request.node_id)
        
        processing_time = (time.time() - start_time) * 1000
        
        response = CurvatureResponse(
            node_id=request.node_id,
            curvature=curvature,
            method=request.method,
            processing_time_ms=processing_time,
            timestamp=datetime.now().isoformat()
        )
        
        logger.info(
            "Curvature calculated",
            node_id=request.node_id,
            curvature=curvature,
            processing_time_ms=processing_time
        )
        
        return response
        
    except Exception as e:
        logger.error("Failed to calculate curvature", node_id=request.node_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Curvature calculation failed: {str(e)}")


@router.post("/detect-anomalies", response_model=AnomalyDetectionResponse)
async def detect_anomalies(
    request: AnomalyDetectionRequest,
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """
    Detect anomalies in the graph using curvature analysis.
    
    Identifies unusual patterns in medical data that may indicate
    important clinical insights or data quality issues.
    """
    settings = get_settings()
    
    try:
        threshold = request.threshold or settings.anomaly_threshold
        
        logger.info(
            "Detecting anomalies",
            threshold=threshold,
            entity_types=request.entity_types,
            patient_id=request.patient_id,
            time_range_hours=request.time_range_hours
        )
        
        start_time = time.time()
        
        # Detect anomalies using the graph manager
        anomalies = await graph_manager.detect_anomalies(threshold)
        
        # Filter anomalies based on request parameters
        filtered_anomalies = []
        for anomaly in anomalies:
            # Filter by entity types if specified
            if request.entity_types:
                node_data = await graph_manager.get_node(anomaly["node_id"])
                if node_data:
                    entity_type = node_data.get("data", {}).get("entity_type")
                    if entity_type not in request.entity_types:
                        continue
            
            # Filter by patient if specified
            if request.patient_id:
                node_data = await graph_manager.get_node(anomaly["node_id"])
                if node_data:
                    patient_id = node_data.get("data", {}).get("patient_id")
                    if patient_id != request.patient_id:
                        continue
            
            # Filter by time range if specified
            if request.time_range_hours:
                anomaly_time = datetime.fromisoformat(anomaly.get("detected_at", datetime.now().isoformat()))
                cutoff_time = datetime.now() - timedelta(hours=request.time_range_hours)
                if anomaly_time < cutoff_time:
                    continue
            
            # Convert to response format
            anomaly_response = AnomalyResponse(
                anomaly_id=anomaly.get("id", f"anomaly_{len(filtered_anomalies)}"),
                node_id=anomaly["node_id"],
                anomaly_type=anomaly.get("anomaly_type", "curvature_anomaly"),
                severity=anomaly.get("severity", 0.0),
                confidence=anomaly.get("confidence", 0.0),
                description=anomaly.get("description", "Curvature-based anomaly detected"),
                detected_at=anomaly.get("detected_at", datetime.now().isoformat()),
                metadata=anomaly.get("metadata", {})
            )
            filtered_anomalies.append(anomaly_response)
        
        processing_time = (time.time() - start_time) * 1000
        
        response = AnomalyDetectionResponse(
            success=True,
            anomalies=filtered_anomalies,
            anomaly_count=len(filtered_anomalies),
            threshold_used=threshold,
            processing_time_ms=processing_time
        )
        
        logger.info(
            "Anomaly detection completed",
            anomalies_found=len(filtered_anomalies),
            threshold=threshold,
            processing_time_ms=processing_time
        )
        
        return response
        
    except Exception as e:
        logger.error("Failed to detect anomalies", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=f"Anomaly detection failed: {str(e)}")


@router.post("/temporal-analysis", response_model=TemporalCurvatureResponse)
async def analyze_temporal_curvature(
    request: TemporalCurvatureRequest,
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """
    Analyze curvature changes over time.
    
    Provides insights into how the graph structure and anomalies
    evolve over time, useful for tracking disease progression.
    """
    try:
        logger.info(
            "Analyzing temporal curvature",
            start_time=request.start_time.isoformat(),
            end_time=request.end_time.isoformat(),
            node_id=request.node_id,
            patient_id=request.patient_id
        )
        
        start_time = time.time()
        
        # Perform temporal curvature analysis
        analysis = await graph_manager.analyze_temporal_curvature(
            request.start_time,
            request.end_time
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        response = TemporalCurvatureResponse(
            start_time=request.start_time.isoformat(),
            end_time=request.end_time.isoformat(),
            curvature_trend=analysis.get("curvature_trend", "stable"),
            anomaly_count=analysis.get("anomaly_count", 0),
            average_curvature=analysis.get("average_curvature", 0.0),
            curvature_variance=analysis.get("curvature_variance", 0.0),
            processing_time_ms=processing_time,
            metadata=analysis
        )
        
        logger.info(
            "Temporal curvature analysis completed",
            trend=response.curvature_trend,
            anomaly_count=response.anomaly_count,
            processing_time_ms=processing_time
        )
        
        return response
        
    except Exception as e:
        logger.error("Failed to analyze temporal curvature", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=f"Temporal curvature analysis failed: {str(e)}")


@router.get("/patient/{patient_id}/anomalies")
async def get_patient_anomalies(
    patient_id: str,
    hours: int = Query(default=24, description="Time range in hours"),
    threshold: Optional[float] = Query(default=None, description="Anomaly threshold"),
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """
    Get anomalies for a specific patient.
    
    Returns all anomalies detected in the patient's medical data graph.
    """
    try:
        logger.info("Getting patient anomalies", patient_id=patient_id, hours=hours)
        
        # Create anomaly detection request for this patient
        request = AnomalyDetectionRequest(
            threshold=threshold,
            patient_id=patient_id,
            time_range_hours=hours
        )
        
        # Use the existing anomaly detection logic
        response = await detect_anomalies(request, graph_manager)
        
        logger.info(
            "Patient anomalies retrieved",
            patient_id=patient_id,
            anomalies_found=response.anomaly_count
        )
        
        return {
            "patient_id": patient_id,
            "time_range_hours": hours,
            "anomalies": response.anomalies,
            "anomaly_count": response.anomaly_count,
            "threshold_used": response.threshold_used
        }
        
    except Exception as e:
        logger.error("Failed to get patient anomalies", patient_id=patient_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get patient anomalies: {str(e)}")


@router.get("/distribution")
async def get_curvature_distribution(
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """
    Get the distribution of curvature values across the graph.
    
    Provides statistical insights into the overall graph structure.
    """
    try:
        logger.info("Getting curvature distribution")
        
        start_time = time.time()
        
        # Get curvature distribution from the analyzer
        if graph_manager.curvature_analyzer:
            distribution = graph_manager.curvature_analyzer.get_curvature_distribution()
        else:
            distribution = {
                "mean": 0.0,
                "std": 0.0,
                "min": 0.0,
                "max": 0.0,
                "count": 0
            }
        
        processing_time = (time.time() - start_time) * 1000
        
        logger.info(
            "Curvature distribution retrieved",
            mean=distribution.get("mean", 0.0),
            count=distribution.get("count", 0),
            processing_time_ms=processing_time
        )
        
        return {
            "distribution": distribution,
            "processing_time_ms": processing_time,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get curvature distribution", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get curvature distribution: {str(e)}")


@router.get("/health")
async def curvature_health_check(graph_manager: GraphManager = Depends(get_graph_manager)):
    """Health check for curvature analysis service."""
    try:
        # Test curvature analysis functionality
        analyzer_available = graph_manager.curvature_analyzer is not None
        
        return {
            "service": "Curvature Analysis",
            "status": "healthy",
            "curvature_analyzer_available": analyzer_available,
            "graph_engine_initialized": graph_manager.initialized,
            "methods_supported": ["ricci", "scalar", "gaussian"]
        }
    except Exception as e:
        logger.error("Curvature health check failed", error=str(e))
        return {
            "service": "Curvature Analysis",
            "status": "unhealthy",
            "error": str(e)
        }
