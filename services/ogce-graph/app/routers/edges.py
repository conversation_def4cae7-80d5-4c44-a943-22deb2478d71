"""
Edges Router - Edge operations for the graph

Handles CRUD operations for graph edges/relationships.
"""

import time
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Request, Query
from pydantic import BaseModel, Field
import structlog

from ..graph_manager import GraphManager

logger = structlog.get_logger()
router = APIRouter()


class EdgeRequest(BaseModel):
    """Request model for edge operations."""
    source_id: str = Field(..., description="Source node ID")
    target_id: str = Field(..., description="Target node ID")
    relationship_type: str = Field(..., description="Type of relationship")
    properties: Optional[Dict[str, Any]] = Field(default={}, description="Edge properties")


class EdgeResponse(BaseModel):
    """Response model for edge operations."""
    success: bool = Field(description="Whether operation was successful")
    source_id: str = Field(description="Source node ID")
    target_id: str = Field(description="Target node ID")
    relationship_type: str = Field(description="Type of relationship")
    properties: Dict[str, Any] = Field(description="Edge properties")
    processing_time_ms: float = Field(description="Processing time in milliseconds")


def get_graph_manager(request: Request) -> GraphManager:
    """Get the graph manager from app state."""
    return request.app.state.graph_manager


@router.post("/", response_model=EdgeResponse)
async def create_edge(
    request: EdgeRequest,
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """Create a new edge in the graph."""
    try:
        start_time = time.time()
        
        success = await graph_manager.add_edge(
            request.source_id,
            request.target_id,
            request.relationship_type,
            request.properties
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        return EdgeResponse(
            success=success,
            source_id=request.source_id,
            target_id=request.target_id,
            relationship_type=request.relationship_type,
            properties=request.properties or {},
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        logger.error("Failed to create edge", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/")
async def query_edges(
    source_id: Optional[str] = Query(default=None),
    target_id: Optional[str] = Query(default=None),
    relationship_type: Optional[str] = Query(default=None),
    limit: int = Query(default=100, le=1000),
    offset: int = Query(default=0),
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """Query edges with filters."""
    try:
        filters = {}
        if source_id:
            filters["source"] = source_id
        if target_id:
            filters["target"] = target_id
        if relationship_type:
            filters["relationship"] = relationship_type
            
        edges = await graph_manager.query_edges(filters, limit, offset)
        return {"edges": edges, "count": len(edges)}
        
    except Exception as e:
        logger.error("Failed to query edges", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))
