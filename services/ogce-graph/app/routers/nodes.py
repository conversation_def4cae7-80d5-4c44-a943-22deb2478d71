"""
Nodes Router - Node operations for the graph

Handles CRUD operations for graph nodes.
"""

import time
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Request, Query
from pydantic import BaseModel, Field
import structlog

from ..graph_manager import GraphManager

logger = structlog.get_logger()
router = APIRouter()


class NodeRequest(BaseModel):
    """Request model for node operations."""
    node_id: str = Field(..., description="Node identifier")
    node_type: str = Field(..., description="Type of node")
    properties: Dict[str, Any] = Field(..., description="Node properties")


class NodeResponse(BaseModel):
    """Response model for node operations."""
    success: bool = Field(description="Whether operation was successful")
    node_id: str = Field(description="Node identifier")
    node_type: str = Field(description="Type of node")
    properties: Dict[str, Any] = Field(description="Node properties")
    processing_time_ms: float = Field(description="Processing time in milliseconds")


def get_graph_manager(request: Request) -> GraphManager:
    """Get the graph manager from app state."""
    return request.app.state.graph_manager


@router.post("/", response_model=NodeResponse)
async def create_node(
    request: NodeRequest,
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """Create a new node in the graph."""
    try:
        start_time = time.time()
        
        success = await graph_manager.add_node(
            request.node_id,
            request.node_type,
            request.properties
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        return NodeResponse(
            success=success,
            node_id=request.node_id,
            node_type=request.node_type,
            properties=request.properties,
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        logger.error("Failed to create node", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{node_id}")
async def get_node(
    node_id: str,
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """Get a node by ID."""
    try:
        node = await graph_manager.get_node(node_id)
        if not node:
            raise HTTPException(status_code=404, detail="Node not found")
        return node
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get node", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/")
async def query_nodes(
    node_type: Optional[str] = Query(default=None),
    limit: int = Query(default=100, le=1000),
    offset: int = Query(default=0),
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """Query nodes with filters."""
    try:
        filters = {}
        if node_type:
            filters["node_type"] = node_type
            
        nodes = await graph_manager.query_nodes(filters, limit, offset)
        return {"nodes": nodes, "count": len(nodes)}
        
    except Exception as e:
        logger.error("Failed to query nodes", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))
