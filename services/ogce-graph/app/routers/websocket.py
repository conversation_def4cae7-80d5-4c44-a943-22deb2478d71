"""
WebSocket Router - Real-time Graph Updates

This module provides WebSocket endpoints for real-time graph updates,
allowing clients to receive live notifications when nodes or edges are added/modified.
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Set, Any, Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
import structlog

from ..graph_manager import GraphManager
from ..auth import get_current_user_ws

logger = structlog.get_logger()

router = APIRouter()

# Connection manager for WebSocket clients
class ConnectionManager:
    """Manages WebSocket connections and broadcasts updates."""
    
    def __init__(self):
        # Active connections by patient_id
        self.patient_connections: Dict[str, Set[WebSocket]] = {}
        # Global connections (receive all updates)
        self.global_connections: Set[WebSocket] = set()
        # Connection metadata
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}
    
    async def connect(self, websocket: WebSocket, patient_id: Optional[str] = None):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        
        # Store connection metadata
        self.connection_metadata[websocket] = {
            "patient_id": patient_id,
            "connected_at": datetime.now(),
            "last_ping": time.time()
        }
        
        if patient_id:
            # Patient-specific connection
            if patient_id not in self.patient_connections:
                self.patient_connections[patient_id] = set()
            self.patient_connections[patient_id].add(websocket)
            logger.info("WebSocket connected for patient", patient_id=patient_id)
        else:
            # Global connection
            self.global_connections.add(websocket)
            logger.info("Global WebSocket connected")
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        metadata = self.connection_metadata.get(websocket, {})
        patient_id = metadata.get("patient_id")
        
        if patient_id and patient_id in self.patient_connections:
            self.patient_connections[patient_id].discard(websocket)
            if not self.patient_connections[patient_id]:
                del self.patient_connections[patient_id]
            logger.info("WebSocket disconnected for patient", patient_id=patient_id)
        else:
            self.global_connections.discard(websocket)
            logger.info("Global WebSocket disconnected")
        
        # Clean up metadata
        self.connection_metadata.pop(websocket, None)
    
    async def send_to_patient(self, patient_id: str, message: Dict[str, Any]):
        """Send message to all connections for a specific patient."""
        if patient_id not in self.patient_connections:
            return
        
        disconnected = set()
        for websocket in self.patient_connections[patient_id]:
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.warning("Failed to send message to patient connection", 
                             patient_id=patient_id, error=str(e))
                disconnected.add(websocket)
        
        # Clean up disconnected sockets
        for websocket in disconnected:
            self.disconnect(websocket)
    
    async def broadcast_global(self, message: Dict[str, Any]):
        """Send message to all global connections."""
        disconnected = set()
        for websocket in self.global_connections:
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.warning("Failed to send message to global connection", error=str(e))
                disconnected.add(websocket)
        
        # Clean up disconnected sockets
        for websocket in disconnected:
            self.disconnect(websocket)
    
    async def broadcast_update(self, update: Dict[str, Any]):
        """Broadcast an update to relevant connections."""
        patient_id = update.get("patient_id")
        
        # Send to global connections
        await self.broadcast_global(update)
        
        # Send to patient-specific connections if applicable
        if patient_id:
            await self.send_to_patient(patient_id, update)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get statistics about active connections."""
        total_patient_connections = sum(len(conns) for conns in self.patient_connections.values())
        return {
            "global_connections": len(self.global_connections),
            "patient_connections": total_patient_connections,
            "patients_with_connections": len(self.patient_connections),
            "total_connections": len(self.global_connections) + total_patient_connections
        }

# Global connection manager instance
manager = ConnectionManager()

@router.websocket("/updates")
async def websocket_graph_updates(
    websocket: WebSocket,
    patient_id: Optional[str] = Query(default=None, description="Patient ID to filter updates")
):
    """
    WebSocket endpoint for real-time graph updates.
    
    - If patient_id is provided, only receives updates for that patient
    - If no patient_id, receives all graph updates
    
    Message format:
    {
        "type": "node_added" | "node_updated" | "edge_added" | "edge_updated" | "node_deleted" | "edge_deleted",
        "timestamp": "2023-12-15T14:30:00Z",
        "patient_id": "patient_123",
        "data": {
            "node_id": "symptom_456",
            "node_type": "symptom",
            "properties": {...}
        }
    }
    """
    await manager.connect(websocket, patient_id)
    
    try:
        # Send initial connection confirmation
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "timestamp": datetime.now().isoformat(),
            "patient_id": patient_id,
            "message": f"Connected to graph updates{f' for patient {patient_id}' if patient_id else ' (global)'}"
        }))
        
        # Keep connection alive and handle ping/pong
        while True:
            try:
                # Wait for client messages (ping, etc.)
                data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                message = json.loads(data)
                
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    }))
                    
                    # Update last ping time
                    if websocket in manager.connection_metadata:
                        manager.connection_metadata[websocket]["last_ping"] = time.time()
                        
            except asyncio.TimeoutError:
                # Send periodic heartbeat
                await websocket.send_text(json.dumps({
                    "type": "heartbeat",
                    "timestamp": datetime.now().isoformat(),
                    "stats": manager.get_connection_stats()
                }))
                
    except WebSocketDisconnect:
        logger.info("WebSocket client disconnected", patient_id=patient_id)
    except Exception as e:
        logger.error("WebSocket error", patient_id=patient_id, error=str(e))
    finally:
        manager.disconnect(websocket)

@router.get("/connections/stats")
async def get_connection_stats():
    """Get statistics about active WebSocket connections."""
    return {
        "status": "active",
        "timestamp": datetime.now().isoformat(),
        **manager.get_connection_stats()
    }

# Helper function to broadcast updates (to be called from graph_manager)
async def broadcast_graph_update(
    update_type: str,
    patient_id: Optional[str],
    data: Dict[str, Any]
):
    """
    Broadcast a graph update to connected WebSocket clients.
    
    This function should be called from the GraphManager when nodes/edges are modified.
    """
    update_message = {
        "type": update_type,
        "timestamp": datetime.now().isoformat(),
        "patient_id": patient_id,
        "data": data
    }
    
    await manager.broadcast_update(update_message)
    
    logger.info(
        "Graph update broadcasted",
        update_type=update_type,
        patient_id=patient_id,
        active_connections=manager.get_connection_stats()["total_connections"]
    )
