"""
Graph Router - Main graph operations

Handles high-level graph operations including patient graphs and medical entity management.
"""

import time
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Request, Query
from pydantic import BaseModel, Field
import structlog

from ..config import get_settings
from ..graph_manager import GraphManager

logger = structlog.get_logger()

router = APIRouter()


class MedicalEntityRequest(BaseModel):
    """Request model for adding medical entities."""
    entity_type: str = Field(..., description="Type of medical entity")
    entity_data: Dict[str, Any] = Field(..., description="Entity data")
    patient_id: Optional[str] = Field(default=None, description="Associated patient ID")


class MedicalEntityResponse(BaseModel):
    """Response model for medical entity operations."""
    success: bool = Field(description="Whether operation was successful")
    entity_id: str = Field(description="Generated entity ID")
    entity_type: str = Field(description="Type of entity")
    patient_id: Optional[str] = Field(default=None, description="Associated patient ID")
    processing_time_ms: float = Field(description="Processing time in milliseconds")


class PatientGraphResponse(BaseModel):
    """Response model for patient graph."""
    patient_id: str = Field(description="Patient identifier")
    nodes: List[Dict[str, Any]] = Field(description="Graph nodes")
    edges: List[Dict[str, Any]] = Field(description="Graph edges")
    node_count: int = Field(description="Total number of nodes")
    edge_count: int = Field(description="Total number of edges")
    processing_time_ms: float = Field(description="Processing time in milliseconds")


class GraphStatsResponse(BaseModel):
    """Response model for graph statistics."""
    total_nodes: int = Field(description="Total number of nodes")
    total_edges: int = Field(description="Total number of edges")
    node_types: Dict[str, int] = Field(description="Count by node type")
    edge_types: Dict[str, int] = Field(description="Count by edge type")
    temporal_layers: int = Field(description="Number of temporal layers")
    last_updated: str = Field(description="Last update timestamp")


def get_graph_manager(request: Request) -> GraphManager:
    """Get the graph manager from app state."""
    return request.app.state.graph_manager


@router.post("/medical-entity", response_model=MedicalEntityResponse)
async def add_medical_entity(
    request: MedicalEntityRequest,
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """
    Add a medical entity to the graph.
    
    Creates a new medical entity (symptom, medication, diagnosis, etc.)
    and optionally links it to a patient.
    """
    settings = get_settings()
    
    # Validate entity type
    if request.entity_type not in settings.medical_entity_types:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid entity type. Allowed: {', '.join(settings.medical_entity_types)}"
        )
    
    try:
        logger.info(
            "Adding medical entity",
            entity_type=request.entity_type,
            patient_id=request.patient_id,
            data_keys=list(request.entity_data.keys())
        )
        
        start_time = time.time()
        
        entity_id = await graph_manager.add_medical_entity(
            entity_type=request.entity_type,
            entity_data=request.entity_data,
            patient_id=request.patient_id
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        response = MedicalEntityResponse(
            success=True,
            entity_id=entity_id,
            entity_type=request.entity_type,
            patient_id=request.patient_id,
            processing_time_ms=processing_time
        )
        
        logger.info(
            "Medical entity added successfully",
            entity_id=entity_id,
            processing_time_ms=processing_time
        )
        
        return response
        
    except Exception as e:
        logger.error("Failed to add medical entity", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to add medical entity: {str(e)}")


@router.get("/patient/{patient_id}", response_model=PatientGraphResponse)
async def get_patient_graph(
    patient_id: str,
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """
    Get the complete graph for a specific patient.
    
    Returns all nodes and edges associated with the patient,
    including symptoms, medications, diagnoses, and relationships.
    """
    try:
        logger.info("Retrieving patient graph", patient_id=patient_id)
        
        start_time = time.time()
        
        graph_data = await graph_manager.get_patient_graph(patient_id)
        
        processing_time = (time.time() - start_time) * 1000
        
        response = PatientGraphResponse(
            patient_id=patient_id,
            nodes=graph_data["nodes"],
            edges=graph_data["edges"],
            node_count=len(graph_data["nodes"]),
            edge_count=len(graph_data["edges"]),
            processing_time_ms=processing_time
        )
        
        logger.info(
            "Patient graph retrieved",
            patient_id=patient_id,
            node_count=response.node_count,
            edge_count=response.edge_count,
            processing_time_ms=processing_time
        )
        
        return response
        
    except Exception as e:
        logger.error("Failed to get patient graph", patient_id=patient_id, error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get patient graph: {str(e)}")


@router.get("/stats", response_model=GraphStatsResponse)
async def get_graph_statistics(
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """
    Get overall graph statistics.
    
    Returns counts of nodes, edges, types, and other graph metrics.
    """
    try:
        logger.info("Retrieving graph statistics")
        
        start_time = time.time()
        
        # Get all nodes and edges for statistics
        all_nodes = await graph_manager.query_nodes(limit=10000)  # Large limit for stats
        all_edges = await graph_manager.query_edges(limit=50000)  # Large limit for stats
        
        # Count by types
        node_types = {}
        for node in all_nodes:
            node_type = node.get("data", {}).get("entity_type", "unknown")
            node_types[node_type] = node_types.get(node_type, 0) + 1
        
        edge_types = {}
        for edge in all_edges:
            edge_type = edge.get("relationship", "unknown")
            edge_types[edge_type] = edge_types.get(edge_type, 0) + 1
        
        processing_time = (time.time() - start_time) * 1000
        
        response = GraphStatsResponse(
            total_nodes=len(all_nodes),
            total_edges=len(all_edges),
            node_types=node_types,
            edge_types=edge_types,
            temporal_layers=0,  # TODO: Get actual temporal layer count
            last_updated=datetime.now().isoformat()
        )
        
        logger.info(
            "Graph statistics retrieved",
            total_nodes=response.total_nodes,
            total_edges=response.total_edges,
            processing_time_ms=processing_time
        )
        
        return response
        
    except Exception as e:
        logger.error("Failed to get graph statistics", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get graph statistics: {str(e)}")


@router.post("/patient/{patient_id}/symptom")
async def add_patient_symptom(
    patient_id: str,
    symptom_name: str = Field(..., description="Symptom name"),
    severity: Optional[int] = Field(default=None, ge=1, le=10, description="Severity (1-10)"),
    location: Optional[str] = Field(default=None, description="Body location"),
    duration: Optional[str] = Field(default=None, description="Duration"),
    description: Optional[str] = Field(default=None, description="Additional description"),
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """
    Add a symptom to a patient's graph.
    
    Convenience endpoint for adding symptoms with medical-specific fields.
    """
    try:
        symptom_data = {
            "name": symptom_name,
            "severity": severity,
            "location": location,
            "duration": duration,
            "description": description,
            "timestamp": datetime.now().isoformat()
        }
        
        # Remove None values
        symptom_data = {k: v for k, v in symptom_data.items() if v is not None}
        
        entity_id = await graph_manager.add_medical_entity(
            entity_type="symptom",
            entity_data=symptom_data,
            patient_id=patient_id
        )
        
        logger.info(
            "Symptom added to patient",
            patient_id=patient_id,
            symptom_name=symptom_name,
            entity_id=entity_id
        )
        
        return {
            "success": True,
            "entity_id": entity_id,
            "patient_id": patient_id,
            "symptom_name": symptom_name
        }
        
    except Exception as e:
        logger.error("Failed to add patient symptom", patient_id=patient_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to add symptom: {str(e)}")


@router.post("/patient/{patient_id}/medication")
async def add_patient_medication(
    patient_id: str,
    medication_name: str = Field(..., description="Medication name"),
    dosage: Optional[str] = Field(default=None, description="Dosage amount"),
    frequency: Optional[str] = Field(default=None, description="Frequency"),
    route: Optional[str] = Field(default=None, description="Route of administration"),
    indication: Optional[str] = Field(default=None, description="What it's for"),
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """
    Add a medication to a patient's graph.
    
    Convenience endpoint for adding medications with medical-specific fields.
    """
    try:
        medication_data = {
            "name": medication_name,
            "dosage": dosage,
            "frequency": frequency,
            "route": route,
            "indication": indication,
            "timestamp": datetime.now().isoformat()
        }
        
        # Remove None values
        medication_data = {k: v for k, v in medication_data.items() if v is not None}
        
        entity_id = await graph_manager.add_medical_entity(
            entity_type="medication",
            entity_data=medication_data,
            patient_id=patient_id
        )
        
        logger.info(
            "Medication added to patient",
            patient_id=patient_id,
            medication_name=medication_name,
            entity_id=entity_id
        )
        
        return {
            "success": True,
            "entity_id": entity_id,
            "patient_id": patient_id,
            "medication_name": medication_name
        }
        
    except Exception as e:
        logger.error("Failed to add patient medication", patient_id=patient_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to add medication: {str(e)}")


@router.get("/search")
async def search_graph(
    query: str = Query(..., description="Search query"),
    entity_types: Optional[List[str]] = Query(default=None, description="Filter by entity types"),
    limit: int = Query(default=50, le=200, description="Maximum results"),
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """
    Search the graph for entities matching a query.
    
    Performs text-based search across node properties.
    """
    try:
        logger.info("Searching graph", query=query, entity_types=entity_types)
        
        # Build search filters
        filters = {}
        if entity_types:
            filters["entity_type"] = entity_types
        
        # Get nodes (in a real implementation, you'd do text search)
        nodes = await graph_manager.query_nodes(filters, limit=limit)
        
        # Simple text matching (replace with proper search in your OGContextEngine)
        matching_nodes = []
        query_lower = query.lower()
        
        for node in nodes:
            node_text = str(node.get("data", {})).lower()
            if query_lower in node_text:
                matching_nodes.append(node)
        
        logger.info(
            "Graph search completed",
            query=query,
            total_checked=len(nodes),
            matches_found=len(matching_nodes)
        )
        
        return {
            "query": query,
            "results": matching_nodes,
            "total_results": len(matching_nodes),
            "entity_types_filter": entity_types
        }
        
    except Exception as e:
        logger.error("Failed to search graph", query=query, error=str(e))
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.get("/health")
async def graph_health_check(graph_manager: GraphManager = Depends(get_graph_manager)):
    """Health check for graph service."""
    try:
        # Test basic graph functionality
        test_nodes = await graph_manager.query_nodes(limit=1)
        
        return {
            "service": "Graph",
            "status": "healthy",
            "graph_engine_initialized": graph_manager.initialized,
            "can_query_nodes": True,
            "sample_node_count": len(test_nodes)
        }
    except Exception as e:
        logger.error("Graph health check failed", error=str(e))
        return {
            "service": "Graph",
            "status": "unhealthy",
            "error": str(e)
        }
