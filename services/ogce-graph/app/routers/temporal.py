"""
Temporal Router - Temporal layer operations

Handles temporal graph operations and time-based queries.
"""

import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Request, Query
from pydantic import BaseModel, Field
import structlog

from ..graph_manager import GraphManager

logger = structlog.get_logger()
router = APIRouter()


class TemporalLayerResponse(BaseModel):
    """Response model for temporal layer."""
    layer_id: str = Field(description="Layer identifier")
    timestamp: str = Field(description="Layer timestamp")
    node_count: int = Field(description="Number of nodes in layer")
    edge_count: int = Field(description="Number of edges in layer")
    processing_time_ms: float = Field(description="Processing time in milliseconds")


def get_graph_manager(request: Request) -> GraphManager:
    """Get the graph manager from app state."""
    return request.app.state.graph_manager


@router.get("/layer")
async def get_temporal_layer(
    timestamp: datetime = Query(..., description="Timestamp for layer"),
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """Get temporal layer for a specific timestamp."""
    try:
        start_time = time.time()
        
        layer = await graph_manager.get_temporal_layer(timestamp)
        
        processing_time = (time.time() - start_time) * 1000
        
        if not layer:
            raise HTTPException(status_code=404, detail="Temporal layer not found")
        
        return TemporalLayerResponse(
            layer_id=layer.get("layer_id", "unknown"),
            timestamp=timestamp.isoformat(),
            node_count=layer.get("node_count", 0),
            edge_count=layer.get("edge_count", 0),
            processing_time_ms=processing_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get temporal layer", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/range")
async def get_temporal_range(
    start_time: datetime = Query(..., description="Start time"),
    end_time: datetime = Query(..., description="End time"),
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """Get temporal layers in a time range."""
    try:
        layers = await graph_manager.get_temporal_range(start_time, end_time)
        return {"layers": layers, "count": len(layers)}
        
    except Exception as e:
        logger.error("Failed to get temporal range", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))
