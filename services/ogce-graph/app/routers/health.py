"""
Health Check Router for OGCE Graph Service

Provides health check endpoints for monitoring and load balancing.
"""

import time
import psutil
from typing import Dict, Any
from fastapi import APIRouter, Depends, Request
from pydantic import BaseModel
import structlog

from ..config import get_settings
from ..graph_manager import GraphManager

logger = structlog.get_logger()
router = APIRouter()


class HealthStatus(BaseModel):
    """Health status response model."""
    status: str
    timestamp: float
    version: str
    uptime_seconds: float


class DetailedHealthStatus(BaseModel):
    """Detailed health status with system metrics."""
    status: str
    timestamp: float
    version: str
    uptime_seconds: float
    system: Dict[str, Any]
    graph_engine: Dict[str, Any]
    configuration: Dict[str, Any]


# Track service start time
SERVICE_START_TIME = time.time()


def get_graph_manager(request: Request) -> GraphManager:
    """Get the graph manager from app state."""
    return getattr(request.app.state, 'graph_manager', None)


@router.get("/", response_model=HealthStatus)
async def health_check():
    """Basic health check endpoint."""
    current_time = time.time()
    uptime = current_time - SERVICE_START_TIME
    
    return HealthStatus(
        status="healthy",
        timestamp=current_time,
        version="0.1.0",
        uptime_seconds=uptime
    )


@router.get("/detailed", response_model=DetailedHealthStatus)
async def detailed_health_check(
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """Detailed health check with system metrics and graph engine status."""
    settings = get_settings()
    current_time = time.time()
    uptime = current_time - SERVICE_START_TIME
    
    # Get system metrics
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        system_metrics = {
            "cpu_usage_percent": cpu_percent,
            "memory_usage_percent": memory.percent,
            "available_memory_gb": round(memory.available / (1024**3), 2)
        }
    except:
        system_metrics = {"error": "Unable to retrieve system metrics"}
    
    # Check graph engine status
    graph_status = {}
    if graph_manager:
        try:
            # Test basic graph functionality
            test_nodes = await graph_manager.query_nodes(limit=1)
            
            graph_status = {
                "initialized": graph_manager.initialized,
                "graph_engine_available": graph_manager.graph_engine is not None,
                "curvature_analyzer_available": graph_manager.curvature_analyzer is not None,
                "can_query_nodes": True,
                "status": "healthy"
            }
        except Exception as e:
            graph_status = {
                "initialized": False,
                "status": "unhealthy",
                "error": str(e)
            }
    else:
        graph_status = {
            "initialized": False,
            "status": "not_initialized"
        }
    
    # Configuration status
    config_status = {
        "debug_mode": settings.debug,
        "environment": "development" if settings.debug else "production",
        "ogce_config": settings.get_ogce_config(),
        "medical_entity_types": settings.medical_entity_types,
        "curvature_method": settings.curvature_calculation_method
    }
    
    # Determine overall status
    overall_status = "healthy"
    if graph_status.get("status") != "healthy":
        overall_status = "degraded"
    
    return DetailedHealthStatus(
        status=overall_status,
        timestamp=current_time,
        version="0.1.0",
        uptime_seconds=uptime,
        system=system_metrics,
        graph_engine=graph_status,
        configuration=config_status
    )


@router.get("/ready")
async def readiness_check(
    graph_manager: GraphManager = Depends(get_graph_manager)
):
    """Readiness check for Kubernetes deployments."""
    try:
        if not graph_manager or not graph_manager.initialized:
            return {"status": "not_ready", "reason": "Graph engine not initialized"}, 503
        
        # Quick functionality test
        test_nodes = await graph_manager.query_nodes(limit=1)
        
        return {"status": "ready", "timestamp": time.time()}
        
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        return {"status": "not_ready", "reason": str(e)}, 503


@router.get("/live")
async def liveness_check():
    """Liveness check for Kubernetes deployments."""
    try:
        current_time = time.time()
        uptime = current_time - SERVICE_START_TIME
        
        # Check system resources
        try:
            memory = psutil.virtual_memory()
            if memory.percent > 95:
                logger.error("Critical memory usage detected", memory_percent=memory.percent)
                return {"status": "unhealthy", "reason": "Critical memory usage"}, 503
        except:
            pass
        
        return {"status": "alive", "timestamp": current_time, "uptime_seconds": uptime}
        
    except Exception as e:
        logger.error("Liveness check failed", error=str(e))
        return {"status": "dead", "reason": str(e)}, 503


@router.get("/version")
async def version_info():
    """Service version information."""
    return {
        "service": "SymptomOS OGCE Graph Service",
        "version": "0.1.0",
        "build_time": "2024-01-15T00:00:00Z",
        "git_commit": "unknown",
        "python_version": "3.11+",
        "dependencies": {
            "fastapi": "0.109.0",
            "networkx": "3.2.1",
            "numpy": "1.26.0",
            "ogcontextengine": "local"
        }
    }
