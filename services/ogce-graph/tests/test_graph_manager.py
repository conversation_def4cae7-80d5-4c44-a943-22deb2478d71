"""
Tests for Graph Manager
"""

import pytest
from datetime import datetime
from app.graph_manager import GraphManager
from app.config import Settings


@pytest.fixture
def settings():
    """Create test settings."""
    return Settings(
        secret_key="test-secret-key",
        database_url="sqlite:///test.db",
        redis_url="redis://localhost:6379/1",
        debug=True
    )


@pytest.fixture
async def graph_manager(settings):
    """Create and initialize graph manager for testing."""
    manager = GraphManager(settings)
    # Note: This will fail until you implement the actual OGContextEngine
    # For now, we'll mock the initialization
    manager.initialized = True
    manager.graph_engine = MockGraphEngine()
    manager.curvature_analyzer = MockCurvatureAnalyzer()
    
    yield manager
    
    await manager.cleanup()


class MockGraphEngine:
    """Mock graph engine for testing."""
    
    def add_node(self, node_id, properties, timestamp):
        return True
    
    def query_nodes(self, filters):
        return [{"id": "test_node", "data": {"entity_type": "symptom"}}]
    
    def add_edge(self, source, target, relationship, properties, timestamp):
        return True
    
    def query_edges(self, filters):
        return [{"source": "node1", "target": "node2", "relationship": "has_symptom"}]


class MockCurvatureAnalyzer:
    """Mock curvature analyzer for testing."""
    
    def calculate_ricci_curvature(self, node_id):
        return 0.5
    
    def detect_anomalies(self, threshold):
        return [{"node_id": "test_node", "severity": 0.8}]


@pytest.mark.asyncio
async def test_add_node(graph_manager):
    """Test adding a node."""
    success = await graph_manager.add_node(
        "test_node",
        "symptom",
        {"name": "headache"}
    )
    assert success is True


@pytest.mark.asyncio
async def test_query_nodes(graph_manager):
    """Test querying nodes."""
    nodes = await graph_manager.query_nodes()
    assert len(nodes) > 0
    assert nodes[0]["id"] == "test_node"


@pytest.mark.asyncio
async def test_add_medical_entity(graph_manager):
    """Test adding a medical entity."""
    entity_id = await graph_manager.add_medical_entity(
        "symptom",
        {"name": "headache", "severity": 7},
        "patient_123"
    )
    assert entity_id is not None
    assert entity_id.startswith("symptom_")


@pytest.mark.asyncio
async def test_calculate_curvature(graph_manager):
    """Test curvature calculation."""
    curvature = await graph_manager.calculate_node_curvature("test_node")
    assert curvature == 0.5


@pytest.mark.asyncio
async def test_detect_anomalies(graph_manager):
    """Test anomaly detection."""
    anomalies = await graph_manager.detect_anomalies(0.7)
    assert len(anomalies) > 0
    assert anomalies[0]["node_id"] == "test_node"
