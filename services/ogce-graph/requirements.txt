# FastAPI and web framework
fastapi==0.109.0
uvicorn[standard]==0.27.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP client for external services
httpx==0.26.0
requests==2.31.0

# Graph and mathematical libraries
networkx==3.2.1
numpy==1.26.0
scipy==1.11.4

# OnionGraphContextEngine dependencies
# Add your specific OGContextEngine requirements here when integrating

# Database and persistence
redis==5.0.1
asyncpg==0.29.0  # PostgreSQL async driver
sqlalchemy[asyncio]==2.0.25

# Environment and configuration
python-dotenv==1.0.0

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Async support
aiofiles==23.2.1
asyncio-mqtt==0.16.1

# Monitoring and logging
structlog==23.2.0
prometheus-client==0.19.0

# Date and time handling
python-dateutil==2.8.2

# Testing
pytest==7.4.4
pytest-asyncio==0.23.2
httpx==0.26.0

# Development
black==24.1.1
ruff==0.1.9
mypy==1.8.0

# Local package reference to OnionGraphContextEngine
-e ../../packages/ogcontextengine
