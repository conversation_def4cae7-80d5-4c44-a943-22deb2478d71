"""
Tests for health check endpoints
"""

import pytest
from fastapi.testclient import TestClient
from app.main import create_app


@pytest.fixture
def client():
    """Create test client."""
    app = create_app()
    return TestClient(app)


def test_health_check(client):
    """Test basic health check endpoint."""
    response = client.get("/healthz/")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert "version" in data
    assert "uptime_seconds" in data


def test_readiness_check(client):
    """Test readiness check endpoint."""
    response = client.get("/healthz/ready")
    # May return 503 if watsonx client is not properly initialized in test
    assert response.status_code in [200, 503]


def test_liveness_check(client):
    """Test liveness check endpoint."""
    response = client.get("/healthz/live")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "alive"


def test_version_info(client):
    """Test version information endpoint."""
    response = client.get("/healthz/version")
    assert response.status_code == 200
    
    data = response.json()
    assert data["service"] == "SymptomOS Granite Gateway"
    assert data["version"] == "0.1.0"


def test_root_endpoint(client):
    """Test root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    
    data = response.json()
    assert data["service"] == "SymptomOS Granite Gateway"
    assert data["status"] == "healthy"
    assert "endpoints" in data
