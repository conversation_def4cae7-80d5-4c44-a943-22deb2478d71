# FastAPI and web framework
fastapi==0.109.0
uvicorn[standard]==0.27.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP client for IBM watsonx.ai
httpx==0.26.0
requests==2.31.0

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Environment and configuration
python-dotenv==1.0.0

# IBM watsonx.ai SDK (if available)
# ibm-watson-machine-learning==1.0.335
# ibm-cloud-sdk-core==3.16.7

# Audio processing
librosa==0.10.1
soundfile==0.12.1
pydub==0.25.1

# Medical text processing
spacy==3.7.2
scispacy==0.5.3

# Async support
asyncio-mqtt==0.16.1
aiofiles==23.2.1

# Monitoring and logging
structlog==23.2.0
prometheus-client==0.19.0

# Testing
pytest==7.4.4
pytest-asyncio==0.23.2
httpx==0.26.0  # for testing

# Development
black==24.1.1
ruff==0.1.9
mypy==1.8.0
