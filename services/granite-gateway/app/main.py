"""
Granite Gateway Service - IBM watsonx.ai Integration

This service provides a gateway to IBM Granite models for:
- Speech-to-text (ASR)
- Text generation
- Text-to-speech (TTS)
- Medical entity extraction
"""

import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON>AP<PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import structlog

from .config import get_settings
from .routers import asr, generate, tts, health
from .middleware import LoggingMiddleware, MetricsMiddleware
from .auth import get_current_user
from .watsonx_client import WatsonXClient

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    settings = get_settings()
    
    # Startup
    logger.info("Starting Granite Gateway Service", version="0.1.0")
    
    # Initialize IBM watsonx.ai client
    try:
        watsonx_client = WatsonXClient(settings)
        await watsonx_client.initialize()
        app.state.watsonx_client = watsonx_client
        logger.info("IBM watsonx.ai client initialized successfully")
    except Exception as e:
        logger.error("Failed to initialize IBM watsonx.ai client", error=str(e))
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Granite Gateway Service")
    if hasattr(app.state, 'watsonx_client'):
        await app.state.watsonx_client.close()


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title="SymptomOS Granite Gateway",
        description="IBM watsonx.ai integration service for medical AI processing",
        version="0.1.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.allowed_hosts
    )
    
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(MetricsMiddleware)
    
    # Include routers
    app.include_router(health.router, prefix="/healthz", tags=["health"])
    app.include_router(
        asr.router, 
        prefix="/v1/asr", 
        tags=["speech-to-text"],
        dependencies=[Depends(get_current_user)] if not settings.debug else []
    )
    app.include_router(
        generate.router, 
        prefix="/v1/generate", 
        tags=["text-generation"],
        dependencies=[Depends(get_current_user)] if not settings.debug else []
    )
    app.include_router(
        tts.router, 
        prefix="/v1/tts", 
        tags=["text-to-speech"],
        dependencies=[Depends(get_current_user)] if not settings.debug else []
    )
    
    # Global exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        logger.error(
            "Unhandled exception",
            path=request.url.path,
            method=request.method,
            error=str(exc),
            exc_info=True
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Internal server error"}
        )
    
    # Root endpoint
    @app.get("/")
    async def root():
        return {
            "service": "SymptomOS Granite Gateway",
            "version": "0.1.0",
            "status": "healthy",
            "endpoints": {
                "health": "/healthz",
                "speech_to_text": "/v1/asr",
                "text_generation": "/v1/generate", 
                "text_to_speech": "/v1/tts",
                "docs": "/docs" if settings.debug else "disabled"
            }
        }
    
    return app


# Create the app instance
app = create_app()

if __name__ == "__main__":
    import uvicorn
    settings = get_settings()
    
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_config=None,  # Use structlog instead
    )
