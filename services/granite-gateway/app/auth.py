"""
Authentication and authorization for Granite Gateway Service

Provides JWT-based authentication for API access.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from pydantic import BaseModel
import structlog

from .config import get_settings

logger = structlog.get_logger()

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer token scheme
security = HTTPBearer()


class TokenData(BaseModel):
    """Token payload data."""
    username: Optional[str] = None
    scopes: list[str] = []


class User(BaseModel):
    """User model."""
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    disabled: bool = False
    scopes: list[str] = []


class UserInDB(User):
    """User model with hashed password."""
    hashed_password: str


# Mock user database (in production, use a real database)
fake_users_db = {
    "doctor": {
        "username": "doctor",
        "full_name": "Dr. John Smith",
        "email": "<EMAIL>",
        "hashed_password": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "secret"
        "disabled": False,
        "scopes": ["read", "write", "admin"]
    },
    "patient": {
        "username": "patient",
        "full_name": "Jane Doe",
        "email": "<EMAIL>", 
        "hashed_password": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "secret"
        "disabled": False,
        "scopes": ["read"]
    },
    "service": {
        "username": "service",
        "full_name": "Service Account",
        "email": "<EMAIL>",
        "hashed_password": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "secret"
        "disabled": False,
        "scopes": ["read", "write"]
    }
}


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash a password."""
    return pwd_context.hash(password)


def get_user(username: str) -> Optional[UserInDB]:
    """Get user from database."""
    if username in fake_users_db:
        user_dict = fake_users_db[username]
        return UserInDB(**user_dict)
    return None


def authenticate_user(username: str, password: str) -> Optional[UserInDB]:
    """Authenticate a user with username and password."""
    user = get_user(username)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token."""
    settings = get_settings()
    
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """Get the current authenticated user from JWT token."""
    settings = get_settings()
    
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(credentials.credentials, settings.secret_key, algorithms=[settings.algorithm])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username, scopes=payload.get("scopes", []))
    except JWTError as e:
        logger.warning("JWT validation failed", error=str(e))
        raise credentials_exception
    
    user = get_user(username=token_data.username)
    if user is None:
        raise credentials_exception
    
    return User(
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        disabled=user.disabled,
        scopes=user.scopes
    )


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Get the current active user (not disabled)."""
    if current_user.disabled:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


def require_scopes(required_scopes: list[str]):
    """Dependency to require specific scopes."""
    def scope_checker(current_user: User = Depends(get_current_active_user)) -> User:
        for scope in required_scopes:
            if scope not in current_user.scopes:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Not enough permissions. Required scope: {scope}"
                )
        return current_user
    return scope_checker


# Convenience dependencies for common permission levels
require_read = require_scopes(["read"])
require_write = require_scopes(["write"])
require_admin = require_scopes(["admin"])


class APIKeyAuth:
    """Simple API key authentication for service-to-service communication."""
    
    def __init__(self):
        # In production, store these in a secure database
        self.api_keys = {
            "sk-symptomos-parser-service": {
                "name": "Parser Service",
                "scopes": ["read", "write"],
                "active": True
            },
            "sk-symptomos-ogce-graph": {
                "name": "OGCE Graph Service", 
                "scopes": ["read", "write"],
                "active": True
            },
            "sk-symptomos-mobile-app": {
                "name": "Mobile App",
                "scopes": ["read"],
                "active": True
            }
        }
    
    def validate_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Validate an API key and return associated metadata."""
        if api_key in self.api_keys:
            key_data = self.api_keys[api_key]
            if key_data["active"]:
                return key_data
        return None


api_key_auth = APIKeyAuth()


async def get_api_key_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """Authenticate using API key instead of JWT."""
    api_key = credentials.credentials
    
    # Check if it's an API key (starts with 'sk-')
    if api_key.startswith('sk-'):
        key_data = api_key_auth.validate_api_key(api_key)
        if key_data:
            return User(
                username=key_data["name"],
                email=None,
                full_name=key_data["name"],
                disabled=False,
                scopes=key_data["scopes"]
            )
    
    # Fall back to JWT authentication
    return await get_current_user(credentials)


# Optional: Create a dependency that accepts either JWT or API key
async def get_authenticated_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """Authenticate user with either JWT token or API key."""
    try:
        return await get_api_key_user(credentials)
    except HTTPException:
        # If API key auth fails, try JWT
        return await get_current_user(credentials)
