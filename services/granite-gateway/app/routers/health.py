"""
Health Check Router

Provides health check endpoints for monitoring and load balancing.
"""

import time
import psutil
from typing import Dict, Any
from fastapi import API<PERSON>outer, Depends, Request
from pydantic import BaseModel
import structlog

from ..config import get_settings
from ..watsonx_client import WatsonXClient

logger = structlog.get_logger()

router = APIRouter()


class HealthStatus(BaseModel):
    """Health status response model."""
    status: str
    timestamp: float
    version: str
    uptime_seconds: float


class DetailedHealthStatus(BaseModel):
    """Detailed health status with system metrics."""
    status: str
    timestamp: float
    version: str
    uptime_seconds: float
    system: Dict[str, Any]
    services: Dict[str, Any]
    configuration: Dict[str, Any]


# Track service start time
SERVICE_START_TIME = time.time()


def get_watsonx_client(request: Request) -> WatsonXClient:
    """Get the WatsonX client from app state."""
    return getattr(request.app.state, 'watsonx_client', None)


def get_system_metrics() -> Dict[str, Any]:
    """Get system performance metrics."""
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "cpu_usage_percent": cpu_percent,
            "memory": {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_percent": memory.percent
            },
            "disk": {
                "total_gb": round(disk.total / (1024**3), 2),
                "free_gb": round(disk.free / (1024**3), 2),
                "used_percent": round((disk.used / disk.total) * 100, 2)
            }
        }
    except Exception as e:
        logger.warning("Failed to get system metrics", error=str(e))
        return {"error": "Unable to retrieve system metrics"}


@router.get("/", response_model=HealthStatus)
async def health_check():
    """
    Basic health check endpoint.
    
    Returns simple status for load balancers and monitoring systems.
    """
    current_time = time.time()
    uptime = current_time - SERVICE_START_TIME
    
    return HealthStatus(
        status="healthy",
        timestamp=current_time,
        version="0.1.0",
        uptime_seconds=uptime
    )


@router.get("/detailed", response_model=DetailedHealthStatus)
async def detailed_health_check(
    watsonx_client: WatsonXClient = Depends(get_watsonx_client)
):
    """
    Detailed health check with system metrics and service status.
    
    Includes system performance, service dependencies, and configuration status.
    """
    settings = get_settings()
    current_time = time.time()
    uptime = current_time - SERVICE_START_TIME
    
    # Get system metrics
    system_metrics = get_system_metrics()
    
    # Check service dependencies
    services_status = {}
    
    # Check IBM watsonx.ai connection
    if watsonx_client:
        try:
            watsonx_health = await watsonx_client.health_check()
            services_status["watsonx"] = watsonx_health
        except Exception as e:
            services_status["watsonx"] = {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": current_time
            }
    else:
        services_status["watsonx"] = {
            "status": "not_initialized",
            "timestamp": current_time
        }
    
    # Configuration status
    config_status = {
        "debug_mode": settings.debug,
        "environment": "development" if settings.debug else "production",
        "watsonx_configured": bool(settings.watsonx_api_key and settings.watsonx_project_id),
        "cache_enabled": settings.enable_cache,
        "metrics_enabled": settings.enable_metrics
    }
    
    # Determine overall status
    overall_status = "healthy"
    if services_status.get("watsonx", {}).get("status") == "unhealthy":
        overall_status = "degraded"
    
    return DetailedHealthStatus(
        status=overall_status,
        timestamp=current_time,
        version="0.1.0",
        uptime_seconds=uptime,
        system=system_metrics,
        services=services_status,
        configuration=config_status
    )


@router.get("/ready")
async def readiness_check(
    watsonx_client: WatsonXClient = Depends(get_watsonx_client)
):
    """
    Readiness check for Kubernetes deployments.
    
    Returns 200 if service is ready to accept traffic, 503 otherwise.
    """
    try:
        # Check if watsonx client is initialized and healthy
        if not watsonx_client:
            return {"status": "not_ready", "reason": "WatsonX client not initialized"}, 503
        
        # Quick health check
        health_status = await watsonx_client.health_check()
        if health_status["status"] != "healthy":
            return {"status": "not_ready", "reason": "WatsonX service unhealthy"}, 503
        
        return {"status": "ready", "timestamp": time.time()}
        
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        return {"status": "not_ready", "reason": str(e)}, 503


@router.get("/live")
async def liveness_check():
    """
    Liveness check for Kubernetes deployments.
    
    Returns 200 if service is alive, 503 if it should be restarted.
    """
    try:
        # Basic checks to ensure service is functioning
        current_time = time.time()
        uptime = current_time - SERVICE_START_TIME
        
        # Check if service has been running too long (optional restart trigger)
        max_uptime = 24 * 60 * 60  # 24 hours
        if uptime > max_uptime:
            logger.warning("Service uptime exceeds maximum", uptime_hours=uptime/3600)
        
        # Check system resources
        try:
            memory = psutil.virtual_memory()
            if memory.percent > 95:  # Critical memory usage
                logger.error("Critical memory usage detected", memory_percent=memory.percent)
                return {"status": "unhealthy", "reason": "Critical memory usage"}, 503
        except:
            pass  # Don't fail liveness check if we can't get metrics
        
        return {"status": "alive", "timestamp": current_time, "uptime_seconds": uptime}
        
    except Exception as e:
        logger.error("Liveness check failed", error=str(e))
        return {"status": "dead", "reason": str(e)}, 503


@router.get("/metrics")
async def metrics_endpoint():
    """
    Prometheus-compatible metrics endpoint.
    
    Returns metrics in Prometheus format for monitoring.
    """
    settings = get_settings()
    
    if not settings.enable_metrics:
        return {"error": "Metrics disabled"}, 404
    
    current_time = time.time()
    uptime = current_time - SERVICE_START_TIME
    
    # Get system metrics
    system_metrics = get_system_metrics()
    
    # Format as Prometheus metrics
    metrics = []
    
    # Service metrics
    metrics.append(f"symptomos_granite_gateway_uptime_seconds {uptime}")
    metrics.append(f"symptomos_granite_gateway_info{{version=\"0.1.0\"}} 1")
    
    # System metrics
    if "cpu_usage_percent" in system_metrics:
        metrics.append(f"symptomos_granite_gateway_cpu_usage_percent {system_metrics['cpu_usage_percent']}")
    
    if "memory" in system_metrics:
        memory = system_metrics["memory"]
        metrics.append(f"symptomos_granite_gateway_memory_usage_percent {memory.get('used_percent', 0)}")
        metrics.append(f"symptomos_granite_gateway_memory_total_bytes {memory.get('total_gb', 0) * 1024**3}")
    
    # TODO: Add request metrics, error rates, response times, etc.
    # This would typically be handled by a metrics middleware
    
    return "\n".join(metrics), {"Content-Type": "text/plain"}


@router.get("/version")
async def version_info():
    """
    Service version information.
    """
    return {
        "service": "SymptomOS Granite Gateway",
        "version": "0.1.0",
        "build_time": "2024-01-15T00:00:00Z",  # TODO: Set during build
        "git_commit": "unknown",  # TODO: Set during build
        "python_version": "3.11+",
        "dependencies": {
            "fastapi": "0.109.0",
            "httpx": "0.26.0",
            "pydantic": "2.5.0"
        }
    }
