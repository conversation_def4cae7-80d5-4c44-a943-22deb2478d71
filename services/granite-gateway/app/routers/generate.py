"""
Text Generation Router

Handles text generation using IBM Granite models for medical insights and analysis.
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field
import structlog

from ..config import get_settings
from ..watsonx_client import WatsonXClient, WatsonXAPIError

logger = structlog.get_logger()

router = APIRouter()


class GenerationParameters(BaseModel):
    """Parameters for text generation."""
    max_tokens: int = Field(default=500, ge=1, le=2000, description="Maximum tokens to generate")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="Sampling temperature")
    top_p: float = Field(default=0.9, ge=0.0, le=1.0, description="Nucleus sampling parameter")
    top_k: int = Field(default=50, ge=1, le=100, description="Top-k sampling parameter")
    repetition_penalty: float = Field(default=1.1, ge=1.0, le=2.0, description="Repetition penalty")
    stop_sequences: List[str] = Field(default=[], description="Stop generation at these sequences")


class MedicalContext(BaseModel):
    """Medical context for generation."""
    patient_id: Optional[str] = Field(default=None, description="Patient identifier")
    patient_age: Optional[int] = Field(default=None, description="Patient age")
    patient_gender: Optional[str] = Field(default=None, description="Patient gender")
    medical_history: List[str] = Field(default=[], description="Relevant medical history")
    current_symptoms: List[str] = Field(default=[], description="Current symptoms")
    medications: List[str] = Field(default=[], description="Current medications")


class GenerateRequest(BaseModel):
    """Request model for text generation."""
    prompt: str = Field(..., min_length=1, max_length=4000, description="Input prompt")
    model_id: Optional[str] = Field(default=None, description="Specific model to use")
    parameters: GenerationParameters = Field(default_factory=GenerationParameters)
    medical_context: Optional[MedicalContext] = Field(default=None, description="Medical context")
    task_type: str = Field(
        default="general",
        description="Type of task: general, medical_analysis, symptom_analysis, treatment_suggestion"
    )
    include_reasoning: bool = Field(default=True, description="Include reasoning in response")


class MedicalInsight(BaseModel):
    """Medical insight from AI analysis."""
    insight_type: str = Field(description="Type of insight")
    description: str = Field(description="Insight description")
    confidence: float = Field(description="Confidence score (0-1)")
    supporting_evidence: List[str] = Field(default=[], description="Supporting evidence")


class GenerateResponse(BaseModel):
    """Response model for text generation."""
    generated_text: str = Field(description="Generated text")
    confidence: float = Field(description="Overall confidence score")
    tokens_used: int = Field(description="Number of tokens used")
    model_used: str = Field(description="Model used for generation")
    medical_insights: List[MedicalInsight] = Field(default=[], description="Extracted medical insights")
    processing_time_ms: float = Field(description="Processing time in milliseconds")
    reasoning: Optional[str] = Field(default=None, description="AI reasoning process")


def get_watsonx_client(request: Request) -> WatsonXClient:
    """Get the WatsonX client from app state."""
    return request.app.state.watsonx_client


def build_medical_prompt(request: GenerateRequest) -> str:
    """Build a medical-context aware prompt."""
    base_prompt = request.prompt
    
    if not request.medical_context:
        return base_prompt
    
    context_parts = []
    
    # Add patient context
    if request.medical_context.patient_age:
        context_parts.append(f"Patient age: {request.medical_context.patient_age}")
    
    if request.medical_context.patient_gender:
        context_parts.append(f"Patient gender: {request.medical_context.patient_gender}")
    
    if request.medical_context.medical_history:
        context_parts.append(f"Medical history: {', '.join(request.medical_context.medical_history)}")
    
    if request.medical_context.current_symptoms:
        context_parts.append(f"Current symptoms: {', '.join(request.medical_context.current_symptoms)}")
    
    if request.medical_context.medications:
        context_parts.append(f"Current medications: {', '.join(request.medical_context.medications)}")
    
    if context_parts:
        context_str = "\n".join(context_parts)
        enhanced_prompt = f"""Medical Context:
{context_str}

Task: {base_prompt}

Please provide a medical analysis considering the above context. Be thorough but concise."""
        return enhanced_prompt
    
    return base_prompt


def extract_medical_insights(generated_text: str, task_type: str) -> List[MedicalInsight]:
    """Extract medical insights from generated text."""
    insights = []
    
    # Simple keyword-based insight extraction
    # In a real implementation, this would use more sophisticated NLP
    
    text_lower = generated_text.lower()
    
    # Pattern detection
    if any(word in text_lower for word in ["pattern", "correlation", "trend"]):
        insights.append(MedicalInsight(
            insight_type="pattern_detection",
            description="Potential pattern or correlation identified in symptoms",
            confidence=0.7,
            supporting_evidence=["Pattern analysis in generated text"]
        ))
    
    # Risk assessment
    if any(word in text_lower for word in ["risk", "concern", "warning", "urgent"]):
        insights.append(MedicalInsight(
            insight_type="risk_assessment",
            description="Potential risk factors or concerns identified",
            confidence=0.8,
            supporting_evidence=["Risk indicators in analysis"]
        ))
    
    # Treatment suggestions
    if any(word in text_lower for word in ["recommend", "suggest", "treatment", "therapy"]):
        insights.append(MedicalInsight(
            insight_type="treatment_suggestion",
            description="Treatment or intervention suggestions provided",
            confidence=0.6,
            supporting_evidence=["Treatment recommendations in text"]
        ))
    
    return insights


@router.post("/", response_model=GenerateResponse)
async def generate_text(
    request: GenerateRequest,
    watsonx_client: WatsonXClient = Depends(get_watsonx_client)
):
    """
    Generate text using IBM Granite models.
    
    Supports various medical analysis tasks including symptom analysis,
    treatment suggestions, and general medical insights.
    """
    settings = get_settings()
    
    try:
        # Build the prompt with medical context
        enhanced_prompt = build_medical_prompt(request)
        
        logger.info(
            "Processing text generation request",
            task_type=request.task_type,
            prompt_length=len(enhanced_prompt),
            model_id=request.model_id,
            has_medical_context=request.medical_context is not None
        )
        
        # Prepare parameters
        generation_params = {
            "max_new_tokens": request.parameters.max_tokens,
            "temperature": request.parameters.temperature,
            "top_p": request.parameters.top_p,
            "top_k": request.parameters.top_k,
            "repetition_penalty": request.parameters.repetition_penalty,
            "stop_sequences": request.parameters.stop_sequences
        }
        
        # Generate text
        import time
        start_time = time.time()
        
        result = await watsonx_client.generate_text(
            prompt=enhanced_prompt,
            model_id=request.model_id,
            parameters=generation_params
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        # Extract the generated text
        generated_text = result.get("results", [{}])[0].get("generated_text", "")
        
        # Extract medical insights
        medical_insights = extract_medical_insights(generated_text, request.task_type)
        
        # Extract reasoning if requested
        reasoning = None
        if request.include_reasoning:
            reasoning = "AI analysis based on medical context and symptom patterns"
        
        response = GenerateResponse(
            generated_text=generated_text,
            confidence=0.85,  # TODO: Get actual confidence from model
            tokens_used=result.get("results", [{}])[0].get("token_count", 0),
            model_used=request.model_id or settings.default_text_model,
            medical_insights=medical_insights,
            processing_time_ms=processing_time,
            reasoning=reasoning
        )
        
        logger.info(
            "Text generation completed",
            generated_length=len(generated_text),
            tokens_used=response.tokens_used,
            insights_found=len(medical_insights),
            processing_time_ms=processing_time
        )
        
        return response
        
    except WatsonXAPIError as e:
        logger.error("WatsonX API error during text generation", error=str(e))
        raise HTTPException(status_code=502, detail=f"Text generation service error: {str(e)}")
        
    except Exception as e:
        logger.error("Unexpected error during text generation", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error during text generation")


@router.post("/analyze-symptoms")
async def analyze_symptoms(
    symptoms: List[str] = Field(..., description="List of symptoms to analyze"),
    patient_context: Optional[MedicalContext] = None,
    watsonx_client: WatsonXClient = Depends(get_watsonx_client)
):
    """
    Analyze a list of symptoms and provide medical insights.
    
    This is a specialized endpoint for symptom analysis.
    """
    # Build a symptom analysis prompt
    symptoms_text = ", ".join(symptoms)
    prompt = f"""Analyze the following symptoms and provide medical insights:

Symptoms: {symptoms_text}

Please provide:
1. Possible conditions or diagnoses
2. Severity assessment
3. Recommended next steps
4. Any red flags or urgent concerns

Be thorough but concise in your analysis."""
    
    request = GenerateRequest(
        prompt=prompt,
        medical_context=patient_context,
        task_type="symptom_analysis",
        parameters=GenerationParameters(max_tokens=800, temperature=0.3)
    )
    
    return await generate_text(request, watsonx_client)


@router.post("/suggest-treatment")
async def suggest_treatment(
    condition: str = Field(..., description="Medical condition or diagnosis"),
    patient_context: Optional[MedicalContext] = None,
    watsonx_client: WatsonXClient = Depends(get_watsonx_client)
):
    """
    Suggest treatment options for a given condition.
    
    This is a specialized endpoint for treatment suggestions.
    """
    prompt = f"""Provide treatment suggestions for the following condition:

Condition: {condition}

Please include:
1. First-line treatment options
2. Alternative treatments
3. Lifestyle modifications
4. Monitoring recommendations
5. When to seek immediate medical attention

Focus on evidence-based recommendations."""
    
    request = GenerateRequest(
        prompt=prompt,
        medical_context=patient_context,
        task_type="treatment_suggestion",
        parameters=GenerationParameters(max_tokens=1000, temperature=0.2)
    )
    
    return await generate_text(request, watsonx_client)


@router.get("/models")
async def list_available_models():
    """List available text generation models."""
    return {
        "models": [
            {
                "id": "ibm/granite-13b-chat-v2",
                "name": "Granite 13B Chat V2",
                "description": "General purpose conversational model",
                "medical_optimized": False,
                "max_tokens": 2048
            },
            {
                "id": "ibm/granite-medical-v1",
                "name": "Granite Medical V1",
                "description": "Medical domain optimized model",
                "medical_optimized": True,
                "max_tokens": 4096
            }
        ]
    }


@router.get("/health")
async def generate_health_check(
    watsonx_client: WatsonXClient = Depends(get_watsonx_client)
):
    """Health check for text generation service."""
    try:
        health_status = await watsonx_client.health_check()
        return {
            "service": "Text Generation",
            "status": "healthy" if health_status["status"] == "healthy" else "unhealthy",
            "watsonx_status": health_status,
            "models_available": True
        }
    except Exception as e:
        logger.error("Text generation health check failed", error=str(e))
        return {
            "service": "Text Generation",
            "status": "unhealthy",
            "error": str(e)
        }
