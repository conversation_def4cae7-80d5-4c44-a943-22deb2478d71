"""
Automatic Speech Recognition (ASR) Router

Handles speech-to-text conversion using IBM Granite Speech models.
"""

import io
from typing import List, Optional
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import structlog

from ..config import get_settings
from ..watsonx_client import WatsonXClient, WatsonXAPIError

logger = structlog.get_logger()

router = APIRouter()


class ASRRequest(BaseModel):
    """Request model for speech recognition."""
    language_code: str = Field(default="en-US", description="Language code for recognition")
    enable_medical_terms: bool = Field(default=True, description="Enable medical terminology recognition")
    model_id: Optional[str] = Field(default=None, description="Specific model to use")
    extract_entities: bool = Field(default=True, description="Extract medical entities from transcription")


class MedicalEntity(BaseModel):
    """Medical entity extracted from speech."""
    text: str = Field(description="The entity text")
    entity_type: str = Field(description="Type of entity (symptom, medication, etc.)")
    confidence: float = Field(description="Confidence score (0-1)")
    start_offset: int = Field(description="Start position in transcription")
    end_offset: int = Field(description="End position in transcription")


class ASRAlternative(BaseModel):
    """Alternative transcription result."""
    text: str = Field(description="Alternative transcription")
    confidence: float = Field(description="Confidence score (0-1)")


class ASRResponse(BaseModel):
    """Response model for speech recognition."""
    transcription: str = Field(description="Primary transcription result")
    confidence: float = Field(description="Overall confidence score")
    alternatives: List[ASRAlternative] = Field(default=[], description="Alternative transcriptions")
    medical_entities: List[MedicalEntity] = Field(default=[], description="Extracted medical entities")
    processing_time_ms: float = Field(description="Processing time in milliseconds")
    model_used: str = Field(description="Model used for recognition")


def get_watsonx_client(request: Request) -> WatsonXClient:
    """Get the WatsonX client from app state."""
    return request.app.state.watsonx_client


@router.post("/", response_model=ASRResponse)
async def speech_to_text(
    audio_file: UploadFile = File(..., description="Audio file to transcribe"),
    request_data: ASRRequest = Depends(),
    watsonx_client: WatsonXClient = Depends(get_watsonx_client)
):
    """
    Convert speech to text using IBM Granite Speech models.
    
    Supports various audio formats including WAV, MP3, FLAC, and OGG.
    Automatically extracts medical entities from the transcription.
    """
    settings = get_settings()
    
    # Validate file size
    if audio_file.size and audio_file.size > settings.max_audio_size_mb * 1024 * 1024:
        raise HTTPException(
            status_code=413,
            detail=f"Audio file too large. Maximum size: {settings.max_audio_size_mb}MB"
        )
    
    # Validate content type
    allowed_types = ["audio/wav", "audio/mpeg", "audio/mp3", "audio/flac", "audio/ogg"]
    if audio_file.content_type not in allowed_types:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported audio format. Allowed: {', '.join(allowed_types)}"
        )
    
    try:
        # Read audio data
        audio_data = await audio_file.read()
        
        logger.info(
            "Processing speech recognition request",
            filename=audio_file.filename,
            content_type=audio_file.content_type,
            size_bytes=len(audio_data),
            language=request_data.language_code
        )
        
        # Process with IBM Granite Speech
        import time
        start_time = time.time()
        
        result = await watsonx_client.speech_to_text(
            audio_data=audio_data,
            content_type=audio_file.content_type,
            model_id=request_data.model_id
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        # Extract medical entities if requested
        medical_entities = []
        if request_data.extract_entities and result.get("transcription"):
            entities = await watsonx_client.extract_medical_entities(result["transcription"])
            medical_entities = [
                MedicalEntity(
                    text=entity["text"],
                    entity_type=entity["type"],
                    confidence=entity["confidence"],
                    start_offset=entity["start_offset"],
                    end_offset=entity["end_offset"]
                )
                for entity in entities
            ]
        
        # Format alternatives
        alternatives = [
            ASRAlternative(text=alt["text"], confidence=alt["confidence"])
            for alt in result.get("alternatives", [])
        ]
        
        response = ASRResponse(
            transcription=result.get("transcription", ""),
            confidence=result.get("confidence", 0.0),
            alternatives=alternatives,
            medical_entities=medical_entities,
            processing_time_ms=processing_time,
            model_used=request_data.model_id or settings.default_speech_model
        )
        
        logger.info(
            "Speech recognition completed",
            transcription_length=len(response.transcription),
            confidence=response.confidence,
            entities_found=len(medical_entities),
            processing_time_ms=processing_time
        )
        
        return response
        
    except WatsonXAPIError as e:
        logger.error("WatsonX API error during speech recognition", error=str(e))
        raise HTTPException(status_code=502, detail=f"Speech recognition service error: {str(e)}")
        
    except Exception as e:
        logger.error("Unexpected error during speech recognition", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error during speech processing")


@router.post("/batch", response_model=List[ASRResponse])
async def batch_speech_to_text(
    audio_files: List[UploadFile] = File(..., description="Multiple audio files to transcribe"),
    request_data: ASRRequest = Depends(),
    watsonx_client: WatsonXClient = Depends(get_watsonx_client)
):
    """
    Process multiple audio files in batch.
    
    Useful for processing multiple symptom recordings at once.
    """
    settings = get_settings()
    
    if len(audio_files) > 10:  # Limit batch size
        raise HTTPException(
            status_code=400,
            detail="Too many files in batch. Maximum: 10 files"
        )
    
    results = []
    
    for audio_file in audio_files:
        try:
            # Reuse the single file processing logic
            # In a real implementation, you might want to process these in parallel
            result = await speech_to_text(audio_file, request_data, watsonx_client)
            results.append(result)
            
        except HTTPException as e:
            # Add error information to the result
            error_result = ASRResponse(
                transcription="",
                confidence=0.0,
                alternatives=[],
                medical_entities=[],
                processing_time_ms=0.0,
                model_used="error"
            )
            results.append(error_result)
            
            logger.warning(
                "Failed to process file in batch",
                filename=audio_file.filename,
                error=str(e.detail)
            )
    
    logger.info(
        "Batch speech recognition completed",
        total_files=len(audio_files),
        successful=len([r for r in results if r.transcription]),
        failed=len([r for r in results if not r.transcription])
    )
    
    return results


@router.get("/models")
async def list_available_models(
    watsonx_client: WatsonXClient = Depends(get_watsonx_client)
):
    """
    List available speech recognition models.
    """
    # TODO: Implement actual model listing from IBM watsonx.ai
    return {
        "models": [
            {
                "id": "ibm/granite-speech-v1",
                "name": "Granite Speech V1",
                "description": "General purpose speech recognition",
                "languages": ["en-US", "en-GB", "es-ES", "fr-FR"],
                "medical_optimized": False
            },
            {
                "id": "ibm/granite-speech-medical-v1",
                "name": "Granite Speech Medical V1", 
                "description": "Medical terminology optimized speech recognition",
                "languages": ["en-US"],
                "medical_optimized": True
            }
        ]
    }


@router.get("/health")
async def asr_health_check(
    watsonx_client: WatsonXClient = Depends(get_watsonx_client)
):
    """Health check for ASR service."""
    try:
        health_status = await watsonx_client.health_check()
        return {
            "service": "ASR",
            "status": "healthy" if health_status["status"] == "healthy" else "unhealthy",
            "watsonx_status": health_status,
            "models_available": True  # TODO: Check actual model availability
        }
    except Exception as e:
        logger.error("ASR health check failed", error=str(e))
        return {
            "service": "ASR",
            "status": "unhealthy",
            "error": str(e)
        }
