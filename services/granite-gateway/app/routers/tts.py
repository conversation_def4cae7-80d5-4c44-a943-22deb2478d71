"""
Text-to-Speech (TTS) Router

Handles text-to-speech conversion using IBM Watson Text to Speech.
"""

from typing import Optional
from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import Response
from pydantic import BaseModel, Field
import structlog

from ..config import get_settings
from ..watsonx_client import WatsonXClient, WatsonXAPIError

logger = structlog.get_logger()

router = APIRouter()


class TTSRequest(BaseModel):
    """Request model for text-to-speech."""
    text: str = Field(..., min_length=1, max_length=5000, description="Text to synthesize")
    voice: str = Field(
        default="en-US_AllisonV3Voice",
        description="Voice to use for synthesis"
    )
    format: str = Field(
        default="audio/wav",
        description="Audio format (audio/wav, audio/mp3, audio/ogg)"
    )
    sample_rate: int = Field(
        default=22050,
        description="Audio sample rate in Hz"
    )
    speaking_rate: float = Field(
        default=1.0,
        ge=0.5,
        le=2.0,
        description="Speaking rate (0.5-2.0)"
    )
    pitch: float = Field(
        default=0.0,
        ge=-20.0,
        le=20.0,
        description="Pitch adjustment (-20.0 to 20.0)"
    )
    language_code: str = Field(
        default="en-US",
        description="Language code"
    )


class TTSResponse(BaseModel):
    """Response model for text-to-speech metadata."""
    success: bool = Field(description="Whether synthesis was successful")
    format: str = Field(description="Audio format")
    sample_rate: int = Field(description="Audio sample rate")
    duration_ms: int = Field(description="Audio duration in milliseconds")
    processing_time_ms: float = Field(description="Processing time in milliseconds")
    voice_used: str = Field(description="Voice used for synthesis")
    text_length: int = Field(description="Length of input text")


def get_watsonx_client(request: Request) -> WatsonXClient:
    """Get the WatsonX client from app state."""
    return request.app.state.watsonx_client


@router.post("/", response_class=Response)
async def text_to_speech(
    request: TTSRequest,
    watsonx_client: WatsonXClient = Depends(get_watsonx_client)
):
    """
    Convert text to speech using IBM Watson Text to Speech.
    
    Returns audio data directly as response body.
    """
    settings = get_settings()
    
    # Validate format
    allowed_formats = ["audio/wav", "audio/mp3", "audio/ogg"]
    if request.format not in allowed_formats:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported audio format. Allowed: {', '.join(allowed_formats)}"
        )
    
    try:
        logger.info(
            "Processing text-to-speech request",
            text_length=len(request.text),
            voice=request.voice,
            format=request.format,
            sample_rate=request.sample_rate
        )
        
        # Generate speech
        import time
        start_time = time.time()
        
        audio_data = await watsonx_client.text_to_speech(
            text=request.text,
            voice=request.voice,
            format=request.format
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        logger.info(
            "Text-to-speech completed",
            audio_size_bytes=len(audio_data),
            processing_time_ms=processing_time
        )
        
        # Determine content type based on format
        content_type_map = {
            "audio/wav": "audio/wav",
            "audio/mp3": "audio/mpeg",
            "audio/ogg": "audio/ogg"
        }
        
        content_type = content_type_map.get(request.format, "audio/wav")
        
        # Return audio data with appropriate headers
        return Response(
            content=audio_data,
            media_type=content_type,
            headers={
                "Content-Disposition": "attachment; filename=speech.wav",
                "X-Processing-Time-Ms": str(processing_time),
                "X-Voice-Used": request.voice,
                "X-Text-Length": str(len(request.text))
            }
        )
        
    except WatsonXAPIError as e:
        logger.error("WatsonX API error during text-to-speech", error=str(e))
        raise HTTPException(status_code=502, detail=f"Text-to-speech service error: {str(e)}")
        
    except Exception as e:
        logger.error("Unexpected error during text-to-speech", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error during speech synthesis")


@router.post("/metadata", response_model=TTSResponse)
async def text_to_speech_with_metadata(
    request: TTSRequest,
    watsonx_client: WatsonXClient = Depends(get_watsonx_client)
):
    """
    Convert text to speech and return metadata along with audio data.
    
    This endpoint returns JSON with metadata and base64-encoded audio.
    """
    try:
        logger.info(
            "Processing text-to-speech request with metadata",
            text_length=len(request.text),
            voice=request.voice
        )
        
        import time
        import base64
        start_time = time.time()
        
        audio_data = await watsonx_client.text_to_speech(
            text=request.text,
            voice=request.voice,
            format=request.format
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        # Estimate duration (rough calculation)
        # In a real implementation, you'd get this from the TTS service
        estimated_duration_ms = len(request.text) * 100  # ~100ms per character (rough estimate)
        
        response = TTSResponse(
            success=True,
            format=request.format,
            sample_rate=request.sample_rate,
            duration_ms=estimated_duration_ms,
            processing_time_ms=processing_time,
            voice_used=request.voice,
            text_length=len(request.text)
        )
        
        logger.info(
            "Text-to-speech with metadata completed",
            audio_size_bytes=len(audio_data),
            processing_time_ms=processing_time
        )
        
        # Return metadata with base64-encoded audio
        return {
            **response.dict(),
            "audio_data": base64.b64encode(audio_data).decode("utf-8")
        }
        
    except WatsonXAPIError as e:
        logger.error("WatsonX API error during text-to-speech", error=str(e))
        raise HTTPException(status_code=502, detail=f"Text-to-speech service error: {str(e)}")
        
    except Exception as e:
        logger.error("Unexpected error during text-to-speech", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error during speech synthesis")


@router.post("/preview")
async def preview_speech(
    text: str = Field(..., max_length=200, description="Text to preview (max 200 chars)"),
    voice: str = Field(default="en-US_AllisonV3Voice", description="Voice to use"),
    watsonx_client: WatsonXClient = Depends(get_watsonx_client)
):
    """
    Generate a short preview of text-to-speech output.
    
    Useful for testing voices and settings before generating full audio.
    """
    # Truncate text for preview
    preview_text = text[:200] if len(text) > 200 else text
    
    request = TTSRequest(
        text=preview_text,
        voice=voice,
        format="audio/wav"
    )
    
    return await text_to_speech(request, watsonx_client)


@router.get("/voices")
async def list_available_voices():
    """
    List available voices for text-to-speech.
    """
    # TODO: Get actual voices from IBM Watson TTS
    return {
        "voices": [
            {
                "name": "en-US_AllisonV3Voice",
                "language": "en-US",
                "gender": "female",
                "description": "Allison: American English female voice"
            },
            {
                "name": "en-US_LisaV3Voice",
                "language": "en-US", 
                "gender": "female",
                "description": "Lisa: American English female voice"
            },
            {
                "name": "en-US_MichaelV3Voice",
                "language": "en-US",
                "gender": "male",
                "description": "Michael: American English male voice"
            },
            {
                "name": "en-GB_KateV3Voice",
                "language": "en-GB",
                "gender": "female",
                "description": "Kate: British English female voice"
            },
            {
                "name": "es-ES_LauraV3Voice",
                "language": "es-ES",
                "gender": "female",
                "description": "Laura: Spanish female voice"
            }
        ]
    }


@router.get("/formats")
async def list_supported_formats():
    """
    List supported audio formats for text-to-speech.
    """
    return {
        "formats": [
            {
                "format": "audio/wav",
                "description": "WAV format, uncompressed",
                "file_extension": ".wav",
                "quality": "high"
            },
            {
                "format": "audio/mp3",
                "description": "MP3 format, compressed",
                "file_extension": ".mp3",
                "quality": "medium"
            },
            {
                "format": "audio/ogg",
                "description": "OGG Vorbis format, compressed",
                "file_extension": ".ogg",
                "quality": "medium"
            }
        ]
    }


@router.get("/health")
async def tts_health_check(
    watsonx_client: WatsonXClient = Depends(get_watsonx_client)
):
    """Health check for TTS service."""
    try:
        health_status = await watsonx_client.health_check()
        return {
            "service": "TTS",
            "status": "healthy" if health_status["status"] == "healthy" else "unhealthy",
            "watsonx_status": health_status,
            "voices_available": True  # TODO: Check actual voice availability
        }
    except Exception as e:
        logger.error("TTS health check failed", error=str(e))
        return {
            "service": "TTS",
            "status": "unhealthy",
            "error": str(e)
        }
