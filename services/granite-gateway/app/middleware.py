"""
Middleware for Granite Gateway Service

Provides logging, metrics, and request processing middleware.
"""

import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import structlog

logger = structlog.get_logger()


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for structured request/response logging."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID
        request_id = str(uuid.uuid4())
        
        # Add request ID to context
        with structlog.contextvars.bound_contextvars(request_id=request_id):
            start_time = time.time()
            
            # Log request
            logger.info(
                "Request started",
                method=request.method,
                url=str(request.url),
                client_ip=request.client.host if request.client else None,
                user_agent=request.headers.get("user-agent"),
                content_length=request.headers.get("content-length")
            )
            
            # Process request
            try:
                response = await call_next(request)
                
                # Calculate processing time
                processing_time = time.time() - start_time
                
                # Log response
                logger.info(
                    "Request completed",
                    status_code=response.status_code,
                    processing_time_ms=processing_time * 1000,
                    response_size=response.headers.get("content-length")
                )
                
                # Add request ID to response headers
                response.headers["X-Request-ID"] = request_id
                response.headers["X-Processing-Time"] = f"{processing_time:.3f}s"
                
                return response
                
            except Exception as e:
                processing_time = time.time() - start_time
                
                logger.error(
                    "Request failed",
                    error=str(e),
                    processing_time_ms=processing_time * 1000,
                    exc_info=True
                )
                
                raise


class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting request metrics."""
    
    def __init__(self, app):
        super().__init__(app)
        # In a real implementation, you'd initialize Prometheus metrics here
        self.request_count = 0
        self.request_duration_sum = 0.0
        self.error_count = 0
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        try:
            response = await call_next(request)
            
            # Record metrics
            processing_time = time.time() - start_time
            self.request_count += 1
            self.request_duration_sum += processing_time
            
            # TODO: Record Prometheus metrics
            # - Request count by method, path, status code
            # - Request duration histogram
            # - Active requests gauge
            
            # Add metrics headers for debugging
            response.headers["X-Request-Count"] = str(self.request_count)
            response.headers["X-Average-Duration"] = f"{self.request_duration_sum / self.request_count:.3f}s"
            
            return response
            
        except Exception as e:
            # Record error metrics
            self.error_count += 1
            
            # TODO: Record error metrics in Prometheus
            
            raise


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Simple rate limiting middleware."""
    
    def __init__(self, app, requests_per_minute: int = 100):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.client_requests = {}  # In production, use Redis
        self.window_size = 60  # 1 minute
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()
        
        # Clean old entries
        cutoff_time = current_time - self.window_size
        if client_ip in self.client_requests:
            self.client_requests[client_ip] = [
                req_time for req_time in self.client_requests[client_ip]
                if req_time > cutoff_time
            ]
        
        # Check rate limit
        if client_ip not in self.client_requests:
            self.client_requests[client_ip] = []
        
        if len(self.client_requests[client_ip]) >= self.requests_per_minute:
            logger.warning(
                "Rate limit exceeded",
                client_ip=client_ip,
                requests_in_window=len(self.client_requests[client_ip])
            )
            
            return Response(
                content='{"detail": "Rate limit exceeded"}',
                status_code=429,
                headers={
                    "Content-Type": "application/json",
                    "Retry-After": "60",
                    "X-RateLimit-Limit": str(self.requests_per_minute),
                    "X-RateLimit-Remaining": "0",
                    "X-RateLimit-Reset": str(int(current_time + self.window_size))
                }
            )
        
        # Record request
        self.client_requests[client_ip].append(current_time)
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        remaining = self.requests_per_minute - len(self.client_requests[client_ip])
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(current_time + self.window_size))
        
        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        
        # Remove server header
        if "server" in response.headers:
            del response.headers["server"]
        
        return response
