"""
IBM watsonx.ai Client for Granite Gateway Service

This module handles authentication and communication with IBM watsonx.ai services.
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional, List
import httpx
import structlog
from .config import Settings

logger = structlog.get_logger()


class WatsonXAuthError(Exception):
    """Authentication error with IBM watsonx.ai."""
    pass


class WatsonXAPIError(Exception):
    """API error from IBM watsonx.ai."""
    pass


class WatsonXClient:
    """Client for IBM watsonx.ai services."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.access_token: Optional[str] = None
        self.token_expires_at: float = 0
        self.client: Optional[httpx.AsyncClient] = None
        
    async def initialize(self):
        """Initialize the client and authenticate."""
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.settings.request_timeout),
            limits=httpx.Limits(max_connections=20, max_keepalive_connections=5)
        )
        
        # Get initial access token
        await self._refresh_token()
        logger.info("WatsonX client initialized successfully")
        
    async def close(self):
        """Close the client."""
        if self.client:
            await self.client.aclose()
            
    async def _refresh_token(self):
        """Refresh the IBM Cloud IAM access token."""
        try:
            data = {
                "grant_type": "urn:iam:params:oauth:grant-type:apikey",
                "apikey": self.settings.watsonx_api_key
            }
            
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "application/json"
            }
            
            response = await self.client.post(
                self.settings.watsonx_auth_url,
                data=data,
                headers=headers
            )
            
            if response.status_code != 200:
                raise WatsonXAuthError(f"Authentication failed: {response.text}")
                
            token_data = response.json()
            self.access_token = token_data["access_token"]
            
            # Set expiration time (subtract 5 minutes for safety)
            expires_in = token_data.get("expires_in", 3600)
            self.token_expires_at = time.time() + expires_in - 300
            
            logger.info("IBM Cloud IAM token refreshed successfully")
            
        except Exception as e:
            logger.error("Failed to refresh IBM Cloud IAM token", error=str(e))
            raise WatsonXAuthError(f"Token refresh failed: {str(e)}")
            
    async def _ensure_valid_token(self):
        """Ensure we have a valid access token."""
        if not self.access_token or time.time() >= self.token_expires_at:
            await self._refresh_token()
            
    async def _make_request(
        self, 
        method: str, 
        url: str, 
        data: Optional[Dict[str, Any]] = None,
        files: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Make an authenticated request to IBM watsonx.ai."""
        await self._ensure_valid_token()
        
        request_headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Accept": "application/json"
        }
        
        if headers:
            request_headers.update(headers)
            
        if data and not files:
            request_headers["Content-Type"] = "application/json"
            
        try:
            if method.upper() == "POST":
                if files:
                    response = await self.client.post(url, data=data, files=files, headers=request_headers)
                else:
                    response = await self.client.post(url, json=data, headers=request_headers)
            elif method.upper() == "GET":
                response = await self.client.get(url, params=data, headers=request_headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
            if response.status_code not in [200, 201]:
                error_msg = f"API request failed: {response.status_code} - {response.text}"
                logger.error("WatsonX API error", status_code=response.status_code, response=response.text)
                raise WatsonXAPIError(error_msg)
                
            return response.json()
            
        except httpx.RequestError as e:
            logger.error("HTTP request error", error=str(e))
            raise WatsonXAPIError(f"Request failed: {str(e)}")
            
    async def generate_text(
        self, 
        prompt: str, 
        model_id: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate text using IBM Granite models."""
        model_config = self.settings.get_model_config("text")
        
        payload = {
            "model_id": model_id or model_config["model_id"],
            "input": prompt,
            "parameters": {**model_config["parameters"], **(parameters or {})},
            "project_id": self.settings.watsonx_project_id
        }
        
        url = f"{self.settings.watsonx_url}/ml/v1/text/generation"
        
        logger.info("Generating text", model_id=payload["model_id"], prompt_length=len(prompt))
        
        result = await self._make_request("POST", url, payload)
        
        # TODO: Extract medical entities from generated text
        # This would integrate with medical NER models
        
        return result
        
    async def speech_to_text(
        self, 
        audio_data: bytes, 
        content_type: str = "audio/wav",
        model_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Convert speech to text using IBM Granite Speech models."""
        # TODO: Implement actual IBM watsonx.ai speech-to-text integration
        # For now, return a placeholder response
        
        logger.info("Processing speech to text", audio_size=len(audio_data), content_type=content_type)
        
        # Placeholder implementation
        # In a real implementation, this would call IBM's speech recognition service
        return {
            "transcription": "TODO: Implement IBM Granite Speech-to-Text integration",
            "confidence": 0.95,
            "alternatives": [],
            "medical_entities": []
        }
        
    async def text_to_speech(
        self, 
        text: str, 
        voice: str = "en-US_AllisonV3Voice",
        format: str = "audio/wav"
    ) -> bytes:
        """Convert text to speech using IBM Watson Text to Speech."""
        # TODO: Implement actual IBM Watson TTS integration
        # For now, return placeholder audio data
        
        logger.info("Converting text to speech", text_length=len(text), voice=voice, format=format)
        
        # Placeholder implementation
        # In a real implementation, this would call IBM's TTS service
        return b"TODO: Implement IBM Watson Text-to-Speech integration"
        
    async def extract_medical_entities(self, text: str) -> List[Dict[str, Any]]:
        """Extract medical entities from text using IBM Granite models."""
        # TODO: Implement medical entity extraction
        # This could use IBM's NLU service or custom Granite models
        
        logger.info("Extracting medical entities", text_length=len(text))
        
        # Placeholder implementation
        entities = []
        
        # Simple keyword-based extraction (replace with actual NER)
        medical_keywords = {
            "headache": {"type": "symptom", "severity": None},
            "fever": {"type": "symptom", "severity": None},
            "ibuprofen": {"type": "medication", "dosage": None},
            "aspirin": {"type": "medication", "dosage": None},
            "nausea": {"type": "symptom", "severity": None}
        }
        
        text_lower = text.lower()
        for keyword, info in medical_keywords.items():
            if keyword in text_lower:
                entities.append({
                    "text": keyword,
                    "type": info["type"],
                    "confidence": 0.8,
                    "start_offset": text_lower.find(keyword),
                    "end_offset": text_lower.find(keyword) + len(keyword)
                })
                
        return entities
        
    async def health_check(self) -> Dict[str, Any]:
        """Check the health of IBM watsonx.ai services."""
        try:
            await self._ensure_valid_token()
            
            # Simple health check - try to list available models
            url = f"{self.settings.watsonx_url}/ml/v1/foundation_model_specs"
            params = {"project_id": self.settings.watsonx_project_id, "limit": 1}
            
            result = await self._make_request("GET", url, params)
            
            return {
                "status": "healthy",
                "authenticated": True,
                "models_available": len(result.get("resources", [])) > 0,
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "authenticated": False,
                "error": str(e),
                "timestamp": time.time()
            }
