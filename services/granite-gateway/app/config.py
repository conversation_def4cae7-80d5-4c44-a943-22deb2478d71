"""
Configuration settings for Granite Gateway Service
"""

from functools import lru_cache
from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )
    
    # Application settings
    app_name: str = "SymptomOS Granite Gateway"
    debug: bool = Field(default=False, description="Enable debug mode")
    host: str = Field(default="0.0.0.0", description="Host to bind to")
    port: int = Field(default=8000, description="Port to bind to")
    
    # Security settings
    secret_key: str = Field(..., description="Secret key for JWT tokens")
    algorithm: str = Field(default="HS256", description="JWT algorithm")
    access_token_expire_minutes: int = Field(default=30, description="Token expiration time")
    
    # CORS settings
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:5173"],
        description="Allowed CORS origins"
    )
    allowed_hosts: List[str] = Field(
        default=["localhost", "127.0.0.1", "0.0.0.0"],
        description="Allowed hosts"
    )
    
    # IBM watsonx.ai settings
    watsonx_api_key: str = Field(..., description="IBM watsonx.ai API key")
    watsonx_project_id: str = Field(..., description="IBM watsonx.ai project ID")
    watsonx_url: str = Field(
        default="https://us-south.ml.cloud.ibm.com",
        description="IBM watsonx.ai service URL"
    )
    watsonx_instance_id: Optional[str] = Field(
        default=None,
        description="IBM watsonx.ai instance ID"
    )
    
    # Model settings
    default_text_model: str = Field(
        default="ibm/granite-13b-chat-v2",
        description="Default text generation model"
    )
    default_speech_model: str = Field(
        default="ibm/granite-speech-v1",
        description="Default speech recognition model"
    )
    default_tts_model: str = Field(
        default="ibm/granite-tts-v1",
        description="Default text-to-speech model"
    )
    
    # Processing limits
    max_audio_size_mb: int = Field(default=25, description="Maximum audio file size in MB")
    max_text_length: int = Field(default=4000, description="Maximum text length for generation")
    max_tokens: int = Field(default=1000, description="Maximum tokens to generate")
    
    # Timeout settings
    request_timeout: int = Field(default=30, description="Request timeout in seconds")
    model_timeout: int = Field(default=60, description="Model processing timeout in seconds")
    
    # Cache settings
    enable_cache: bool = Field(default=True, description="Enable response caching")
    cache_ttl: int = Field(default=300, description="Cache TTL in seconds")
    
    # Monitoring settings
    enable_metrics: bool = Field(default=True, description="Enable Prometheus metrics")
    metrics_port: int = Field(default=8001, description="Metrics server port")
    
    # Logging settings
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format (json or text)")
    
    # Medical processing settings
    enable_medical_ner: bool = Field(
        default=True,
        description="Enable medical named entity recognition"
    )
    medical_confidence_threshold: float = Field(
        default=0.7,
        description="Confidence threshold for medical entities"
    )
    
    # Rate limiting
    rate_limit_requests: int = Field(default=100, description="Requests per minute")
    rate_limit_window: int = Field(default=60, description="Rate limit window in seconds")
    
    # Health check settings
    health_check_interval: int = Field(default=30, description="Health check interval in seconds")
    
    @property
    def watsonx_auth_url(self) -> str:
        """Get the IBM Cloud IAM authentication URL."""
        return "https://iam.cloud.ibm.com/identity/token"
    
    @property
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return not self.debug
    
    def get_model_config(self, model_type: str) -> dict:
        """Get model configuration for a specific type."""
        configs = {
            "text": {
                "model_id": self.default_text_model,
                "parameters": {
                    "max_new_tokens": self.max_tokens,
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "repetition_penalty": 1.1
                }
            },
            "speech": {
                "model_id": self.default_speech_model,
                "parameters": {
                    "language": "en-US",
                    "enable_medical_terms": True
                }
            },
            "tts": {
                "model_id": self.default_tts_model,
                "parameters": {
                    "voice": "en-US_AllisonV3Voice",
                    "format": "audio/wav"
                }
            }
        }
        return configs.get(model_type, {})


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()
