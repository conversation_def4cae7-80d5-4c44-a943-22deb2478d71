"""
Embedding Agent - Vector Embeddings using IBM Granite Embedding

Handles vector generation for similarity search and trend detection using
ibm/granite-embedding-107m-multilingual with caching and batch processing.

Author: SymptomOS Team
Version: 0.1
"""

import asyncio
import hashlib
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import structlog
import redis.asyncio as redis
import numpy as np
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

from .config import Settings
from .granite_client import GraniteEmbeddingClient
from .vector_store import VectorStore
from .similarity_engine import SimilarityEngine
from .trend_detector import TrendDetector
from .schemas import (
    EmbeddingRequest, EmbeddingResponse, SimilarityRequest,
    SimilarityResponse, TrendAnalysisRequest, TrendAnalysisResponse,
    VectorMetadata, CacheStats
)

logger = structlog.get_logger()

# Global state
app_state = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    settings = Settings()
    app_state["settings"] = settings
    app_state["redis"] = redis.from_url(settings.redis_url)
    app_state["granite_client"] = GraniteEmbeddingClient(settings)
    app_state["vector_store"] = VectorStore(settings)
    app_state["similarity_engine"] = SimilarityEngine(settings)
    app_state["trend_detector"] = TrendDetector(settings)
    
    # Initialize clients
    await app_state["granite_client"].initialize()
    await app_state["vector_store"].initialize()
    
    logger.info("Embedding agent started", model="granite-embedding-107m-multilingual", port=settings.port)
    
    yield
    
    # Shutdown
    await app_state["redis"].close()
    await app_state["granite_client"].cleanup()
    logger.info("Embedding agent stopped")

app = FastAPI(
    title="SymptomOS Embedding Agent",
    description="Vector embeddings and similarity search using IBM Granite Embedding",
    version="0.1.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.post("/v1/embed", response_model=EmbeddingResponse)
async def generate_embeddings(
    request: EmbeddingRequest
) -> EmbeddingResponse:
    """
    Generate vector embeddings using Granite Embedding model.
    
    Supports batch processing and caching with node-hash strategy.
    Optimized for medical text with $0.10/M token cost efficiency.
    """
    settings = app_state["settings"]
    granite_client = app_state["granite_client"]
    vector_store = app_state["vector_store"]
    
    start_time = time.time()
    
    try:
        # Step 1: Check cache for existing embeddings
        cached_results = []
        texts_to_embed = []
        cache_keys = []
        
        for text in request.texts:
            cache_key = _generate_cache_key(text, request.model_params)
            cached_vector = await _get_cached_embedding(cache_key)
            
            if cached_vector is not None:
                cached_results.append({
                    "text": text,
                    "vector": cached_vector,
                    "cached": True
                })
            else:
                texts_to_embed.append(text)
                cache_keys.append(cache_key)
        
        # Step 2: Generate embeddings for non-cached texts
        new_embeddings = []
        if texts_to_embed:
            # Batch process for efficiency
            batch_results = await granite_client.generate_embeddings_batch(
                texts_to_embed,
                **request.model_params
            )
            
            # Cache new embeddings
            for i, (text, vector) in enumerate(zip(texts_to_embed, batch_results)):
                cache_key = cache_keys[i]
                await _cache_embedding(cache_key, vector, request.cache_ttl)
                
                new_embeddings.append({
                    "text": text,
                    "vector": vector,
                    "cached": False
                })
        
        # Step 3: Combine cached and new results
        all_embeddings = cached_results + new_embeddings
        
        # Step 4: Store in vector store if requested
        if request.store_vectors:
            for embedding in all_embeddings:
                metadata = VectorMetadata(
                    text=embedding["text"],
                    patient_id=request.patient_id,
                    timestamp=datetime.utcnow(),
                    domain=request.domain,
                    source=request.source
                )
                
                await vector_store.store_vector(
                    embedding["vector"],
                    metadata,
                    request.collection_name
                )
        
        processing_time = time.time() - start_time
        
        # Log metrics
        await _log_embedding_metrics(
            total_texts=len(request.texts),
            cached_count=len(cached_results),
            new_count=len(new_embeddings),
            processing_time=processing_time
        )
        
        return EmbeddingResponse(
            embeddings=[emb["vector"] for emb in all_embeddings],
            texts=request.texts,
            model_used="granite-embedding-107m-multilingual",
            processing_time=processing_time,
            cache_hits=len(cached_results),
            cache_misses=len(new_embeddings),
            vector_dimension=len(all_embeddings[0]["vector"]) if all_embeddings else 0,
            metadata={
                "batch_size": len(request.texts),
                "domain": request.domain,
                "cached_percentage": len(cached_results) / len(request.texts) * 100,
                "cost_tokens": sum(len(text.split()) for text in texts_to_embed)
            }
        )
        
    except Exception as e:
        logger.error("Embedding generation failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Embedding generation failed: {str(e)}"
        )


@app.post("/v1/similarity", response_model=SimilarityResponse)
async def find_similar_vectors(
    request: SimilarityRequest
) -> SimilarityResponse:
    """
    Find similar vectors using cosine similarity.
    
    Supports both text-to-vector and vector-to-vector similarity search.
    """
    similarity_engine = app_state["similarity_engine"]
    granite_client = app_state["granite_client"]
    
    try:
        # Step 1: Get query vector
        if request.query_text:
            # Generate embedding for query text
            query_vectors = await granite_client.generate_embeddings_batch([request.query_text])
            query_vector = query_vectors[0]
        elif request.query_vector:
            query_vector = request.query_vector
        else:
            raise ValueError("Either query_text or query_vector must be provided")
        
        # Step 2: Perform similarity search
        similar_results = await similarity_engine.find_similar(
            query_vector=query_vector,
            collection_name=request.collection_name,
            top_k=request.top_k,
            similarity_threshold=request.similarity_threshold,
            filters=request.filters
        )
        
        return SimilarityResponse(
            query_text=request.query_text,
            results=similar_results,
            total_found=len(similar_results),
            search_time=time.time() - time.time(),  # Will be calculated in similarity_engine
            collection_name=request.collection_name
        )
        
    except Exception as e:
        logger.error("Similarity search failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Similarity search failed: {str(e)}"
        )


@app.post("/v1/trends", response_model=TrendAnalysisResponse)
async def analyze_trends(
    request: TrendAnalysisRequest
) -> TrendAnalysisResponse:
    """
    Analyze trends in medical data using vector embeddings.
    
    Detects patterns, clusters, and temporal changes in patient data.
    """
    trend_detector = app_state["trend_detector"]
    
    try:
        # Perform trend analysis
        trends = await trend_detector.analyze_trends(
            patient_id=request.patient_id,
            time_range=request.time_range,
            trend_types=request.trend_types,
            collection_name=request.collection_name
        )
        
        return TrendAnalysisResponse(
            patient_id=request.patient_id,
            time_range=request.time_range,
            trends=trends,
            analysis_timestamp=datetime.utcnow(),
            metadata={
                "trend_types_analyzed": request.trend_types,
                "collection": request.collection_name
            }
        )
        
    except Exception as e:
        logger.error("Trend analysis failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Trend analysis failed: {str(e)}"
        )


@app.post("/v1/cluster")
async def cluster_vectors(
    collection_name: str,
    num_clusters: int = 5,
    algorithm: str = "kmeans",
    filters: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Cluster vectors in a collection for pattern discovery.
    
    Useful for identifying symptom patterns and patient groups.
    """
    similarity_engine = app_state["similarity_engine"]
    
    try:
        clusters = await similarity_engine.cluster_vectors(
            collection_name=collection_name,
            num_clusters=num_clusters,
            algorithm=algorithm,
            filters=filters
        )
        
        return {
            "clusters": clusters,
            "num_clusters": len(clusters),
            "algorithm": algorithm,
            "collection": collection_name,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Vector clustering failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Vector clustering failed: {str(e)}"
        )


@app.get("/v1/collections")
async def list_collections() -> Dict[str, Any]:
    """List available vector collections"""
    vector_store = app_state["vector_store"]
    
    try:
        collections = await vector_store.list_collections()
        return {
            "collections": collections,
            "total": len(collections),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to list collections", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list collections: {str(e)}"
        )


@app.get("/v1/cache/stats", response_model=CacheStats)
async def get_cache_stats() -> CacheStats:
    """Get embedding cache statistics"""
    try:
        redis_client = app_state["redis"]
        
        # Get cache metrics
        total_keys = await redis_client.dbsize()
        cache_keys = await redis_client.keys("embed:cache:*")
        cache_size = len(cache_keys)
        
        # Calculate hit rate from metrics
        hits = await redis_client.get("metrics:embed:cache_hits") or "0"
        misses = await redis_client.get("metrics:embed:cache_misses") or "0"
        
        total_requests = int(hits) + int(misses)
        hit_rate = int(hits) / total_requests if total_requests > 0 else 0.0
        
        return CacheStats(
            total_cached_embeddings=cache_size,
            cache_hit_rate=hit_rate,
            cache_size_mb=cache_size * 0.004,  # Approximate 4KB per embedding
            total_requests=total_requests,
            cache_hits=int(hits),
            cache_misses=int(misses)
        )
        
    except Exception as e:
        logger.error("Failed to get cache stats", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get cache stats: {str(e)}"
        )


@app.delete("/v1/cache")
async def clear_cache() -> Dict[str, str]:
    """Clear embedding cache"""
    try:
        redis_client = app_state["redis"]
        
        # Delete all cache keys
        cache_keys = await redis_client.keys("embed:cache:*")
        if cache_keys:
            await redis_client.delete(*cache_keys)
        
        return {
            "status": "success",
            "cleared_keys": len(cache_keys),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to clear cache", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear cache: {str(e)}"
        )


@app.get("/v1/health")
async def health_check() -> Dict[str, Any]:
    """Health check with model and cache status"""
    granite_client = app_state["granite_client"]
    vector_store = app_state["vector_store"]
    
    try:
        # Check Granite model availability
        granite_healthy = await granite_client.health_check()
        
        # Check vector store
        vector_store_healthy = await vector_store.health_check()
        
        # Check Redis connection
        redis_client = app_state["redis"]
        await redis_client.ping()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "models": {
                "granite_embedding": "healthy" if granite_healthy else "unhealthy",
                "vector_store": "healthy" if vector_store_healthy else "unhealthy"
            },
            "redis": "healthy",
            "version": "0.1.0"
        }
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.get("/healthz")
async def kubernetes_health() -> Dict[str, str]:
    """Kubernetes-style health check"""
    try:
        granite_client = app_state["granite_client"]
        if not await granite_client.health_check():
            raise Exception("Granite client unhealthy")
        
        return {"status": "ok"}
    except Exception:
        raise HTTPException(status_code=503, detail="unhealthy")


def _generate_cache_key(text: str, model_params: Dict[str, Any]) -> str:
    """Generate cache key for text and model parameters"""
    content = f"{text}:{json.dumps(model_params, sort_keys=True)}"
    return f"embed:cache:{hashlib.md5(content.encode()).hexdigest()}"


async def _get_cached_embedding(cache_key: str) -> Optional[List[float]]:
    """Get cached embedding vector"""
    try:
        redis_client = app_state["redis"]
        cached_data = await redis_client.get(cache_key)
        
        if cached_data:
            await redis_client.incr("metrics:embed:cache_hits")
            return json.loads(cached_data)
        else:
            await redis_client.incr("metrics:embed:cache_misses")
            return None
            
    except Exception as e:
        logger.warning("Cache retrieval failed", error=str(e))
        return None


async def _cache_embedding(cache_key: str, vector: List[float], ttl: int):
    """Cache embedding vector"""
    try:
        redis_client = app_state["redis"]
        await redis_client.setex(
            cache_key,
            ttl,
            json.dumps(vector)
        )
    except Exception as e:
        logger.warning("Cache storage failed", error=str(e))


async def _log_embedding_metrics(
    total_texts: int,
    cached_count: int,
    new_count: int,
    processing_time: float
):
    """Log embedding metrics to Redis"""
    try:
        redis_client = app_state["redis"]
        
        metrics = {
            "total_texts": total_texts,
            "cached_count": cached_count,
            "new_count": new_count,
            "processing_time": processing_time,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await redis_client.lpush("metrics:embed:requests", str(metrics))
        await redis_client.ltrim("metrics:embed:requests", 0, 999)  # Keep last 1000
        
    except Exception as e:
        logger.warning("Failed to log metrics", error=str(e))


if __name__ == "__main__":
    import uvicorn
    settings = Settings()
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.port,
        reload=settings.debug
    )
