"""
Guardian Agent - Safety and Policy Checking using IBM Granite Guardian

Critical safety valve using ibm/granite-guardian-3-8b for medical-grade
content validation. Runs after every generation with binary OK/Reject decisions.

Author: SymptomOS Team
Version: 0.1
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
import structlog
import redis.asyncio as redis
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

from .config import Settings
from .granite_client import GraniteGuardianClient
from .safety_checker import SafetyChecker
from .policy_engine import PolicyEngine
from .violation_tracker import ViolationTracker
from .schemas import (
    SafetyCheckRequest, SafetyCheckResponse, PolicyViolation,
    ContentAnalysis, RiskAssessment, SafetyMetrics
)

logger = structlog.get_logger()

# Global state
app_state = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    settings = Settings()
    app_state["settings"] = settings
    app_state["redis"] = redis.from_url(settings.redis_url)
    app_state["granite_client"] = GraniteGuardianClient(settings)
    app_state["safety_checker"] = SafetyChecker(settings)
    app_state["policy_engine"] = PolicyEngine(settings)
    app_state["violation_tracker"] = ViolationTracker(settings)
    
    # Initialize clients
    await app_state["granite_client"].initialize()
    await app_state["policy_engine"].initialize()
    
    logger.info("Guardian agent started", model="granite-guardian-3-8b", port=settings.port)
    
    yield
    
    # Shutdown
    await app_state["redis"].close()
    await app_state["granite_client"].cleanup()
    logger.info("Guardian agent stopped")

app = FastAPI(
    title="SymptomOS Guardian Agent",
    description="Safety and policy checking using IBM Granite Guardian",
    version="0.1.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.post("/v1/safety-check", response_model=SafetyCheckResponse)
async def check_content_safety(
    request: SafetyCheckRequest
) -> SafetyCheckResponse:
    """
    Perform comprehensive safety check using Granite Guardian.
    
    Binary OK/Reject decision with detailed rationale for medical safety.
    Temperature=0.0 for deterministic safety decisions.
    """
    settings = app_state["settings"]
    granite_client = app_state["granite_client"]
    safety_checker = app_state["safety_checker"]
    policy_engine = app_state["policy_engine"]
    violation_tracker = app_state["violation_tracker"]
    
    start_time = time.time()
    
    try:
        # Step 1: Pre-screening with rule-based checks
        prescreening_result = await safety_checker.prescreen_content(
            content=request.content,
            content_type=request.content_type,
            context=request.context
        )
        
        # If pre-screening fails, reject immediately
        if not prescreening_result["safe"]:
            violation = await violation_tracker.log_violation(
                content=request.content,
                violation_type="prescreening_failure",
                reason=prescreening_result["reason"],
                severity="high",
                source_agent=request.source_agent
            )
            
            return SafetyCheckResponse(
                safe=False,
                decision="REJECT",
                confidence=1.0,  # High confidence in rule-based rejection
                rationale=prescreening_result["reason"],
                violation_id=violation.violation_id,
                processing_time=time.time() - start_time,
                model_used="rule_based_prescreening",
                risk_level="high"
            )
        
        # Step 2: Granite Guardian analysis
        guardian_result = await granite_client.analyze_content_safety(
            content=request.content,
            content_type=request.content_type,
            context=request.context,
            temperature=0.0,  # Deterministic for safety
            safety_categories=request.safety_categories
        )
        
        # Step 3: Policy compliance check
        policy_result = await policy_engine.check_policy_compliance(
            content=request.content,
            guardian_analysis=guardian_result,
            context=request.context
        )
        
        # Step 4: Make final safety decision
        final_decision = await safety_checker.make_final_decision(
            prescreening=prescreening_result,
            guardian_analysis=guardian_result,
            policy_check=policy_result
        )
        
        # Step 5: Log violation if content is unsafe
        violation_id = None
        if not final_decision["safe"]:
            violation = await violation_tracker.log_violation(
                content=request.content,
                violation_type=final_decision["violation_type"],
                reason=final_decision["rationale"],
                severity=final_decision["risk_level"],
                source_agent=request.source_agent,
                guardian_analysis=guardian_result,
                policy_analysis=policy_result
            )
            violation_id = violation.violation_id
        
        processing_time = time.time() - start_time
        
        # Log metrics
        await _log_safety_metrics(
            safe=final_decision["safe"],
            processing_time=processing_time,
            content_type=request.content_type,
            source_agent=request.source_agent,
            risk_level=final_decision["risk_level"]
        )
        
        return SafetyCheckResponse(
            safe=final_decision["safe"],
            decision="PASS" if final_decision["safe"] else "REJECT",
            confidence=final_decision["confidence"],
            rationale=final_decision["rationale"],
            violation_id=violation_id,
            processing_time=processing_time,
            model_used="granite-guardian-3-8b",
            risk_level=final_decision["risk_level"],
            safety_scores=guardian_result.get("safety_scores", {}),
            policy_violations=policy_result.get("violations", []),
            metadata={
                "content_length": len(request.content),
                "safety_categories_checked": request.safety_categories,
                "prescreening_passed": prescreening_result["safe"],
                "guardian_confidence": guardian_result.get("confidence", 0.0),
                "policy_compliance": policy_result.get("compliant", True)
            }
        )
        
    except Exception as e:
        logger.error("Safety check failed", error=str(e))
        
        # On error, default to REJECT for safety
        violation = await violation_tracker.log_violation(
            content=request.content,
            violation_type="system_error",
            reason=f"Safety check system error: {str(e)}",
            severity="critical",
            source_agent=request.source_agent
        )
        
        return SafetyCheckResponse(
            safe=False,
            decision="REJECT",
            confidence=1.0,
            rationale=f"Safety check system error - content rejected for safety",
            violation_id=violation.violation_id,
            processing_time=time.time() - start_time,
            model_used="error_fallback",
            risk_level="critical"
        )


@app.post("/v1/batch-check")
async def batch_safety_check(
    requests: List[SafetyCheckRequest]
) -> List[SafetyCheckResponse]:
    """
    Perform batch safety checking for multiple content items.
    
    Optimized for high-throughput safety validation.
    """
    if len(requests) > 50:  # Limit batch size
        raise HTTPException(
            status_code=400,
            detail="Batch size exceeds maximum limit (50 items)"
        )
    
    results = []
    for request in requests:
        try:
            result = await check_content_safety(request)
            results.append(result)
        except Exception as e:
            # Individual item failure shouldn't stop batch
            logger.error("Batch item failed", error=str(e))
            results.append(SafetyCheckResponse(
                safe=False,
                decision="REJECT",
                confidence=1.0,
                rationale="Batch processing error",
                processing_time=0.0,
                model_used="error_fallback",
                risk_level="critical"
            ))
    
    return results


@app.post("/v1/analyze", response_model=ContentAnalysis)
async def analyze_content_detailed(
    content: str,
    content_type: str = "text",
    include_explanations: bool = True
) -> ContentAnalysis:
    """
    Detailed content analysis without binary decision.
    
    Provides comprehensive risk assessment and explanations.
    """
    granite_client = app_state["granite_client"]
    safety_checker = app_state["safety_checker"]
    
    try:
        # Perform detailed analysis
        analysis = await granite_client.analyze_content_detailed(
            content=content,
            content_type=content_type,
            include_explanations=include_explanations
        )
        
        # Add risk assessment
        risk_assessment = await safety_checker.assess_risk_level(
            content=content,
            analysis=analysis
        )
        
        return ContentAnalysis(
            content_type=content_type,
            safety_scores=analysis["safety_scores"],
            risk_categories=analysis["risk_categories"],
            risk_assessment=risk_assessment,
            explanations=analysis.get("explanations", []) if include_explanations else [],
            confidence=analysis["confidence"],
            analyzed_at=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error("Content analysis failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Content analysis failed: {str(e)}"
        )


@app.get("/v1/violations")
async def get_recent_violations(
    limit: int = 100,
    severity: Optional[str] = None,
    source_agent: Optional[str] = None
) -> Dict[str, Any]:
    """Get recent safety violations with filtering"""
    violation_tracker = app_state["violation_tracker"]
    
    try:
        violations = await violation_tracker.get_violations(
            limit=limit,
            severity=severity,
            source_agent=source_agent
        )
        
        return {
            "violations": violations,
            "total": len(violations),
            "filters": {
                "severity": severity,
                "source_agent": source_agent
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get violations", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get violations: {str(e)}"
        )


@app.get("/v1/metrics", response_model=SafetyMetrics)
async def get_safety_metrics() -> SafetyMetrics:
    """Get safety checking metrics and statistics"""
    violation_tracker = app_state["violation_tracker"]
    
    try:
        metrics = await violation_tracker.get_safety_metrics()
        return metrics
        
    except Exception as e:
        logger.error("Failed to get safety metrics", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get safety metrics: {str(e)}"
        )


@app.get("/v1/policies")
async def list_safety_policies() -> Dict[str, Any]:
    """List active safety policies and rules"""
    policy_engine = app_state["policy_engine"]
    
    try:
        policies = await policy_engine.get_active_policies()
        return {
            "policies": policies,
            "total": len(policies),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to list policies", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list policies: {str(e)}"
        )


@app.post("/v1/emergency-block")
async def emergency_content_block(
    content_hash: str,
    reason: str,
    severity: str = "critical"
) -> Dict[str, str]:
    """Emergency block specific content by hash"""
    safety_checker = app_state["safety_checker"]
    
    try:
        await safety_checker.emergency_block_content(
            content_hash=content_hash,
            reason=reason,
            severity=severity
        )
        
        logger.critical(
            "Emergency content block activated",
            content_hash=content_hash,
            reason=reason
        )
        
        return {
            "status": "blocked",
            "content_hash": content_hash,
            "reason": reason,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Emergency block failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Emergency block failed: {str(e)}"
        )


@app.get("/v1/health")
async def health_check() -> Dict[str, Any]:
    """Health check with safety system status"""
    granite_client = app_state["granite_client"]
    policy_engine = app_state["policy_engine"]
    
    try:
        # Check Granite Guardian model
        guardian_healthy = await granite_client.health_check()
        
        # Check policy engine
        policy_healthy = await policy_engine.health_check()
        
        # Check Redis connection
        redis_client = app_state["redis"]
        await redis_client.ping()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "models": {
                "granite_guardian": "healthy" if guardian_healthy else "unhealthy",
                "policy_engine": "healthy" if policy_healthy else "unhealthy"
            },
            "redis": "healthy",
            "version": "0.1.0"
        }
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.get("/healthz")
async def kubernetes_health() -> Dict[str, str]:
    """Kubernetes-style health check"""
    try:
        granite_client = app_state["granite_client"]
        if not await granite_client.health_check():
            raise Exception("Granite Guardian client unhealthy")
        
        return {"status": "ok"}
    except Exception:
        raise HTTPException(status_code=503, detail="unhealthy")


async def _log_safety_metrics(
    safe: bool,
    processing_time: float,
    content_type: str,
    source_agent: str,
    risk_level: str
):
    """Log safety check metrics to Redis"""
    try:
        redis_client = app_state["redis"]
        
        metrics = {
            "safe": safe,
            "processing_time": processing_time,
            "content_type": content_type,
            "source_agent": source_agent,
            "risk_level": risk_level,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await redis_client.lpush("metrics:guardian:checks", str(metrics))
        await redis_client.ltrim("metrics:guardian:checks", 0, 999)  # Keep last 1000
        
        # Update counters
        await redis_client.incr(f"metrics:guardian:total_checks")
        if safe:
            await redis_client.incr(f"metrics:guardian:safe_content")
        else:
            await redis_client.incr(f"metrics:guardian:unsafe_content")
        
    except Exception as e:
        logger.warning("Failed to log metrics", error=str(e))


if __name__ == "__main__":
    import uvicorn
    settings = Settings()
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.port,
        reload=settings.debug
    )
