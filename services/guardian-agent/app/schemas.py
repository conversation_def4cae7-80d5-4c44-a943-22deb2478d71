"""
Guardian Agent Schemas

Pydantic models for safety checking, policy violations, and content analysis
using IBM Granite Guardian for medical-grade safety validation.
"""

from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class RiskLevel(str, Enum):
    """Risk level enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class SafetyCategory(str, Enum):
    """Safety categories for content analysis"""
    MEDICAL_ACCURACY = "medical_accuracy"
    PHI_PROTECTION = "phi_protection"
    HARMFUL_CONTENT = "harmful_content"
    POLICY_COMPLIANCE = "policy_compliance"
    MISINFORMATION = "misinformation"
    INAPPROPRIATE_ADVICE = "inappropriate_advice"


class ViolationType(str, Enum):
    """Types of safety violations"""
    PRESCREENING_FAILURE = "prescreening_failure"
    MEDICAL_MISINFORMATION = "medical_misinformation"
    PHI_LEAKAGE = "phi_leakage"
    HARMFUL_ADVICE = "harmful_advice"
    POLICY_VIOLATION = "policy_violation"
    INAPPROPRIATE_CONTENT = "inappropriate_content"
    SYSTEM_ERROR = "system_error"


class SafetyCheckRequest(BaseModel):
    """Request for safety checking"""
    content: str = Field(..., description="Content to check for safety")
    content_type: str = Field(default="text", description="Type of content (text, image, etc.)")
    source_agent: str = Field(..., description="Agent that generated the content")
    context: Optional[Dict[str, Any]] = Field(default=None, description="Additional context")
    safety_categories: List[SafetyCategory] = Field(
        default_factory=lambda: [SafetyCategory.MEDICAL_ACCURACY, SafetyCategory.PHI_PROTECTION],
        description="Safety categories to check"
    )
    patient_id: Optional[str] = Field(default=None, description="Associated patient ID")
    
    @validator('content')
    def validate_content(cls, v):
        if not v or not v.strip():
            raise ValueError("Content cannot be empty")
        if len(v) > 50000:  # 50KB limit
            raise ValueError("Content exceeds maximum length")
        return v


class SafetyCheckResponse(BaseModel):
    """Response from safety checking"""
    safe: bool = Field(..., description="Whether content is safe")
    decision: str = Field(..., description="PASS or REJECT")
    confidence: float = Field(..., description="Confidence in decision (0-1)")
    rationale: str = Field(..., description="Explanation for the decision")
    violation_id: Optional[str] = Field(default=None, description="Violation ID if unsafe")
    processing_time: float = Field(..., description="Processing time in seconds")
    model_used: str = Field(..., description="Model used for analysis")
    risk_level: RiskLevel = Field(..., description="Risk level assessment")
    safety_scores: Dict[str, float] = Field(default_factory=dict, description="Safety scores by category")
    policy_violations: List[str] = Field(default_factory=list, description="Policy violations found")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    @validator('confidence')
    def validate_confidence(cls, v):
        if not 0 <= v <= 1:
            raise ValueError("Confidence must be between 0 and 1")
        return v


class PolicyViolation(BaseModel):
    """Policy violation details"""
    violation_id: str = Field(..., description="Unique violation identifier")
    violation_type: ViolationType = Field(..., description="Type of violation")
    policy_name: str = Field(..., description="Name of violated policy")
    severity: RiskLevel = Field(..., description="Violation severity")
    description: str = Field(..., description="Description of the violation")
    content_excerpt: str = Field(..., description="Excerpt of violating content")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="When violation occurred")
    source_agent: str = Field(..., description="Agent that generated violating content")
    patient_id: Optional[str] = Field(default=None, description="Associated patient ID")
    remediation_suggestions: List[str] = Field(default_factory=list, description="Suggested fixes")
    
    @validator('content_excerpt')
    def validate_excerpt(cls, v):
        if len(v) > 500:  # Limit excerpt length
            return v[:500] + "..."
        return v


class ContentAnalysis(BaseModel):
    """Detailed content analysis results"""
    content_type: str = Field(..., description="Type of content analyzed")
    safety_scores: Dict[str, float] = Field(..., description="Safety scores by category")
    risk_categories: List[str] = Field(..., description="Identified risk categories")
    risk_assessment: "RiskAssessment" = Field(..., description="Overall risk assessment")
    explanations: List[str] = Field(default_factory=list, description="Detailed explanations")
    confidence: float = Field(..., description="Overall confidence in analysis")
    analyzed_at: datetime = Field(default_factory=datetime.utcnow, description="Analysis timestamp")


class RiskAssessment(BaseModel):
    """Risk assessment details"""
    overall_risk: RiskLevel = Field(..., description="Overall risk level")
    risk_factors: List[str] = Field(..., description="Identified risk factors")
    mitigation_strategies: List[str] = Field(default_factory=list, description="Risk mitigation strategies")
    requires_human_review: bool = Field(default=False, description="Whether human review is needed")
    escalation_required: bool = Field(default=False, description="Whether escalation is required")


class SafetyMetrics(BaseModel):
    """Safety checking metrics and statistics"""
    total_checks: int = Field(..., description="Total safety checks performed")
    safe_content_count: int = Field(..., description="Number of safe content items")
    unsafe_content_count: int = Field(..., description="Number of unsafe content items")
    violation_counts: Dict[str, int] = Field(..., description="Violation counts by type")
    average_processing_time: float = Field(..., description="Average processing time")
    model_performance: Dict[str, float] = Field(..., description="Model performance metrics")
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")


class SafetyAlert(BaseModel):
    """Safety alert for logging to OGCE"""
    alert_id: str = Field(..., description="Unique alert identifier")
    alert_type: str = Field(default="SAFETY_VIOLATION", description="Type of safety alert")
    violation: PolicyViolation = Field(..., description="Associated violation")
    affected_entities: List[str] = Field(default_factory=list, description="Affected entity IDs")
    notification_sent: bool = Field(default=False, description="Whether notification was sent")
    acknowledged: bool = Field(default=False, description="Whether alert was acknowledged")
    acknowledged_by: Optional[str] = Field(default=None, description="Who acknowledged the alert")
    acknowledged_at: Optional[datetime] = Field(default=None, description="When alert was acknowledged")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Alert creation time")


class GuardianFeedback(BaseModel):
    """Guardian feedback for UI display"""
    feedback_id: str = Field(..., description="Unique feedback identifier")
    message: str = Field(..., description="User-friendly feedback message")
    severity: RiskLevel = Field(..., description="Feedback severity")
    violation_type: ViolationType = Field(..., description="Type of violation")
    action_taken: str = Field(..., description="Action taken by guardian")
    recommendations: List[str] = Field(default_factory=list, description="User recommendations")
    show_duration: int = Field(default=5000, description="How long to show feedback (ms)")
    dismissible: bool = Field(default=True, description="Whether user can dismiss")
    requires_acknowledgment: bool = Field(default=False, description="Whether acknowledgment is required")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Feedback timestamp")
    
    @validator('show_duration')
    def validate_duration(cls, v):
        if v < 1000 or v > 30000:  # 1-30 seconds
            raise ValueError("Show duration must be between 1-30 seconds")
        return v


class EmergencyBlock(BaseModel):
    """Emergency content block"""
    block_id: str = Field(..., description="Unique block identifier")
    content_hash: str = Field(..., description="Hash of blocked content")
    reason: str = Field(..., description="Reason for emergency block")
    severity: RiskLevel = Field(..., description="Block severity")
    blocked_at: datetime = Field(default_factory=datetime.utcnow, description="Block timestamp")
    blocked_by: str = Field(..., description="Who initiated the block")
    expires_at: Optional[datetime] = Field(default=None, description="Block expiration")
    active: bool = Field(default=True, description="Whether block is active")


# Update forward references
ContentAnalysis.model_rebuild()
RiskAssessment.model_rebuild()
