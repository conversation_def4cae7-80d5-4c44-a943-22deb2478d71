"""
Violation Tracker

Tracks and logs safety violations for the Guardian Agent.
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import structlog
import redis.asyncio as redis

from .config import Settings
from .schemas import PolicyViolation, ViolationType, RiskLevel, SafetyMetrics


logger = structlog.get_logger()


class ViolationTracker:
    """Tracks and manages safety violations"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.redis_client = None
    
    async def initialize(self):
        """Initialize the violation tracker"""
        self.redis_client = redis.from_url(self.settings.redis_url)
        logger.info("Violation tracker initialized")
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.redis_client:
            await self.redis_client.close()
    
    async def log_violation(
        self,
        content: str,
        violation_type: str,
        reason: str,
        severity: str,
        source_agent: str,
        guardian_analysis: Optional[Dict[str, Any]] = None,
        policy_analysis: Optional[Dict[str, Any]] = None,
        patient_id: Optional[str] = None
    ) -> PolicyViolation:
        """Log a safety violation"""
        
        violation_id = str(uuid.uuid4())
        
        # Create content excerpt (first 200 chars)
        content_excerpt = content[:200] + "..." if len(content) > 200 else content
        
        # Generate remediation suggestions based on violation type
        remediation_suggestions = self._get_remediation_suggestions(violation_type)
        
        violation = PolicyViolation(
            violation_id=violation_id,
            violation_type=ViolationType(violation_type),
            policy_name=f"Guardian Policy - {violation_type.replace('_', ' ').title()}",
            severity=RiskLevel(severity),
            description=reason,
            content_excerpt=content_excerpt,
            source_agent=source_agent,
            patient_id=patient_id,
            remediation_suggestions=remediation_suggestions
        )
        
        # Store in Redis
        try:
            if self.redis_client:
                violation_data = {
                    **violation.dict(),
                    "guardian_analysis": guardian_analysis,
                    "policy_analysis": policy_analysis,
                    "full_content_hash": hash(content)
                }
                
                # Store violation
                await self.redis_client.lpush(
                    "guardian:violations",
                    json.dumps(violation_data, default=str)
                )
                
                # Keep only recent violations
                await self.redis_client.ltrim("guardian:violations", 0, 999)
                
                # Update metrics
                await self._update_metrics(violation_type, severity)
                
                logger.warning(
                    "Safety violation logged",
                    violation_id=violation_id,
                    type=violation_type,
                    severity=severity,
                    source_agent=source_agent
                )
        
        except Exception as e:
            logger.error("Failed to store violation", error=str(e))
        
        return violation
    
    async def get_violations(
        self,
        limit: int = 100,
        severity: Optional[str] = None,
        source_agent: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get recent violations with filtering"""
        
        try:
            if not self.redis_client:
                return []
            
            # Get all violations
            violations_json = await self.redis_client.lrange("guardian:violations", 0, limit - 1)
            
            violations = []
            for violation_json in violations_json:
                try:
                    violation = json.loads(violation_json)
                    
                    # Apply filters
                    if severity and violation.get("severity") != severity:
                        continue
                    
                    if source_agent and violation.get("source_agent") != source_agent:
                        continue
                    
                    violations.append(violation)
                    
                except json.JSONDecodeError:
                    continue
            
            return violations
            
        except Exception as e:
            logger.error("Failed to get violations", error=str(e))
            return []
    
    async def get_safety_metrics(self) -> SafetyMetrics:
        """Get safety metrics and statistics"""
        
        try:
            if not self.redis_client:
                return self._empty_metrics()
            
            # Get basic counters
            total_checks = await self.redis_client.get("metrics:guardian:total_checks") or 0
            safe_content = await self.redis_client.get("metrics:guardian:safe_content") or 0
            unsafe_content = await self.redis_client.get("metrics:guardian:unsafe_content") or 0
            
            # Get violation counts by type
            violation_counts = {}
            for violation_type in ViolationType:
                count = await self.redis_client.get(f"metrics:guardian:violations:{violation_type.value}") or 0
                violation_counts[violation_type.value] = int(count)
            
            # Calculate average processing time (simplified)
            avg_processing_time = 0.5  # Default estimate
            
            # Model performance metrics (simplified)
            model_performance = {
                "accuracy": 0.95,
                "precision": 0.93,
                "recall": 0.97,
                "f1_score": 0.95
            }
            
            return SafetyMetrics(
                total_checks=int(total_checks),
                safe_content_count=int(safe_content),
                unsafe_content_count=int(unsafe_content),
                violation_counts=violation_counts,
                average_processing_time=avg_processing_time,
                model_performance=model_performance
            )
            
        except Exception as e:
            logger.error("Failed to get safety metrics", error=str(e))
            return self._empty_metrics()
    
    async def _update_metrics(self, violation_type: str, severity: str):
        """Update violation metrics"""
        try:
            if self.redis_client:
                await self.redis_client.incr(f"metrics:guardian:violations:{violation_type}")
                await self.redis_client.incr(f"metrics:guardian:violations:severity:{severity}")
        except Exception as e:
            logger.warning("Failed to update metrics", error=str(e))
    
    def _get_remediation_suggestions(self, violation_type: str) -> List[str]:
        """Get remediation suggestions for violation type"""
        
        suggestions_map = {
            "prescreening_failure": [
                "Review content for prohibited terms",
                "Ensure medical accuracy",
                "Check for appropriate medical disclaimers"
            ],
            "medical_misinformation": [
                "Verify medical facts with reliable sources",
                "Add appropriate medical disclaimers",
                "Consult with medical professionals"
            ],
            "phi_leakage": [
                "Remove all personally identifiable information",
                "Use anonymized examples",
                "Implement proper data masking"
            ],
            "harmful_advice": [
                "Provide general information only",
                "Add disclaimer to consult healthcare providers",
                "Remove specific medical recommendations"
            ],
            "policy_violation": [
                "Review content against safety policies",
                "Ensure compliance with medical guidelines",
                "Add appropriate warnings and disclaimers"
            ],
            "inappropriate_content": [
                "Review content for appropriateness",
                "Ensure professional medical tone",
                "Remove any offensive or inappropriate language"
            ],
            "system_error": [
                "Check system logs for errors",
                "Verify service connectivity",
                "Contact system administrator"
            ]
        }
        
        return suggestions_map.get(violation_type, [
            "Review content for safety compliance",
            "Consult safety guidelines",
            "Contact administrator if needed"
        ])
    
    def _empty_metrics(self) -> SafetyMetrics:
        """Return empty metrics object"""
        return SafetyMetrics(
            total_checks=0,
            safe_content_count=0,
            unsafe_content_count=0,
            violation_counts={},
            average_processing_time=0.0,
            model_performance={}
        )
