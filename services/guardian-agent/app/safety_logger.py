"""
Safety Logger

Logs safety violations and alerts to the OGCE Graph service.
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
import structlog
import httpx

from .config import Settings
from .schemas import PolicyViolation, SafetyAlert, ViolationType, RiskLevel


logger = structlog.get_logger()


class SafetyLogger:
    """Logs safety events to OGCE Graph service"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.ogce_config = settings.get_ogce_config()
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def cleanup(self):
        """Cleanup resources"""
        await self.client.aclose()
    
    async def log_safety_alert(
        self,
        violation: PolicyViolation,
        affected_entities: Optional[List[str]] = None,
        send_notification: bool = True
    ) -> Optional[str]:
        """
        Log a safety alert to the OGCE Graph service.
        
        Returns the created node ID if successful, None otherwise.
        """
        
        try:
            alert_id = str(uuid.uuid4())
            
            # Create safety alert
            alert = SafetyAlert(
                alert_id=alert_id,
                violation=violation,
                affected_entities=affected_entities or [],
                notification_sent=send_notification
            )
            
            # Create SAFETY_ALERT node in the graph
            node_id = await self._create_safety_alert_node(alert)
            
            if node_id:
                logger.info(
                    "Safety alert logged to OGCE",
                    alert_id=alert_id,
                    node_id=node_id,
                    violation_type=violation.violation_type.value,
                    severity=violation.severity.value
                )
                
                # If patient is involved, create relationship
                if violation.patient_id:
                    await self._link_alert_to_patient(node_id, violation.patient_id)
                
                # Send notification if enabled
                if send_notification and self.settings.enable_notifications:
                    await self._send_safety_notification(alert)
            
            return node_id
            
        except Exception as e:
            logger.error("Failed to log safety alert", error=str(e), violation_id=violation.violation_id)
            return None
    
    async def _create_safety_alert_node(self, alert: SafetyAlert) -> Optional[str]:
        """Create a SAFETY_ALERT node in the OGCE graph"""
        
        try:
            # Prepare node properties
            properties = {
                "alert_id": alert.alert_id,
                "alert_type": alert.alert_type,
                "violation_id": alert.violation.violation_id,
                "violation_type": alert.violation.violation_type.value,
                "policy_name": alert.violation.policy_name,
                "severity": alert.violation.severity.value,
                "description": alert.violation.description,
                "content_excerpt": alert.violation.content_excerpt,
                "source_agent": alert.violation.source_agent,
                "patient_id": alert.violation.patient_id,
                "remediation_suggestions": alert.violation.remediation_suggestions,
                "affected_entities": alert.affected_entities,
                "notification_sent": alert.notification_sent,
                "acknowledged": alert.acknowledged,
                "timestamp": alert.created_at.isoformat(),
                "entity_type": "safety_alert",
                "source": "guardian_agent"
            }
            
            # Create node request
            node_request = {
                "node_type": "safety_alert",
                "properties": properties,
                "timestamp": alert.created_at.isoformat()
            }
            
            # Send to OGCE Graph service
            response = await self.client.post(
                f"{self.ogce_config['url']}/v1/nodes",
                json=node_request,
                headers={
                    "Authorization": f"Bearer {self.ogce_config['auth_token']}",
                    "Content-Type": "application/json"
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("node_id")
            else:
                logger.error(
                    "Failed to create safety alert node",
                    status_code=response.status_code,
                    response=response.text
                )
                return None
                
        except Exception as e:
            logger.error("Error creating safety alert node", error=str(e))
            return None
    
    async def _link_alert_to_patient(self, alert_node_id: str, patient_id: str):
        """Create relationship between safety alert and patient"""
        
        try:
            # Create edge request
            edge_request = {
                "source_id": alert_node_id,
                "target_id": patient_id,
                "relationship_type": "SAFETY_ALERT_FOR",
                "properties": {
                    "created_at": datetime.utcnow().isoformat(),
                    "relationship_source": "guardian_agent"
                }
            }
            
            response = await self.client.post(
                f"{self.ogce_config['url']}/v1/edges",
                json=edge_request,
                headers={
                    "Authorization": f"Bearer {self.ogce_config['auth_token']}",
                    "Content-Type": "application/json"
                }
            )
            
            if response.status_code == 200:
                logger.info("Safety alert linked to patient", alert_node_id=alert_node_id, patient_id=patient_id)
            else:
                logger.warning(
                    "Failed to link safety alert to patient",
                    status_code=response.status_code,
                    alert_node_id=alert_node_id,
                    patient_id=patient_id
                )
                
        except Exception as e:
            logger.error("Error linking alert to patient", error=str(e))
    
    async def _send_safety_notification(self, alert: SafetyAlert):
        """Send safety notification (webhook, email, etc.)"""
        
        try:
            if not self.settings.notification_webhook_url:
                logger.debug("No notification webhook configured")
                return
            
            # Prepare notification payload
            notification = {
                "type": "safety_alert",
                "alert_id": alert.alert_id,
                "severity": alert.violation.severity.value,
                "violation_type": alert.violation.violation_type.value,
                "description": alert.violation.description,
                "source_agent": alert.violation.source_agent,
                "patient_id": alert.violation.patient_id,
                "timestamp": alert.created_at.isoformat(),
                "remediation_suggestions": alert.violation.remediation_suggestions
            }
            
            # Send webhook notification
            response = await self.client.post(
                self.settings.notification_webhook_url,
                json=notification,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                logger.info("Safety notification sent", alert_id=alert.alert_id)
            else:
                logger.warning(
                    "Failed to send safety notification",
                    status_code=response.status_code,
                    alert_id=alert.alert_id
                )
                
        except Exception as e:
            logger.error("Error sending safety notification", error=str(e))
    
    async def log_guardian_decision(
        self,
        content: str,
        decision: str,
        confidence: float,
        rationale: str,
        source_agent: str,
        patient_id: Optional[str] = None,
        safety_scores: Optional[Dict[str, float]] = None
    ) -> Optional[str]:
        """Log a guardian decision (both safe and unsafe) to the graph"""
        
        try:
            decision_id = str(uuid.uuid4())
            
            # Prepare node properties
            properties = {
                "decision_id": decision_id,
                "decision": decision,
                "confidence": confidence,
                "rationale": rationale,
                "source_agent": source_agent,
                "patient_id": patient_id,
                "content_length": len(content),
                "content_hash": hash(content),
                "safety_scores": safety_scores or {},
                "timestamp": datetime.utcnow().isoformat(),
                "entity_type": "guardian_decision",
                "source": "guardian_agent"
            }
            
            # Create node request
            node_request = {
                "node_type": "guardian_decision",
                "properties": properties,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Send to OGCE Graph service
            response = await self.client.post(
                f"{self.ogce_config['url']}/v1/nodes",
                json=node_request,
                headers={
                    "Authorization": f"Bearer {self.ogce_config['auth_token']}",
                    "Content-Type": "application/json"
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                node_id = result.get("node_id")
                
                logger.info(
                    "Guardian decision logged",
                    decision_id=decision_id,
                    node_id=node_id,
                    decision=decision,
                    confidence=confidence
                )
                
                return node_id
            else:
                logger.error(
                    "Failed to create guardian decision node",
                    status_code=response.status_code,
                    response=response.text
                )
                return None
                
        except Exception as e:
            logger.error("Error logging guardian decision", error=str(e))
            return None
    
    async def get_patient_safety_alerts(self, patient_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get safety alerts for a specific patient"""
        
        try:
            # Query OGCE for patient's safety alerts
            response = await self.client.get(
                f"{self.ogce_config['url']}/v1/graph/patient/{patient_id}",
                headers={
                    "Authorization": f"Bearer {self.ogce_config['auth_token']}"
                }
            )
            
            if response.status_code == 200:
                graph_data = response.json()
                
                # Filter for safety alert nodes
                safety_alerts = []
                for node in graph_data.get("nodes", []):
                    if node.get("node_type") == "safety_alert":
                        safety_alerts.append(node)
                
                # Sort by timestamp (most recent first)
                safety_alerts.sort(
                    key=lambda x: x.get("properties", {}).get("timestamp", ""),
                    reverse=True
                )
                
                return safety_alerts[:limit]
            else:
                logger.error(
                    "Failed to get patient safety alerts",
                    status_code=response.status_code,
                    patient_id=patient_id
                )
                return []
                
        except Exception as e:
            logger.error("Error getting patient safety alerts", error=str(e), patient_id=patient_id)
            return []
