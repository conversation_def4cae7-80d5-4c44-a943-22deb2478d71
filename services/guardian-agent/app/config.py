"""
Guardian Agent Configuration

Settings and configuration management for the Guardian Agent service.
"""

import os
from typing import List, Dict, Any, Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Guardian Agent settings"""
    
    # Service configuration
    port: int = Field(default=8014, env="GUARDIAN_PORT")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # IBM watsonx.ai configuration
    watsonx_api_key: str = Field(..., env="WATSONX_API_KEY")
    watsonx_project_id: str = Field(..., env="WATSONX_PROJECT_ID")
    watsonx_url: str = Field(default="https://us-south.ml.cloud.ibm.com", env="WATSONX_URL")
    
    # Guardian model configuration
    guardian_model_id: str = Field(default="ibm/granite-guardian-3-8b", env="GUARDIAN_MODEL_ID")
    guardian_temperature: float = Field(default=0.0, env="GUARDIAN_TEMPERATURE")
    guardian_max_tokens: int = Field(default=512, env="GUARDIAN_MAX_TOKENS")
    
    # Redis configuration
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    redis_db: int = Field(default=0, env="REDIS_DB")
    
    # Safety configuration
    safety_threshold: float = Field(default=0.8, env="SAFETY_THRESHOLD")
    max_content_length: int = Field(default=50000, env="MAX_CONTENT_LENGTH")
    violation_retention_days: int = Field(default=30, env="VIOLATION_RETENTION_DAYS")
    
    # Policy configuration
    policy_config_path: str = Field(default="config/safety_policies.yaml", env="POLICY_CONFIG_PATH")
    enable_prescreening: bool = Field(default=True, env="ENABLE_PRESCREENING")
    enable_policy_engine: bool = Field(default=True, env="ENABLE_POLICY_ENGINE")
    
    # OGCE Graph service configuration
    ogce_graph_url: str = Field(default="http://localhost:8030", env="OGCE_GRAPH_URL")
    ogce_auth_token: str = Field(default="sk-symptomos-guardian-agent", env="OGCE_AUTH_TOKEN")
    
    # Notification configuration
    enable_notifications: bool = Field(default=True, env="ENABLE_NOTIFICATIONS")
    notification_webhook_url: Optional[str] = Field(default=None, env="NOTIFICATION_WEBHOOK_URL")
    
    # Performance configuration
    max_concurrent_checks: int = Field(default=10, env="MAX_CONCURRENT_CHECKS")
    check_timeout_seconds: int = Field(default=30, env="CHECK_TIMEOUT_SECONDS")
    
    # Safety categories configuration
    default_safety_categories: List[str] = Field(
        default=[
            "medical_accuracy",
            "phi_protection", 
            "harmful_content",
            "policy_compliance"
        ],
        env="DEFAULT_SAFETY_CATEGORIES"
    )
    
    # Risk level thresholds
    risk_thresholds: Dict[str, float] = Field(
        default={
            "low": 0.2,
            "medium": 0.5,
            "high": 0.8,
            "critical": 0.95
        }
    )
    
    # Violation severity mapping
    violation_severity_map: Dict[str, str] = Field(
        default={
            "prescreening_failure": "high",
            "medical_misinformation": "critical",
            "phi_leakage": "critical",
            "harmful_advice": "high",
            "policy_violation": "medium",
            "inappropriate_content": "medium",
            "system_error": "critical"
        }
    )
    
    class Config:
        env_file = ".env"
        case_sensitive = False
    
    def get_watsonx_config(self) -> Dict[str, Any]:
        """Get watsonx.ai configuration"""
        return {
            "api_key": self.watsonx_api_key,
            "project_id": self.watsonx_project_id,
            "url": self.watsonx_url,
            "model_id": self.guardian_model_id,
            "temperature": self.guardian_temperature,
            "max_tokens": self.guardian_max_tokens
        }
    
    def get_redis_config(self) -> Dict[str, Any]:
        """Get Redis configuration"""
        return {
            "url": self.redis_url,
            "db": self.redis_db
        }
    
    def get_safety_config(self) -> Dict[str, Any]:
        """Get safety checking configuration"""
        return {
            "threshold": self.safety_threshold,
            "max_content_length": self.max_content_length,
            "categories": self.default_safety_categories,
            "risk_thresholds": self.risk_thresholds,
            "violation_severity_map": self.violation_severity_map
        }
    
    def get_ogce_config(self) -> Dict[str, Any]:
        """Get OGCE Graph service configuration"""
        return {
            "url": self.ogce_graph_url,
            "auth_token": self.ogce_auth_token
        }
    
    def is_high_risk_violation(self, violation_type: str) -> bool:
        """Check if violation type is high risk"""
        severity = self.violation_severity_map.get(violation_type, "medium")
        return severity in ["high", "critical"]
    
    def get_risk_level_from_score(self, score: float) -> str:
        """Get risk level from numerical score"""
        if score >= self.risk_thresholds["critical"]:
            return "critical"
        elif score >= self.risk_thresholds["high"]:
            return "high"
        elif score >= self.risk_thresholds["medium"]:
            return "medium"
        else:
            return "low"
