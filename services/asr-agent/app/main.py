"""
ASR Agent - Speech-to-Text using IBM Granite Speech

Handles audio transcription with medical domain optimization using
ibm/granite-speech-3-3-8b model with fallback to Whisper-tiny for reliability.

Author: SymptomOS Team
Version: 0.1
"""

import asyncio
import base64
import io
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
import structlog
import redis.asyncio as redis
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, UploadFile, File, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import numpy as np
import librosa

from .config import Settings
from .granite_client import GraniteASRClient
from .whisper_fallback import WhisperFallback
from .audio_processor import AudioProcessor
from .schemas import TranscriptionRequest, TranscriptionResponse, AudioMetadata

logger = structlog.get_logger()

# Global state
app_state = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    settings = Settings()
    app_state["settings"] = settings
    app_state["redis"] = redis.from_url(settings.redis_url)
    app_state["granite_client"] = GraniteASRClient(settings)
    app_state["whisper_fallback"] = WhisperFallback(settings)
    app_state["audio_processor"] = AudioProcessor(settings)
    
    # Initialize clients
    await app_state["granite_client"].initialize()
    await app_state["whisper_fallback"].initialize()
    
    logger.info("ASR agent started", model="granite-speech-3-3-8b", port=settings.port)
    
    yield
    
    # Shutdown
    await app_state["redis"].close()
    await app_state["granite_client"].cleanup()
    logger.info("ASR agent stopped")

app = FastAPI(
    title="SymptomOS ASR Agent",
    description="Speech-to-text transcription using IBM Granite Speech models",
    version="0.1.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.post("/v1/transcribe", response_model=TranscriptionResponse)
async def transcribe_audio(
    request: TranscriptionRequest
) -> TranscriptionResponse:
    """
    Transcribe audio using Granite Speech model with medical domain optimization.
    
    Supports both base64 encoded audio and streaming chunks.
    Falls back to Whisper-tiny on Granite model failure.
    """
    settings = app_state["settings"]
    granite_client = app_state["granite_client"]
    whisper_fallback = app_state["whisper_fallback"]
    audio_processor = app_state["audio_processor"]
    
    start_time = time.time()
    
    try:
        # Step 1: Process and validate audio
        audio_data, metadata = await audio_processor.process_audio(
            request.audio_data,
            request.format,
            request.sample_rate
        )
        
        # Step 2: Apply medical domain preprocessing
        if request.domain == "medical":
            audio_data = await audio_processor.apply_medical_preprocessing(audio_data)
        
        # Step 3: Chunk audio for optimal processing
        chunks = await audio_processor.chunk_audio(
            audio_data,
            chunk_duration=settings.chunk_window_seconds,
            overlap=settings.chunk_overlap_seconds
        )
        
        # Step 4: Transcribe using Granite Speech
        transcription_results = []
        granite_success = True
        
        for i, chunk in enumerate(chunks):
            try:
                result = await granite_client.transcribe_chunk(
                    chunk,
                    metadata,
                    chunk_index=i,
                    medical_context=request.medical_context
                )
                transcription_results.append(result)
                
            except Exception as e:
                logger.warning(
                    "Granite transcription failed for chunk",
                    chunk_index=i,
                    error=str(e)
                )
                granite_success = False
                break
        
        # Step 5: Fallback to Whisper if Granite fails
        if not granite_success or not transcription_results:
            logger.info("Falling back to Whisper-tiny")
            
            fallback_result = await whisper_fallback.transcribe(
                audio_data,
                metadata
            )
            
            return TranscriptionResponse(
                text=fallback_result["text"],
                confidence=fallback_result["confidence"],
                language=fallback_result.get("language", "en"),
                processing_time=time.time() - start_time,
                model_used="whisper-tiny",
                chunks=fallback_result.get("chunks", []),
                metadata={
                    "fallback_reason": "granite_failure",
                    "audio_duration": metadata.duration,
                    "sample_rate": metadata.sample_rate
                }
            )
        
        # Step 6: Combine chunk results
        combined_text = await granite_client.combine_chunk_results(transcription_results)
        
        # Step 7: Apply medical post-processing
        if request.domain == "medical":
            combined_text = await audio_processor.apply_medical_postprocessing(
                combined_text,
                request.medical_context
            )
        
        # Calculate overall confidence
        overall_confidence = np.mean([r["confidence"] for r in transcription_results])
        
        processing_time = time.time() - start_time
        
        # Log metrics
        await _log_transcription_metrics(
            model="granite-speech-3-3-8b",
            duration=metadata.duration,
            processing_time=processing_time,
            confidence=overall_confidence,
            chunk_count=len(chunks)
        )
        
        return TranscriptionResponse(
            text=combined_text,
            confidence=float(overall_confidence),
            language="en",  # Granite Speech primarily supports English
            processing_time=processing_time,
            model_used="granite-speech-3-3-8b",
            chunks=[{
                "text": r["text"],
                "start_time": r["start_time"],
                "end_time": r["end_time"],
                "confidence": r["confidence"]
            } for r in transcription_results],
            metadata={
                "audio_duration": metadata.duration,
                "sample_rate": metadata.sample_rate,
                "chunk_count": len(chunks),
                "medical_domain": request.domain == "medical"
            }
        )
        
    except Exception as e:
        logger.error("Transcription failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Transcription failed: {str(e)}"
        )


@app.post("/v1/transcribe/file")
async def transcribe_file(
    file: UploadFile = File(...),
    domain: str = "general",
    medical_context: Optional[str] = None
) -> TranscriptionResponse:
    """
    Transcribe uploaded audio file.
    
    Supports common audio formats: wav, mp3, m4a, flac, ogg
    """
    try:
        # Read file content
        audio_content = await file.read()
        
        # Convert to base64 for processing
        audio_b64 = base64.b64encode(audio_content).decode('utf-8')
        
        # Create transcription request
        request = TranscriptionRequest(
            audio_data=audio_b64,
            format=file.filename.split('.')[-1].lower() if file.filename else "wav",
            domain=domain,
            medical_context=medical_context
        )
        
        return await transcribe_audio(request)
        
    except Exception as e:
        logger.error("File transcription failed", filename=file.filename, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"File transcription failed: {str(e)}"
        )


@app.post("/v1/transcribe/stream")
async def transcribe_stream(
    chunk_data: str,
    chunk_index: int,
    is_final: bool = False,
    session_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Handle streaming transcription for real-time audio.
    
    Maintains session state for continuous transcription.
    """
    settings = app_state["settings"]
    granite_client = app_state["granite_client"]
    
    try:
        # Process streaming chunk
        result = await granite_client.transcribe_streaming_chunk(
            chunk_data,
            chunk_index,
            is_final,
            session_id
        )
        
        return {
            "partial_text": result.get("partial_text", ""),
            "is_final": is_final,
            "confidence": result.get("confidence", 0.0),
            "session_id": result.get("session_id"),
            "chunk_index": chunk_index
        }
        
    except Exception as e:
        logger.error("Streaming transcription failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Streaming transcription failed: {str(e)}"
        )


@app.get("/v1/models")
async def list_models() -> Dict[str, Any]:
    """Get information about available ASR models"""
    return {
        "primary": {
            "model_id": "ibm/granite-speech-3-3-8b",
            "description": "IBM Granite Speech model optimized for medical domain",
            "languages": ["en"],
            "sample_rates": [16000, 22050, 44100],
            "max_duration": 300  # 5 minutes
        },
        "fallback": {
            "model_id": "whisper-tiny",
            "description": "OpenAI Whisper Tiny for local fallback",
            "languages": ["en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh"],
            "sample_rates": [16000],
            "max_duration": 600  # 10 minutes
        }
    }


@app.get("/v1/health")
async def health_check() -> Dict[str, Any]:
    """Health check with model status"""
    granite_client = app_state["granite_client"]
    whisper_fallback = app_state["whisper_fallback"]
    
    try:
        # Check Granite model availability
        granite_healthy = await granite_client.health_check()
        
        # Check Whisper fallback
        whisper_healthy = await whisper_fallback.health_check()
        
        # Check Redis connection
        redis_client = app_state["redis"]
        await redis_client.ping()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "models": {
                "granite_speech": "healthy" if granite_healthy else "unhealthy",
                "whisper_fallback": "healthy" if whisper_healthy else "unhealthy"
            },
            "redis": "healthy",
            "version": "0.1.0"
        }
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.get("/healthz")
async def kubernetes_health() -> Dict[str, str]:
    """Kubernetes-style health check"""
    try:
        granite_client = app_state["granite_client"]
        if not await granite_client.health_check():
            raise Exception("Granite client unhealthy")
        
        return {"status": "ok"}
    except Exception:
        raise HTTPException(status_code=503, detail="unhealthy")


async def _log_transcription_metrics(
    model: str,
    duration: float,
    processing_time: float,
    confidence: float,
    chunk_count: int
):
    """Log transcription metrics to Redis"""
    try:
        redis_client = app_state["redis"]
        
        metrics = {
            "model": model,
            "audio_duration": duration,
            "processing_time": processing_time,
            "confidence": confidence,
            "chunk_count": chunk_count,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await redis_client.lpush("metrics:asr:transcriptions", str(metrics))
        await redis_client.ltrim("metrics:asr:transcriptions", 0, 999)  # Keep last 1000
        
    except Exception as e:
        logger.warning("Failed to log metrics", error=str(e))


if __name__ == "__main__":
    import uvicorn
    settings = Settings()
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.port,
        reload=settings.debug
    )
