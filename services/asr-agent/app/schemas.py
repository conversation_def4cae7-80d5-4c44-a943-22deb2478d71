"""
Pydantic schemas for ASR Agent

Defines data models for audio transcription requests, responses,
and metadata structures.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field, validator


class TranscriptionRequest(BaseModel):
    """Request for audio transcription"""
    audio_data: str = Field(..., description="Base64 encoded audio data")
    format: str = Field(default="wav", description="Audio format (wav, mp3, m4a, etc.)")
    sample_rate: Optional[int] = Field(default=16000, description="Audio sample rate in Hz")
    domain: str = Field(default="general", description="Domain for optimization (general, medical)")
    medical_context: Optional[str] = Field(None, description="Medical context for better transcription")
    language: str = Field(default="en", description="Expected language code")
    
    @validator('format')
    def validate_format(cls, v):
        """Validate audio format"""
        allowed_formats = {'wav', 'mp3', 'm4a', 'flac', 'ogg', 'webm'}
        if v.lower() not in allowed_formats:
            raise ValueError(f"Format must be one of: {allowed_formats}")
        return v.lower()
    
    @validator('sample_rate')
    def validate_sample_rate(cls, v):
        """Validate sample rate"""
        if v and v not in [8000, 16000, 22050, 44100, 48000]:
            raise ValueError("Sample rate must be 8000, 16000, 22050, 44100, or 48000 Hz")
        return v
    
    @validator('domain')
    def validate_domain(cls, v):
        """Validate domain"""
        allowed_domains = {'general', 'medical', 'conversational'}
        if v not in allowed_domains:
            raise ValueError(f"Domain must be one of: {allowed_domains}")
        return v


class TranscriptionChunk(BaseModel):
    """Individual transcription chunk result"""
    text: str = Field(..., description="Transcribed text for this chunk")
    start_time: float = Field(..., description="Start time in seconds")
    end_time: float = Field(..., description="End time in seconds")
    confidence: float = Field(..., description="Confidence score (0-1)")
    
    @validator('confidence')
    def validate_confidence(cls, v):
        """Validate confidence score"""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Confidence must be between 0 and 1")
        return v


class TranscriptionResponse(BaseModel):
    """Response from audio transcription"""
    text: str = Field(..., description="Complete transcribed text")
    confidence: float = Field(..., description="Overall confidence score (0-1)")
    language: str = Field(..., description="Detected/specified language")
    processing_time: float = Field(..., description="Processing time in seconds")
    model_used: str = Field(..., description="Model used for transcription")
    chunks: List[TranscriptionChunk] = Field(default_factory=list, description="Individual chunk results")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    @validator('confidence')
    def validate_confidence(cls, v):
        """Validate confidence score"""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Confidence must be between 0 and 1")
        return v


class AudioMetadata(BaseModel):
    """Audio file metadata"""
    duration: float = Field(..., description="Audio duration in seconds")
    sample_rate: int = Field(..., description="Sample rate in Hz")
    channels: int = Field(default=1, description="Number of audio channels")
    bit_depth: Optional[int] = Field(None, description="Bit depth")
    format: str = Field(..., description="Audio format")
    size_bytes: int = Field(..., description="File size in bytes")
    
    @validator('duration')
    def validate_duration(cls, v):
        """Validate duration"""
        if v <= 0:
            raise ValueError("Duration must be positive")
        if v > 600:  # 10 minutes max
            raise ValueError("Audio duration exceeds maximum limit (10 minutes)")
        return v


class StreamingSession(BaseModel):
    """Streaming transcription session"""
    session_id: str = Field(..., description="Unique session identifier")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_activity: datetime = Field(default_factory=datetime.utcnow)
    total_chunks: int = Field(default=0, description="Total chunks processed")
    accumulated_text: str = Field(default="", description="Accumulated transcription")
    is_active: bool = Field(default=True, description="Whether session is active")


class MedicalTerms(BaseModel):
    """Medical terminology for enhanced transcription"""
    symptoms: List[str] = Field(default_factory=list, description="Common symptom terms")
    medications: List[str] = Field(default_factory=list, description="Medication names")
    procedures: List[str] = Field(default_factory=list, description="Medical procedures")
    anatomy: List[str] = Field(default_factory=list, description="Anatomical terms")
    
    @classmethod
    def get_default_terms(cls) -> "MedicalTerms":
        """Get default medical terminology"""
        return cls(
            symptoms=[
                "nausea", "headache", "fever", "fatigue", "dizziness", "pain",
                "shortness of breath", "chest pain", "abdominal pain", "vomiting",
                "diarrhea", "constipation", "insomnia", "anxiety", "depression"
            ],
            medications=[
                "aspirin", "ibuprofen", "acetaminophen", "tylenol", "advil",
                "metformin", "lisinopril", "atorvastatin", "omeprazole",
                "levothyroxine", "amlodipine", "metoprolol", "losartan"
            ],
            procedures=[
                "blood pressure", "temperature", "pulse", "weight", "height",
                "blood test", "urine test", "x-ray", "mri", "ct scan",
                "ultrasound", "ecg", "ekg", "colonoscopy", "endoscopy"
            ],
            anatomy=[
                "heart", "lungs", "liver", "kidney", "brain", "stomach",
                "intestine", "spine", "joints", "muscles", "bones",
                "blood vessels", "arteries", "veins", "nerves"
            ]
        )


class ASRMetrics(BaseModel):
    """ASR performance metrics"""
    total_requests: int = Field(default=0, description="Total transcription requests")
    successful_requests: int = Field(default=0, description="Successful transcriptions")
    failed_requests: int = Field(default=0, description="Failed transcriptions")
    granite_usage: int = Field(default=0, description="Granite model usage count")
    whisper_fallback_usage: int = Field(default=0, description="Whisper fallback usage count")
    average_processing_time: float = Field(default=0.0, description="Average processing time")
    average_confidence: float = Field(default=0.0, description="Average confidence score")
    total_audio_duration: float = Field(default=0.0, description="Total audio processed (seconds)")
    
    def update_success(self, processing_time: float, confidence: float, audio_duration: float, model: str):
        """Update metrics for successful transcription"""
        self.total_requests += 1
        self.successful_requests += 1
        
        if model == "granite-speech-3-3-8b":
            self.granite_usage += 1
        elif model == "whisper-tiny":
            self.whisper_fallback_usage += 1
        
        # Update averages
        total_successful = self.successful_requests
        self.average_processing_time = (
            (self.average_processing_time * (total_successful - 1) + processing_time) / total_successful
        )
        self.average_confidence = (
            (self.average_confidence * (total_successful - 1) + confidence) / total_successful
        )
        self.total_audio_duration += audio_duration
    
    def update_failure(self):
        """Update metrics for failed transcription"""
        self.total_requests += 1
        self.failed_requests += 1
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests
    
    @property
    def granite_usage_rate(self) -> float:
        """Calculate Granite model usage rate"""
        if self.successful_requests == 0:
            return 0.0
        return self.granite_usage / self.successful_requests


class AudioProcessingConfig(BaseModel):
    """Configuration for audio processing"""
    target_sample_rate: int = Field(default=16000, description="Target sample rate for processing")
    chunk_duration: float = Field(default=15.0, description="Chunk duration in seconds")
    chunk_overlap: float = Field(default=1.0, description="Overlap between chunks in seconds")
    noise_reduction: bool = Field(default=True, description="Apply noise reduction")
    volume_normalization: bool = Field(default=True, description="Apply volume normalization")
    medical_enhancement: bool = Field(default=True, description="Apply medical domain enhancements")
    
    @validator('chunk_duration')
    def validate_chunk_duration(cls, v):
        """Validate chunk duration"""
        if not 5.0 <= v <= 30.0:
            raise ValueError("Chunk duration must be between 5 and 30 seconds")
        return v
    
    @validator('chunk_overlap')
    def validate_chunk_overlap(cls, v):
        """Validate chunk overlap"""
        if not 0.0 <= v <= 5.0:
            raise ValueError("Chunk overlap must be between 0 and 5 seconds")
        return v
