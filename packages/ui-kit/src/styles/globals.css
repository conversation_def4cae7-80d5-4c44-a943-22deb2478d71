@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }
  
  html {
    -webkit-text-size-adjust: 100%;
  }
  
  body {
    margin: 0;
    line-height: inherit;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-colors duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .card {
    @apply bg-white rounded-lg border border-gray-200;
  }
  
  .ring-node {
    @apply relative rounded-full flex items-center justify-center transition-all duration-200;
  }
}