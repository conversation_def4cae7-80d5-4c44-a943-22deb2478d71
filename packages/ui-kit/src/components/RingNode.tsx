import React from 'react';
import { cn } from '../utils/cn';
import { Badge } from './Badge';

export interface RingNodeData {
  id: string;
  type: 'symptom' | 'medication' | 'event' | 'test' | 'diagnosis';
  label: string;
  timestamp: Date;
  severity?: number; // 1-10 scale
  confidence?: number; // 0-1 scale
  metadata?: Record<string, any>;
}

export interface RingNodeProps extends React.HTMLAttributes<HTMLDivElement> {
  data: RingNodeData;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  selected?: boolean;
  pulse?: boolean;
  interactive?: boolean;
  showLabel?: boolean;
  showTimestamp?: boolean;
}

const nodeTypeColors = {
  symptom: {
    bg: 'bg-red-500',
    border: 'border-red-600',
    text: 'text-red-700',
    light: 'bg-red-50'
  },
  medication: {
    bg: 'bg-blue-500', 
    border: 'border-blue-600',
    text: 'text-blue-700',
    light: 'bg-blue-50'
  },
  event: {
    bg: 'bg-green-500',
    border: 'border-green-600', 
    text: 'text-green-700',
    light: 'bg-green-50'
  },
  test: {
    bg: 'bg-purple-500',
    border: 'border-purple-600',
    text: 'text-purple-700',
    light: 'bg-purple-50'
  },
  diagnosis: {
    bg: 'bg-orange-500',
    border: 'border-orange-600',
    text: 'text-orange-700', 
    light: 'bg-orange-50'
  }
};

const nodeSizes = {
  sm: {
    ring: 'w-8 h-8',
    core: 'w-4 h-4',
    text: 'text-xs',
    gap: 'gap-1'
  },
  md: {
    ring: 'w-12 h-12', 
    core: 'w-6 h-6',
    text: 'text-sm',
    gap: 'gap-2'
  },
  lg: {
    ring: 'w-16 h-16',
    core: 'w-8 h-8', 
    text: 'text-base',
    gap: 'gap-3'
  },
  xl: {
    ring: 'w-20 h-20',
    core: 'w-10 h-10',
    text: 'text-lg', 
    gap: 'gap-4'
  }
};

const getSeverityOpacity = (severity?: number): string => {
  if (!severity) return 'opacity-50';
  if (severity <= 3) return 'opacity-40';
  if (severity <= 6) return 'opacity-70';
  return 'opacity-100';
};

const getConfidenceRing = (confidence?: number): string => {
  if (!confidence) return 'ring-2';
  if (confidence < 0.5) return 'ring-1 ring-dashed';
  if (confidence < 0.8) return 'ring-2';
  return 'ring-4';
};

export const RingNode: React.FC<RingNodeProps> = ({
  data,
  size = 'md',
  selected = false,
  pulse = false,
  interactive = true,
  showLabel = true,
  showTimestamp = false,
  className,
  onClick,
  ...props
}) => {
  const colors = nodeTypeColors[data.type];
  const sizes = nodeSizes[size];
  
  const formatTimestamp = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  return (
    <div
      className={cn(
        'flex flex-col items-center',
        sizes.gap,
        className
      )}
      {...props}
    >
      {/* Ring Node */}
      <div
        className={cn(
          'relative rounded-full flex items-center justify-center transition-all duration-200',
          'border-2',
          sizes.ring,
          colors.light,
          colors.border,
          selected && 'ring-4 ring-offset-2 ring-blue-400',
          interactive && 'cursor-pointer hover:scale-110 hover:shadow-lg',
          pulse && 'animate-pulse',
          getSeverityOpacity(data.severity),
          getConfidenceRing(data.confidence)
        )}
        onClick={onClick}
      >
        {/* Core */}
        <div
          className={cn(
            'rounded-full',
            sizes.core,
            colors.bg,
            data.severity && data.severity >= 8 && 'animate-ping absolute',
          )}
        />
        <div
          className={cn(
            'rounded-full relative',
            sizes.core, 
            colors.bg
          )}
        />
        
        {/* Confidence indicator */}
        {data.confidence && data.confidence < 0.5 && (
          <div className="absolute -top-1 -right-1">
            <div className="w-3 h-3 bg-yellow-400 rounded-full border border-yellow-500" />
          </div>
        )}
      </div>

      {/* Label */}
      {showLabel && (
        <div className="text-center max-w-20">
          <div className={cn('font-medium truncate', sizes.text, colors.text)}>
            {data.label}
          </div>
          {showTimestamp && (
            <div className="text-xs text-gray-500 truncate">
              {formatTimestamp(data.timestamp)}
            </div>
          )}
        </div>
      )}

      {/* Severity badge */}
      {data.severity && data.severity >= 7 && (
        <Badge variant="danger" size="sm" className="absolute -top-2 -right-2">
          {data.severity}
        </Badge>
      )}
    </div>
  );
};

// Compound component for multiple rings
export interface RingClusterProps extends React.HTMLAttributes<HTMLDivElement> {
  nodes: RingNodeData[];
  selectedId?: string;
  onNodeClick?: (node: RingNodeData) => void;
  layout?: 'horizontal' | 'vertical' | 'circular';
  spacing?: 'tight' | 'normal' | 'loose';
}

const clusterLayouts = {
  horizontal: 'flex-row',
  vertical: 'flex-col', 
  circular: 'grid grid-cols-3'
};

const clusterSpacing = {
  tight: 'gap-2',
  normal: 'gap-4',
  loose: 'gap-6'
};

export const RingCluster: React.FC<RingClusterProps> = ({
  nodes,
  selectedId,
  onNodeClick,
  layout = 'horizontal',
  spacing = 'normal',
  className,
  ...props
}) => {
  return (
    <div
      className={cn(
        'flex items-center justify-center',
        clusterLayouts[layout],
        clusterSpacing[spacing],
        layout === 'circular' && 'max-w-48',
        className
      )}
      {...props}
    >
      {nodes.map((node) => (
        <RingNode
          key={node.id}
          data={node}
          selected={selectedId === node.id}
          onClick={() => onNodeClick?.(node)}
          size={layout === 'circular' ? 'sm' : 'md'}
        />
      ))}
    </div>
  );
};