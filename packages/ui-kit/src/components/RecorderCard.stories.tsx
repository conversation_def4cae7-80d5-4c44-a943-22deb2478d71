import type { Meta, StoryObj } from '@storybook/react';
import { RecorderCard } from './RecorderCard';
import { useState } from 'react';

const meta: Meta<typeof RecorderCard> = {
  title: 'Components/RecorderCard',
  component: RecorderCard,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A comprehensive voice recording component for medical symptom capture with Granite ASR integration.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    disabled: {
      control: 'boolean',
    },
    maxDuration: {
      control: 'number',
    },
  },
} satisfies Meta<typeof RecorderCard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onRecordingComplete: (blob, duration) => {
      console.log('Recording completed:', { size: blob.size, duration });
    },
    onTranscriptionReceived: (text, confidence) => {
      console.log('Transcription:', { text, confidence });
    },
    onSymptomLogged: (success, message) => {
      console.log('Symptom logging:', { success, message });
    },
  },
};

export const Disabled: Story = {
  args: {
    ...Default.args,
    disabled: true,
  },
};

export const ShortDuration: Story = {
  args: {
    ...Default.args,
    maxDuration: 30, // 30 seconds
  },
};

export const WithMockGateway: Story = {
  args: {
    ...Default.args,
    graniteGatewayUrl: '/mock-api',
  },
};

export const Interactive: Story = {
  render: () => {
    const [recordings, setRecordings] = useState<Array<{
      id: number;
      transcription: string;
      confidence: number;
      success: boolean;
      message: string;
    }>>([]);

    const handleRecordingComplete = (blob: Blob, duration: number) => {
      console.log(`Recording completed: ${blob.size} bytes, ${duration}s`);
    };

    const handleTranscriptionReceived = (transcription: string, confidence: number) => {
      console.log(`Transcription: "${transcription}" (${Math.round(confidence * 100)}% confidence)`);
    };

    const handleSymptomLogged = (success: boolean, message: string) => {
      const newRecording = {
        id: Date.now(),
        transcription: 'Sample transcription: "I have a severe headache that started this morning"',
        confidence: 0.89,
        success,
        message,
      };
      setRecordings(prev => [newRecording, ...prev].slice(0, 5));
    };

    return (
      <div className="space-y-6">
        <RecorderCard
          onRecordingComplete={handleRecordingComplete}
          onTranscriptionReceived={handleTranscriptionReceived}
          onSymptomLogged={handleSymptomLogged}
        />
        
        {recordings.length > 0 && (
          <div className="w-full max-w-md mx-auto">
            <h3 className="text-lg font-semibold mb-3">Recent Recordings</h3>
            <div className="space-y-2">
              {recordings.map((recording) => (
                <div
                  key={recording.id}
                  className="p-3 bg-gray-50 rounded-lg text-sm"
                >
                  <div className="flex justify-between items-start mb-1">
                    <span className={recording.success ? 'text-green-600' : 'text-red-600'}>
                      {recording.success ? '✅ Success' : '❌ Failed'}
                    </span>
                    <span className="text-gray-500">
                      {Math.round(recording.confidence * 100)}%
                    </span>
                  </div>
                  <p className="text-gray-700 mb-1">{recording.transcription}</p>
                  <p className="text-xs text-gray-500">{recording.message}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  },
};

export const ErrorState: Story = {
  render: () => {
    const [showError, setShowError] = useState(false);

    return (
      <div className="space-y-4">
        <RecorderCard
          onRecordingComplete={() => {}}
          onTranscriptionReceived={() => {}}
          onSymptomLogged={(success) => {
            if (!success) {
              setShowError(true);
            }
          }}
        />
        
        <button
          onClick={() => setShowError(!showError)}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          {showError ? 'Hide' : 'Simulate'} Error
        </button>
        
        {showError && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <h4 className="font-medium text-red-800">Simulated Error States:</h4>
            <ul className="mt-2 text-sm text-red-700">
              <li>• Microphone access denied</li>
              <li>• Network connection failed</li>
              <li>• Audio quality too low</li>
              <li>• No speech detected</li>
              <li>• Server processing error</li>
            </ul>
          </div>
        )}
      </div>
    );
  },
};

export const ProcessingFlow: Story = {
  render: () => {
    const [currentStep, setCurrentStep] = useState(0);
    
    const steps = [
      { name: 'Record', description: 'Capture voice input' },
      { name: 'Transcribe', description: 'Convert speech to text' },
      { name: 'Parse', description: 'Extract medical entities' },
      { name: 'Complete', description: 'Symptom logged' },
    ];

    return (
      <div className="space-y-6">
        <RecorderCard
          onRecordingComplete={() => setCurrentStep(1)}
          onTranscriptionReceived={() => setCurrentStep(2)}
          onSymptomLogged={() => setCurrentStep(3)}
        />
        
        <div className="w-full max-w-md mx-auto">
          <h3 className="text-lg font-semibold mb-4">Processing Flow</h3>
          <div className="space-y-3">
            {steps.map((step, index) => (
              <div
                key={step.name}
                className={`flex items-center space-x-3 p-3 rounded-lg ${
                  index <= currentStep
                    ? 'bg-blue-50 border border-blue-200'
                    : 'bg-gray-50 border border-gray-200'
                }`}
              >
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    index < currentStep
                      ? 'bg-green-500 text-white'
                      : index === currentStep
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-300 text-gray-600'
                  }`}
                >
                  {index < currentStep ? '✓' : index + 1}
                </div>
                <div>
                  <div className="font-medium">{step.name}</div>
                  <div className="text-sm text-gray-600">{step.description}</div>
                </div>
              </div>
            ))}
          </div>
          
          <button
            onClick={() => setCurrentStep(0)}
            className="mt-4 w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Reset Flow
          </button>
        </div>
      </div>
    );
  },
};

export const AccessibilityFeatures: Story = {
  render: () => (
    <div className="space-y-6">
      <RecorderCard
        onRecordingComplete={() => {}}
        onTranscriptionReceived={() => {}}
        onSymptomLogged={() => {}}
      />
      
      <div className="w-full max-w-md mx-auto p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">Accessibility Features</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Keyboard navigation support</li>
          <li>• Screen reader announcements</li>
          <li>• High contrast visual indicators</li>
          <li>• Voice feedback for recording states</li>
          <li>• Large touch targets for mobile</li>
          <li>• Clear error messages</li>
        </ul>
      </div>
    </div>
  ),
};