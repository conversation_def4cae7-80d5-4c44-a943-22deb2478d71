import React from 'react';
import { cn } from '../utils/cn';

export interface TimelineRingProps {
  severity?: number; // 1-10 scale
  type?: 'symptom' | 'medication' | 'note' | 'alert';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
  className?: string;
}

const getSeverityColor = (severity?: number) => {
  if (!severity) return 'bg-gray-400';
  if (severity <= 3) return 'bg-green-500';
  if (severity <= 6) return 'bg-yellow-500';
  if (severity <= 8) return 'bg-orange-500';
  return 'bg-red-500';
};

const getTypeColor = (type: TimelineRingProps['type']) => {
  switch (type) {
    case 'symptom':
      return 'bg-red-500';
    case 'medication':
      return 'bg-blue-500';
    case 'note':
      return 'bg-purple-500';
    case 'alert':
      return 'bg-orange-500';
    default:
      return 'bg-gray-500';
  }
};

const ringSize = {
  sm: 'w-6 h-6',
  md: 'w-8 h-8',
  lg: 'w-12 h-12',
};

const iconSize = {
  sm: 'w-3 h-3',
  md: 'w-4 h-4',
  lg: 'w-6 h-6',
};

export const TimelineRing: React.FC<TimelineRingProps> = ({
  severity,
  type = 'symptom',
  size = 'md',
  icon,
  className,
}) => {
  const colorClass = severity ? getSeverityColor(severity) : getTypeColor(type);

  return (
    <div
      className={cn(
        'rounded-full flex items-center justify-center text-white shadow-sm',
        ringSize[size],
        colorClass,
        className
      )}
    >
      {icon && (
        <span className={iconSize[size]}>
          {icon}
        </span>
      )}
      {!icon && severity && (
        <span className={cn(
          'font-bold text-white',
          size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'
        )}>
          {severity}
        </span>
      )}
    </div>
  );
};

// Timeline component for displaying a series of events
export interface TimelineEvent {
  id: string;
  timestamp: Date;
  title: string;
  description?: string;
  severity?: number;
  type?: TimelineRingProps['type'];
  icon?: React.ReactNode;
}

export interface TimelineProps {
  events: TimelineEvent[];
  className?: string;
}

export const Timeline: React.FC<TimelineProps> = ({ events, className }) => {
  return (
    <div className={cn('space-y-4', className)}>
      {events.map((event, index) => (
        <div key={event.id} className="flex items-start space-x-3">
          <div className="flex flex-col items-center">
            <TimelineRing
              severity={event.severity}
              type={event.type}
              icon={event.icon}
            />
            {index < events.length - 1 && (
              <div className="w-0.5 h-8 bg-gray-200 mt-2" />
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-900">{event.title}</h4>
              <time className="text-xs text-gray-500">
                {event.timestamp.toLocaleTimeString()}
              </time>
            </div>
            {event.description && (
              <p className="text-sm text-gray-600 mt-1">{event.description}</p>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

// Attach Timeline to TimelineRing
TimelineRing.Timeline = Timeline;
