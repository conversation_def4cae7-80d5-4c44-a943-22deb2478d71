import React, { useState, useCallback, useRef } from 'react';
import { Card } from './Card';
import { Button } from './Button';
import { Badge } from './Badge';
import { cn } from '../utils/cn';
import { Mic, MicOff, Square, Play, Pause, Trash2, Send } from 'lucide-react';

export interface RecorderCardProps extends React.HTMLAttributes<HTMLDivElement> {
  onRecordingComplete?: (audioBlob: Blob, duration: number) => void;
  onTranscriptionReceived?: (transcription: string, confidence: number) => void;
  onSymptomLogged?: (success: boolean, message: string) => void;
  graniteGatewayUrl?: string;
  disabled?: boolean;
  maxDuration?: number; // in seconds
}

export type RecordingState = 'idle' | 'recording' | 'paused' | 'completed' | 'processing' | 'transcribing' | 'parsing';

interface AudioAnalysis {
  volume: number;
  frequency: number;
}

export const RecorderCard: React.FC<RecorderCardProps> = ({
  onRecordingComplete,
  onTranscriptionReceived,
  onSymptomLogged,
  graniteGatewayUrl = '/api/granite-gateway',
  disabled = false,
  maxDuration = 300, // 5 minutes default
  className,
  ...props
}) => {
  const [state, setState] = useState<RecordingState>('idle');
  const [duration, setDuration] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [transcription, setTranscription] = useState<string>('');
  const [confidence, setConfidence] = useState<number>(0);
  const [error, setError] = useState<string>('');
  const [audioAnalysis, setAudioAnalysis] = useState<AudioAnalysis>({ volume: 0, frequency: 0 });

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const animationRef = useRef<number | null>(null);

  const startRecording = useCallback(async () => {
    try {
      setError('');
      
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 16000,
        } 
      });
      
      // Set up audio analysis
      audioContextRef.current = new AudioContext();
      analyserRef.current = audioContextRef.current.createAnalyser();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      source.connect(analyserRef.current);
      
      analyserRef.current.fftSize = 256;
      const bufferLength = analyserRef.current.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);

      // Start audio analysis animation
      const updateAnalysis = () => {
        if (analyserRef.current && state === 'recording') {
          analyserRef.current.getByteFrequencyData(dataArray);
          
          const volume = dataArray.reduce((a, b) => a + b) / bufferLength;
          const frequency = dataArray.findIndex(val => val > 50);
          
          setAudioAnalysis({ volume: volume / 255, frequency });
          animationRef.current = requestAnimationFrame(updateAnalysis);
        }
      };

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus',
      });
      
      const chunks: Blob[] = [];
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/webm' });
        setAudioBlob(blob);
        setState('completed');
        onRecordingComplete?.(blob, duration);
        
        // Clean up
        stream.getTracks().forEach(track => track.stop());
        if (audioContextRef.current) {
          audioContextRef.current.close();
        }
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current);
        }
      };
      
      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();
      setState('recording');
      
      // Start timer
      setDuration(0);
      intervalRef.current = setInterval(() => {
        setDuration(prev => {
          const newDuration = prev + 1;
          if (newDuration >= maxDuration) {
            stopRecording();
          }
          return newDuration;
        });
      }, 1000);

      // Start analysis
      updateAnalysis();
      
    } catch (err) {
      setError('Failed to access microphone. Please check permissions.');
      console.error('Recording error:', err);
    }
  }, [maxDuration, onRecordingComplete, state]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && state === 'recording') {
      mediaRecorderRef.current.stop();
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    }
  }, [state]);

  const pauseRecording = useCallback(() => {
    if (mediaRecorderRef.current && state === 'recording') {
      mediaRecorderRef.current.pause();
      setState('paused');
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }
  }, [state]);

  const resumeRecording = useCallback(() => {
    if (mediaRecorderRef.current && state === 'paused') {
      mediaRecorderRef.current.resume();
      setState('recording');
      
      intervalRef.current = setInterval(() => {
        setDuration(prev => {
          const newDuration = prev + 1;
          if (newDuration >= maxDuration) {
            stopRecording();
          }
          return newDuration;
        });
      }, 1000);
    }
  }, [maxDuration, state, stopRecording]);

  const resetRecording = useCallback(() => {
    setState('idle');
    setDuration(0);
    setAudioBlob(null);
    setTranscription('');
    setConfidence(0);
    setError('');
    setAudioAnalysis({ volume: 0, frequency: 0 });
  }, []);

  const processRecording = useCallback(async () => {
    if (!audioBlob) return;

    setState('transcribing');
    setError('');

    try {
      // Step 1: Transcribe audio using Granite Gateway
      const formData = new FormData();
      formData.append('audio_file', audioBlob, 'recording.webm');
      formData.append('language_code', 'en-US');
      formData.append('enable_medical_terms', 'true');

      const asrResponse = await fetch(`${graniteGatewayUrl}/v1/asr/`, {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': 'Bearer sk-symptomos-patient-app',
        },
      });

      if (!asrResponse.ok) {
        throw new Error(`ASR failed: ${asrResponse.status}`);
      }

      const asrData = await asrResponse.json();
      const transcribedText = asrData.transcription || '';
      const transcriptionConfidence = asrData.confidence || 0;

      setTranscription(transcribedText);
      setConfidence(transcriptionConfidence);
      onTranscriptionReceived?.(transcribedText, transcriptionConfidence);

      if (!transcribedText.trim()) {
        throw new Error('No speech detected in recording');
      }

      setState('parsing');

      // Step 2: Parse text for symptoms using parser service
      const parseResponse = await fetch(`${graniteGatewayUrl}/v1/parse/text`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer sk-symptomos-patient-app',
        },
        body: JSON.stringify({
          text: transcribedText,
          extract_symptoms: true,
          extract_medications: true,
          extract_temporal: true,
          extract_severity: true,
        }),
      });

      if (!parseResponse.ok) {
        throw new Error(`Parsing failed: ${parseResponse.status}`);
      }

      const parseData = await parseResponse.json();
      
      if (parseData.success && parseData.entity_count > 0) {
        onSymptomLogged?.(true, `Symptom logged: ${parseData.entity_count} entities detected`);
        setState('completed');
      } else {
        onSymptomLogged?.(false, 'No symptoms detected in recording');
        setState('completed');
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to process recording';
      setError(errorMessage);
      onSymptomLogged?.(false, errorMessage);
      setState('completed');
    }
  }, [audioBlob, graniteGatewayUrl, onTranscriptionReceived, onSymptomLogged]);

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStateColor = (): string => {
    switch (state) {
      case 'recording': return 'text-red-600';
      case 'paused': return 'text-yellow-600';
      case 'processing':
      case 'transcribing': 
      case 'parsing': return 'text-blue-600';
      case 'completed': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getStateMessage = (): string => {
    switch (state) {
      case 'idle': return 'Tap to record your symptoms';
      case 'recording': return 'Recording... Describe your symptoms';
      case 'paused': return 'Recording paused';
      case 'completed': return transcription ? 'Recording completed' : 'Ready to process';
      case 'transcribing': return 'Converting speech to text...';
      case 'parsing': return 'Extracting symptom information...';
      case 'processing': return 'Processing your recording...';
      default: return '';
    }
  };

  // Calculate volume visualization
  const volumeBars = Array.from({ length: 5 }, (_, i) => {
    const threshold = (i + 1) * 0.2;
    return audioAnalysis.volume > threshold;
  });

  return (
    <Card
      variant="elevated"
      className={cn(
        'w-full max-w-md mx-auto transition-all duration-300',
        state === 'recording' && 'ring-2 ring-red-500 ring-pulse',
        className
      )}
      {...props}
    >
      <Card.Header
        title="Voice Symptom Recorder"
        subtitle={getStateMessage()}
        action={
          <Badge 
            variant={
              state === 'recording' ? 'danger' : 
              state === 'completed' ? 'success' : 
              state === 'processing' || state === 'transcribing' || state === 'parsing' ? 'info' : 
              'default'
            }
          >
            {state === 'idle' ? 'Ready' : state.charAt(0).toUpperCase() + state.slice(1)}
          </Badge>
        }
      />

      <Card.Content>
        <div className="space-y-4">
          {/* Recording Visualization */}
          <div className="flex flex-col items-center space-y-4">
            {/* Main Record Button */}
            <div className="relative">
              <Button
                size="lg"
                variant={state === 'recording' ? 'danger' : 'primary'}
                className={cn(
                  'w-20 h-20 rounded-full p-0',
                  state === 'recording' && 'animate-pulse',
                  disabled && 'opacity-50 cursor-not-allowed'
                )}
                disabled={disabled || state === 'processing' || state === 'transcribing' || state === 'parsing'}
                onClick={state === 'idle' ? startRecording : state === 'recording' ? stopRecording : undefined}
              >
                {state === 'idle' && <Mic className="w-8 h-8" />}
                {state === 'recording' && <Square className="w-6 h-6" />}
                {(state === 'processing' || state === 'transcribing' || state === 'parsing') && (
                  <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
                )}
                {state === 'completed' && <MicOff className="w-8 h-8" />}
                {state === 'paused' && <Play className="w-8 h-8" />}
              </Button>

              {/* Volume visualization rings */}
              {state === 'recording' && (
                <div className="absolute inset-0 pointer-events-none">
                  {volumeBars.map((active, i) => (
                    <div
                      key={i}
                      className={cn(
                        'absolute inset-0 rounded-full border-2 transition-opacity duration-100',
                        active ? 'border-red-400 opacity-70' : 'border-red-200 opacity-20'
                      )}
                      style={{
                        transform: `scale(${1 + (i * 0.2)})`,
                        animationDelay: `${i * 100}ms`,
                      }}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Duration Display */}
            {duration > 0 && (
              <div className={cn('text-2xl font-mono font-bold', getStateColor())}>
                {formatDuration(duration)}
              </div>
            )}

            {/* Control Buttons */}
            {(state === 'recording' || state === 'paused') && (
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={state === 'recording' ? pauseRecording : resumeRecording}
                >
                  {state === 'recording' ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={resetRecording}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            )}

            {/* Process Button */}
            {state === 'completed' && audioBlob && (
              <Button
                variant="primary"
                onClick={processRecording}
                icon={<Send className="w-4 h-4" />}
                className="w-full"
              >
                Process Recording
              </Button>
            )}
          </div>

          {/* Transcription Display */}
          {transcription && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-start mb-2">
                <span className="text-sm font-medium text-gray-700">Transcription:</span>
                <Badge variant="info" size="sm">
                  {Math.round(confidence * 100)}% confidence
                </Badge>
              </div>
              <p className="text-sm text-gray-800">{transcription}</p>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="p-3 bg-red-50 rounded-lg">
              <p className="text-sm text-red-700">{error}</p>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setError('')}
                className="mt-2"
              >
                Dismiss
              </Button>
            </div>
          )}
        </div>
      </Card.Content>

      {(state === 'idle' || state === 'completed') && (
        <Card.Footer>
          <div className="text-xs text-gray-500 text-center">
            Your voice data is processed securely and not stored permanently.
          </div>
        </Card.Footer>
      )}
    </Card>
  );
};