import type { Meta, StoryObj } from '@storybook/react';
import { Card } from './Card';
import { Button } from './Button';
import { Badge } from './Badge';
import { Settings, MoreVertical } from 'lucide-react';

const meta: Meta<typeof Card> = {
  title: 'Components/Card',
  component: Card,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A flexible card component with header, content, and footer sections.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'elevated', 'outlined'],
    },
    padding: {
      control: 'select',
      options: ['none', 'sm', 'md', 'lg'],
    },
    hover: {
      control: 'boolean',
    },
  },
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: (
      <div>
        <h3 className="text-lg font-semibold">Card Title</h3>
        <p className="text-gray-600 mt-2">This is the card content area.</p>
      </div>
    ),
  },
};

export const Elevated: Story = {
  args: {
    variant: 'elevated',
    children: (
      <div>
        <h3 className="text-lg font-semibold">Elevated Card</h3>
        <p className="text-gray-600 mt-2">This card has a shadow elevation.</p>
      </div>
    ),
  },
};

export const Outlined: Story = {
  args: {
    variant: 'outlined',
    children: (
      <div>
        <h3 className="text-lg font-semibold">Outlined Card</h3>
        <p className="text-gray-600 mt-2">This card has a thick outline.</p>
      </div>
    ),
  },
};

export const WithHover: Story = {
  args: {
    hover: true,
    children: (
      <div>
        <h3 className="text-lg font-semibold">Hover Card</h3>
        <p className="text-gray-600 mt-2">Hover over this card to see the effect.</p>
      </div>
    ),
  },
};

export const NoPadding: Story = {
  args: {
    padding: 'none',
    children: (
      <div className="p-4 bg-gray-50">
        <h3 className="text-lg font-semibold">Custom Padding</h3>
        <p className="text-gray-600 mt-2">This card has no default padding.</p>
      </div>
    ),
  },
};

export const WithHeader: Story = {
  render: () => (
    <Card className="w-80">
      <Card.Header
        title="Patient Record"
        subtitle="Last updated 2 hours ago"
        action={
          <Button variant="ghost" size="sm">
            <MoreVertical className="w-4 h-4" />
          </Button>
        }
      />
      <Card.Content>
        <p className="text-gray-600">
          Patient shows improvement in symptoms. Continue current medication
          regimen and schedule follow-up in 2 weeks.
        </p>
      </Card.Content>
      <Card.Footer>
        <div className="flex justify-between items-center">
          <Badge variant="success">Stable</Badge>
          <Button size="sm" variant="outline">
            View Details
          </Button>
        </div>
      </Card.Footer>
    </Card>
  ),
};

export const SimpleCard: Story = {
  render: () => (
    <Card className="w-64">
      <Card.Header title="Symptom Alert" />
      <Card.Content>
        <div className="space-y-2">
          <p className="text-sm text-gray-600">Severe headache reported</p>
          <Badge variant="danger" size="sm">High Priority</Badge>
        </div>
      </Card.Content>
    </Card>
  ),
};

export const ActionCard: Story = {
  render: () => (
    <Card hover className="w-72 cursor-pointer">
      <Card.Header
        title="Record Symptoms"
        subtitle="Tap to start voice recording"
        action={<Settings className="w-5 h-5 text-gray-400" />}
      />
      <Card.Content>
        <div className="flex items-center justify-center py-8">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            <div className="w-8 h-8 bg-blue-500 rounded-full" />
          </div>
        </div>
      </Card.Content>
    </Card>
  ),
};

export const PatientCard: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-4xl">
      <Card variant="elevated">
        <Card.Header
          title="John Doe"
          subtitle="Patient ID: #12345"
          action={<Badge variant="success">Active</Badge>}
        />
        <Card.Content>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Age:</span>
              <span className="text-sm font-medium">45</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Last Visit:</span>
              <span className="text-sm font-medium">Dec 15, 2023</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Condition:</span>
              <Badge variant="warning" size="sm">Monitoring</Badge>
            </div>
          </div>
        </Card.Content>
        <Card.Footer>
          <Button size="sm" className="w-full">
            View Timeline
          </Button>
        </Card.Footer>
      </Card>

      <Card variant="outlined">
        <Card.Header title="Recent Symptoms" />
        <Card.Content>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-red-500 rounded-full" />
              <span className="text-sm">Headache (Severity: 7/10)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full" />
              <span className="text-sm">Fatigue (Mild)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full" />
              <span className="text-sm">Nausea (Occasional)</span>
            </div>
          </div>
        </Card.Content>
        <Card.Footer>
          <Button variant="outline" size="sm" className="w-full">
            Add Symptom
          </Button>
        </Card.Footer>
      </Card>
    </div>
  ),
};