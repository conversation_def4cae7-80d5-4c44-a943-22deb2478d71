import type { <PERSON>a, StoryObj } from '@storybook/react';
import { RingNode, RingCluster, type RingNodeData } from './RingNode';

const meta: Meta<typeof RingNode> = {
  title: 'Components/RingNode',
  component: RingNode,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A specialized visualization component for medical timeline data, representing events as interactive ring nodes.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg', 'xl'],
    },
    selected: {
      control: 'boolean',
    },
    pulse: {
      control: 'boolean',
    },
    interactive: {
      control: 'boolean',
    },
    showLabel: {
      control: 'boolean',
    },
    showTimestamp: {
      control: 'boolean',
    },
  },
} satisfies Meta<typeof RingNode>;

export default meta;
type Story = StoryObj<typeof meta>;

const sampleSymptom: RingNodeData = {
  id: '1',
  type: 'symptom',
  label: 'Headache',
  timestamp: new Date('2023-12-15T14:30:00'),
  severity: 8,
  confidence: 0.9,
  metadata: {
    duration: '3 hours',
    location: 'temporal',
  },
};

const sampleMedication: RingNodeData = {
  id: '2',
  type: 'medication',
  label: 'Ibuprofen',
  timestamp: new Date('2023-12-15T15:00:00'),
  confidence: 0.95,
  metadata: {
    dosage: '400mg',
    route: 'oral',
  },
};

export const SymptomNode: Story = {
  args: {
    data: sampleSymptom,
  },
};

export const MedicationNode: Story = {
  args: {
    data: sampleMedication,
  },
};

export const EventNode: Story = {
  args: {
    data: {
      id: '3',
      type: 'event',
      label: 'Doctor Visit',
      timestamp: new Date('2023-12-15T10:00:00'),
      confidence: 1.0,
    },
  },
};

export const TestNode: Story = {
  args: {
    data: {
      id: '4',
      type: 'test',
      label: 'Blood Test',
      timestamp: new Date('2023-12-15T09:00:00'),
      confidence: 0.85,
    },
  },
};

export const DiagnosisNode: Story = {
  args: {
    data: {
      id: '5',
      type: 'diagnosis',
      label: 'Migraine',
      timestamp: new Date('2023-12-15T11:00:00'),
      confidence: 0.8,
    },
  },
};

export const SevereLowConfidence: Story = {
  args: {
    data: {
      id: '6',
      type: 'symptom',
      label: 'Chest Pain',
      timestamp: new Date(),
      severity: 9,
      confidence: 0.4,
    },
  },
};

export const Selected: Story = {
  args: {
    data: sampleSymptom,
    selected: true,
  },
};

export const Pulsing: Story = {
  args: {
    data: {
      ...sampleSymptom,
      severity: 9,
    },
    pulse: true,
  },
};

export const SmallSize: Story = {
  args: {
    data: sampleSymptom,
    size: 'sm',
  },
};

export const LargeSize: Story = {
  args: {
    data: sampleSymptom,
    size: 'lg',
  },
};

export const XLargeSize: Story = {
  args: {
    data: sampleSymptom,
    size: 'xl',
  },
};

export const WithTimestamp: Story = {
  args: {
    data: sampleSymptom,
    showTimestamp: true,
  },
};

export const NoLabel: Story = {
  args: {
    data: sampleSymptom,
    showLabel: false,
  },
};

export const NonInteractive: Story = {
  args: {
    data: sampleSymptom,
    interactive: false,
  },
};

export const AllNodeTypes: Story = {
  render: () => {
    const nodes: RingNodeData[] = [
      {
        id: '1',
        type: 'symptom',
        label: 'Headache',
        timestamp: new Date('2023-12-15T14:30:00'),
        severity: 8,
        confidence: 0.9,
      },
      {
        id: '2',
        type: 'medication',
        label: 'Ibuprofen',
        timestamp: new Date('2023-12-15T15:00:00'),
        confidence: 0.95,
      },
      {
        id: '3',
        type: 'event',
        label: 'Doctor Visit',
        timestamp: new Date('2023-12-15T10:00:00'),
        confidence: 1.0,
      },
      {
        id: '4',
        type: 'test',
        label: 'Blood Test',
        timestamp: new Date('2023-12-15T09:00:00'),
        confidence: 0.85,
      },
      {
        id: '5',
        type: 'diagnosis',
        label: 'Migraine',
        timestamp: new Date('2023-12-15T11:00:00'),
        confidence: 0.8,
      },
    ];

    return (
      <div className="grid grid-cols-5 gap-8">
        {nodes.map((node) => (
          <div key={node.id} className="text-center">
            <RingNode data={node} showTimestamp />
            <p className="mt-2 text-xs text-gray-500 capitalize">{node.type}</p>
          </div>
        ))}
      </div>
    );
  },
};

export const SeverityVariations: Story = {
  render: () => {
    const severities = [1, 3, 5, 7, 9];
    
    return (
      <div className="flex gap-8 items-center">
        {severities.map((severity) => (
          <div key={severity} className="text-center">
            <RingNode
              data={{
                id: `severity-${severity}`,
                type: 'symptom',
                label: 'Pain',
                timestamp: new Date(),
                severity,
                confidence: 0.9,
              }}
            />
            <p className="mt-2 text-xs text-gray-500">Severity: {severity}/10</p>
          </div>
        ))}
      </div>
    );
  },
};

export const ConfidenceVariations: Story = {
  render: () => {
    const confidences = [0.2, 0.4, 0.6, 0.8, 1.0];
    
    return (
      <div className="flex gap-8 items-center">
        {confidences.map((confidence) => (
          <div key={confidence} className="text-center">
            <RingNode
              data={{
                id: `confidence-${confidence}`,
                type: 'symptom',
                label: 'Headache',
                timestamp: new Date(),
                severity: 5,
                confidence,
              }}
            />
            <p className="mt-2 text-xs text-gray-500">
              Confidence: {Math.round(confidence * 100)}%
            </p>
          </div>
        ))}
      </div>
    );
  },
};

// RingCluster Stories
export const HorizontalCluster: Story = {
  render: () => {
    const nodes: RingNodeData[] = [
      {
        id: '1',
        type: 'symptom',
        label: 'Headache',
        timestamp: new Date('2023-12-15T14:30:00'),
        severity: 8,
        confidence: 0.9,
      },
      {
        id: '2',
        type: 'medication',
        label: 'Ibuprofen',
        timestamp: new Date('2023-12-15T15:00:00'),
        confidence: 0.95,
      },
      {
        id: '3',
        type: 'event',
        label: 'Rest',
        timestamp: new Date('2023-12-15T16:00:00'),
        confidence: 1.0,
      },
    ];

    return (
      <RingCluster
        nodes={nodes}
        layout="horizontal"
        onNodeClick={(node) => console.log('Clicked:', node.label)}
      />
    );
  },
};

export const VerticalCluster: Story = {
  render: () => {
    const nodes: RingNodeData[] = [
      {
        id: '1',
        type: 'symptom',
        label: 'Headache',
        timestamp: new Date('2023-12-15T14:30:00'),
        severity: 8,
        confidence: 0.9,
      },
      {
        id: '2',
        type: 'medication',
        label: 'Ibuprofen',
        timestamp: new Date('2023-12-15T15:00:00'),
        confidence: 0.95,
      },
      {
        id: '3',
        type: 'event',
        label: 'Rest',
        timestamp: new Date('2023-12-15T16:00:00'),
        confidence: 1.0,
      },
    ];

    return (
      <RingCluster
        nodes={nodes}
        layout="vertical"
        onNodeClick={(node) => console.log('Clicked:', node.label)}
      />
    );
  },
};

export const CircularCluster: Story = {
  render: () => {
    const nodes: RingNodeData[] = [
      {
        id: '1',
        type: 'symptom',
        label: 'Head',
        timestamp: new Date('2023-12-15T14:30:00'),
        severity: 8,
        confidence: 0.9,
      },
      {
        id: '2',
        type: 'medication',
        label: 'Ibu',
        timestamp: new Date('2023-12-15T15:00:00'),
        confidence: 0.95,
      },
      {
        id: '3',
        type: 'event',
        label: 'Rest',
        timestamp: new Date('2023-12-15T16:00:00'),
        confidence: 1.0,
      },
      {
        id: '4',
        type: 'test',
        label: 'BP',
        timestamp: new Date('2023-12-15T17:00:00'),
        confidence: 0.85,
      },
      {
        id: '5',
        type: 'diagnosis',
        label: 'Migr',
        timestamp: new Date('2023-12-15T18:00:00'),
        confidence: 0.8,
      },
    ];

    return (
      <RingCluster
        nodes={nodes}
        layout="circular"
        spacing="tight"
        onNodeClick={(node) => console.log('Clicked:', node.label)}
      />
    );
  },
};