import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Tag } from './Tag';
import { User, Calendar, Pill, Activity } from 'lucide-react';

const meta: Meta<typeof Tag> = {
  title: 'Components/Tag',
  component: Tag,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A tag component for labeling and categorizing content with optional remove functionality.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'success', 'warning', 'danger', 'info'],
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
    },
    removable: {
      control: 'boolean',
    },
  },
} satisfies Meta<typeof Tag>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Default Tag',
  },
};

export const Success: Story = {
  args: {
    children: 'Success',
    variant: 'success',
  },
};

export const Warning: Story = {
  args: {
    children: 'Warning',
    variant: 'warning',
  },
};

export const Danger: Story = {
  args: {
    children: 'Danger',
    variant: 'danger',
  },
};

export const Info: Story = {
  args: {
    children: 'Info',
    variant: 'info',
  },
};

export const Small: Story = {
  args: {
    children: 'Small Tag',
    size: 'sm',
  },
};

export const Large: Story = {
  args: {
    children: 'Large Tag',
    size: 'lg',
  },
};

export const Removable: Story = {
  args: {
    children: 'Removable Tag',
    removable: true,
    onRemove: () => alert('Tag removed!'),
  },
};

export const WithIcon: Story = {
  args: {
    children: 'Patient',
    icon: <User className="w-3 h-3" />,
    variant: 'info',
  },
};

export const RemovableWithIcon: Story = {
  args: {
    children: 'Medication',
    icon: <Pill className="w-3 h-3" />,
    variant: 'success',
    removable: true,
    onRemove: () => alert('Medication tag removed!'),
  },
};

export const MedicalTags: Story = {
  render: () => (
    <div className="flex flex-wrap gap-2">
      <Tag variant="danger" icon={<Activity className="w-3 h-3" />}>
        Severe Headache
      </Tag>
      <Tag variant="warning" icon={<Pill className="w-3 h-3" />}>
        Ibuprofen 400mg
      </Tag>
      <Tag variant="info" icon={<Calendar className="w-3 h-3" />}>
        Dec 15, 2023
      </Tag>
      <Tag variant="success">
        Improving
      </Tag>
      <Tag variant="default" removable onRemove={() => {}}>
        Custom Note
      </Tag>
    </div>
  ),
};

export const SymptomTags: Story = {
  render: () => (
    <div className="space-y-3">
      <div className="flex flex-wrap gap-2">
        <span className="text-sm font-medium text-gray-700">Current Symptoms:</span>
        <Tag variant="danger" size="sm" removable onRemove={() => {}}>
          Headache (8/10)
        </Tag>
        <Tag variant="warning" size="sm" removable onRemove={() => {}}>
          Nausea
        </Tag>
        <Tag variant="info" size="sm" removable onRemove={() => {}}>
          Fatigue
        </Tag>
      </div>
      
      <div className="flex flex-wrap gap-2">
        <span className="text-sm font-medium text-gray-700">Medications:</span>
        <Tag variant="success" size="sm" icon={<Pill className="w-3 h-3" />}>
          Ibuprofen 400mg
        </Tag>
        <Tag variant="success" size="sm" icon={<Pill className="w-3 h-3" />}>
          Acetaminophen 500mg
        </Tag>
      </div>
      
      <div className="flex flex-wrap gap-2">
        <span className="text-sm font-medium text-gray-700">Timeline:</span>
        <Tag variant="default" size="sm" icon={<Calendar className="w-3 h-3" />}>
          Started 3 days ago
        </Tag>
        <Tag variant="default" size="sm" icon={<Calendar className="w-3 h-3" />}>
          Peak yesterday
        </Tag>
      </div>
    </div>
  ),
};

export const InteractiveTagList: Story = {
  render: () => {
    const tags = [
      { id: 1, label: 'Headache', variant: 'danger' as const },
      { id: 2, label: 'Medication Taken', variant: 'success' as const },
      { id: 3, label: 'Follow-up Required', variant: 'warning' as const },
      { id: 4, label: 'Lab Results', variant: 'info' as const },
    ];

    return (
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-900">Patient Tags</h4>
        <div className="flex flex-wrap gap-2">
          {tags.map((tag) => (
            <Tag
              key={tag.id}
              variant={tag.variant}
              removable
              onRemove={() => console.log(`Removed tag: ${tag.label}`)}
            >
              {tag.label}
            </Tag>
          ))}
        </div>
      </div>
    );
  },
};