import React from 'react';
import { Badge, BadgeProps } from './Badge';
import { X } from 'lucide-react';
import { cn } from '../utils/cn';

export interface TagProps extends Omit<BadgeProps, 'dot'> {
  removable?: boolean;
  onRemove?: () => void;
  icon?: React.ReactNode;
}

export const Tag: React.FC<TagProps> = ({
  removable = false,
  onRemove,
  icon,
  className,
  children,
  ...props
}) => {
  return (
    <Badge
      className={cn(
        'inline-flex items-center gap-1',
        removable && 'pr-1',
        className
      )}
      {...props}
    >
      {icon && <span className="w-3 h-3">{icon}</span>}
      {children}
      {removable && (
        <button
          type="button"
          onClick={onRemove}
          className={cn(
            'ml-1 w-4 h-4 rounded-full flex items-center justify-center',
            'hover:bg-black/10 transition-colors',
            'focus:outline-none focus:ring-1 focus:ring-offset-1'
          )}
          aria-label="Remove tag"
        >
          <X className="w-3 h-3" />
        </button>
      )}
    </Badge>
  );
};