import { useState, useEffect, useCallback } from 'react';

export interface GuardianFeedback {
  feedback_id: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  violation_type: string;
  action_taken: string;
  recommendations: string[];
  show_duration: number;
  dismissible: boolean;
  requires_acknowledgment: boolean;
  timestamp: string;
}

export interface UseGuardianFeedbackOptions {
  /** Guardian service URL. Defaults to http://localhost:8014 */
  guardianServiceUrl?: string;
  /** Authentication token for Guardian service */
  authToken?: string;
  /** Maximum number of feedback items to keep in memory */
  maxFeedbackItems?: number;
  /** Whether to automatically fetch safety alerts */
  autoFetch?: boolean;
  /** Fetch interval in milliseconds */
  fetchInterval?: number;
  /** Patient ID to filter alerts for */
  patientId?: string;
}

export interface UseGuardianFeedbackReturn {
  /** Current feedback items */
  feedbackItems: GuardianFeedback[];
  /** Add a new feedback item */
  addFeedback: (feedback: GuardianFeedback) => void;
  /** Dismiss a feedback item */
  dismissFeedback: (feedbackId: string) => void;
  /** Acknowledge a feedback item */
  acknowledgeFeedback: (feedbackId: string) => void;
  /** Clear all feedback items */
  clearAllFeedback: () => void;
  /** Get feedback items by severity */
  getFeedbackBySeverity: (severity: GuardianFeedback['severity']) => GuardianFeedback[];
  /** Check if there are any critical alerts */
  hasCriticalAlerts: boolean;
  /** Check if there are any unacknowledged alerts requiring acknowledgment */
  hasUnacknowledgedAlerts: boolean;
  /** Fetch latest safety alerts from Guardian service */
  fetchSafetyAlerts: () => Promise<void>;
  /** Loading state */
  loading: boolean;
  /** Error state */
  error: string | null;
}

export const useGuardianFeedback = (options: UseGuardianFeedbackOptions = {}): UseGuardianFeedbackReturn => {
  const {
    guardianServiceUrl = 'http://localhost:8014',
    authToken = 'sk-symptomos-doctor-desktop',
    maxFeedbackItems = 50,
    autoFetch = true,
    fetchInterval = 30000, // 30 seconds
    patientId
  } = options;

  const [feedbackItems, setFeedbackItems] = useState<GuardianFeedback[]>([]);
  const [acknowledgedItems, setAcknowledgedItems] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const addFeedback = useCallback((feedback: GuardianFeedback) => {
    setFeedbackItems(prev => {
      const filtered = prev.filter(item => item.feedback_id !== feedback.feedback_id);
      const updated = [feedback, ...filtered].slice(0, maxFeedbackItems);
      return updated;
    });
  }, [maxFeedbackItems]);

  const dismissFeedback = useCallback((feedbackId: string) => {
    setFeedbackItems(prev => prev.filter(item => item.feedback_id !== feedbackId));
  }, []);

  const acknowledgeFeedback = useCallback(async (feedbackId: string) => {
    setAcknowledgedItems(prev => new Set([...prev, feedbackId]));
    
    // Send acknowledgment to Guardian service
    try {
      await fetch(`${guardianServiceUrl}/v1/feedback/${feedbackId}/acknowledge`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });
    } catch (err) {
      console.warn('Failed to send acknowledgment to Guardian service:', err);
    }
  }, [guardianServiceUrl, authToken]);

  const clearAllFeedback = useCallback(() => {
    setFeedbackItems([]);
    setAcknowledgedItems(new Set());
  }, []);

  const getFeedbackBySeverity = useCallback((severity: GuardianFeedback['severity']) => {
    return feedbackItems.filter(item => item.severity === severity);
  }, [feedbackItems]);

  const hasCriticalAlerts = feedbackItems.some(item => item.severity === 'critical');
  
  const hasUnacknowledgedAlerts = feedbackItems.some(item => 
    item.requires_acknowledgment && !acknowledgedItems.has(item.feedback_id)
  );

  const fetchSafetyAlerts = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const url = new URL(`${guardianServiceUrl}/v1/safety-alerts`);
      if (patientId) {
        url.searchParams.set('patient_id', patientId);
      }
      url.searchParams.set('limit', '20');

      const response = await fetch(url.toString(), {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch safety alerts: ${response.status}`);
      }

      const alerts = await response.json();
      
      // Transform alerts to feedback format
      const feedbackItems: GuardianFeedback[] = alerts.map((alert: any) => ({
        feedback_id: alert.alert_id || alert.violation_id,
        message: alert.description || alert.violation?.description || 'Safety violation detected',
        severity: alert.severity || alert.violation?.severity || 'medium',
        violation_type: alert.violation_type || alert.violation?.violation_type || 'policy_violation',
        action_taken: alert.action_taken || 'Content blocked by Guardian',
        recommendations: alert.remediation_suggestions || alert.violation?.remediation_suggestions || [],
        show_duration: getSeverityDuration(alert.severity || 'medium'),
        dismissible: true,
        requires_acknowledgment: (alert.severity === 'critical' || alert.severity === 'high'),
        timestamp: alert.timestamp || alert.created_at || new Date().toISOString()
      }));

      // Add new feedback items
      feedbackItems.forEach(feedback => addFeedback(feedback));

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      console.error('Failed to fetch safety alerts:', err);
    } finally {
      setLoading(false);
    }
  }, [guardianServiceUrl, authToken, patientId, addFeedback]);

  // Auto-fetch safety alerts
  useEffect(() => {
    if (autoFetch) {
      fetchSafetyAlerts();
      
      const interval = setInterval(fetchSafetyAlerts, fetchInterval);
      return () => clearInterval(interval);
    }
  }, [autoFetch, fetchSafetyAlerts, fetchInterval]);

  return {
    feedbackItems,
    addFeedback,
    dismissFeedback,
    acknowledgeFeedback,
    clearAllFeedback,
    getFeedbackBySeverity,
    hasCriticalAlerts,
    hasUnacknowledgedAlerts,
    fetchSafetyAlerts,
    loading,
    error
  };
};

// Helper function to get show duration based on severity
const getSeverityDuration = (severity: string): number => {
  switch (severity) {
    case 'critical':
      return 0; // Never auto-dismiss critical alerts
    case 'high':
      return 15000; // 15 seconds
    case 'medium':
      return 10000; // 10 seconds
    case 'low':
      return 5000; // 5 seconds
    default:
      return 8000; // 8 seconds
  }
};

// Utility function to create feedback from Guardian response
export const createFeedbackFromGuardianResponse = (
  guardianResponse: any,
  actionTaken: string = 'Content reviewed by Guardian'
): GuardianFeedback => {
  const severity = guardianResponse.risk_level || 'medium';
  const violationType = guardianResponse.violation_id ? 'safety_violation' : 'content_review';
  
  return {
    feedback_id: guardianResponse.violation_id || `guardian_${Date.now()}`,
    message: guardianResponse.rationale || 'Content has been reviewed for safety',
    severity: severity as GuardianFeedback['severity'],
    violation_type: violationType,
    action_taken: actionTaken,
    recommendations: guardianResponse.policy_violations || [],
    show_duration: getSeverityDuration(severity),
    dismissible: !guardianResponse.violation_id, // Violations require acknowledgment
    requires_acknowledgment: !!guardianResponse.violation_id,
    timestamp: new Date().toISOString()
  };
};

// Utility function to create feedback for blocked content
export const createBlockedContentFeedback = (
  reason: string,
  severity: GuardianFeedback['severity'] = 'high',
  recommendations: string[] = []
): GuardianFeedback => {
  return {
    feedback_id: `blocked_${Date.now()}`,
    message: `Content blocked: ${reason}`,
    severity,
    violation_type: 'content_blocked',
    action_taken: 'Content blocked by Guardian',
    recommendations: recommendations.length > 0 ? recommendations : [
      'Review content for safety compliance',
      'Ensure medical accuracy',
      'Add appropriate disclaimers'
    ],
    show_duration: getSeverityDuration(severity),
    dismissible: severity !== 'critical',
    requires_acknowledgment: severity === 'critical' || severity === 'high',
    timestamp: new Date().toISOString()
  };
};
