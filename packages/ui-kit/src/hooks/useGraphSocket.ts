import { useState, useEffect, useRef, useCallback } from 'react';

export interface GraphUpdate {
  type: 'node_added' | 'node_updated' | 'edge_added' | 'edge_updated' | 'node_deleted' | 'edge_deleted' | 'connection_established' | 'heartbeat' | 'pong';
  timestamp: string;
  patient_id?: string;
  data?: any;
  message?: string;
  stats?: {
    global_connections: number;
    patient_connections: number;
    patients_with_connections: number;
    total_connections: number;
  };
}

export interface UseGraphSocketOptions {
  /** Patient ID to filter updates for. If not provided, receives all updates */
  patientId?: string;
  /** WebSocket server URL. Defaults to ws://localhost:8030 */
  serverUrl?: string;
  /** Whether to automatically reconnect on disconnect */
  autoReconnect?: boolean;
  /** Reconnection delay in milliseconds */
  reconnectDelay?: number;
  /** Maximum number of reconnection attempts */
  maxReconnectAttempts?: number;
  /** Authentication token for WebSocket connection */
  authToken?: string;
  /** Whether to enable debug logging */
  debug?: boolean;
}

export interface UseGraphSocketReturn {
  /** Current connection status */
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  /** Latest graph update received */
  lastUpdate: GraphUpdate | null;
  /** All updates received during this session */
  updates: GraphUpdate[];
  /** Connection statistics from the server */
  connectionStats: GraphUpdate['stats'] | null;
  /** Error message if connection failed */
  error: string | null;
  /** Manually reconnect to the WebSocket */
  reconnect: () => void;
  /** Send a ping to the server */
  ping: () => void;
  /** Clear all stored updates */
  clearUpdates: () => void;
}

export const useGraphSocket = (options: UseGraphSocketOptions = {}): UseGraphSocketReturn => {
  const {
    patientId,
    serverUrl = 'ws://localhost:8030',
    autoReconnect = true,
    reconnectDelay = 3000,
    maxReconnectAttempts = 5,
    authToken,
    debug = false
  } = options;

  const [connectionStatus, setConnectionStatus] = useState<UseGraphSocketReturn['connectionStatus']>('disconnected');
  const [lastUpdate, setLastUpdate] = useState<GraphUpdate | null>(null);
  const [updates, setUpdates] = useState<GraphUpdate[]>([]);
  const [connectionStats, setConnectionStats] = useState<GraphUpdate['stats'] | null>(null);
  const [error, setError] = useState<string | null>(null);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const log = useCallback((message: string, ...args: any[]) => {
    if (debug) {
      console.log(`[useGraphSocket] ${message}`, ...args);
    }
  }, [debug]);

  const buildWebSocketUrl = useCallback(() => {
    const url = new URL(`${serverUrl}/ws/graph/updates`);
    
    if (patientId) {
      url.searchParams.set('patient_id', patientId);
    }
    
    if (authToken) {
      url.searchParams.set('token', authToken);
    }
    
    return url.toString();
  }, [serverUrl, patientId, authToken]);

  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const update: GraphUpdate = JSON.parse(event.data);
      log('Received update:', update);
      
      setLastUpdate(update);
      setUpdates(prev => [...prev, update]);
      
      // Handle specific message types
      switch (update.type) {
        case 'connection_established':
          setConnectionStatus('connected');
          setError(null);
          reconnectAttemptsRef.current = 0;
          log('Connection established');
          break;
          
        case 'heartbeat':
          if (update.stats) {
            setConnectionStats(update.stats);
          }
          log('Heartbeat received', update.stats);
          break;
          
        case 'pong':
          log('Pong received');
          break;
          
        default:
          // Regular graph updates
          log('Graph update received:', update.type, update.data);
          break;
      }
    } catch (err) {
      log('Failed to parse WebSocket message:', err);
      setError('Failed to parse server message');
    }
  }, [log]);

  const handleError = useCallback((event: Event) => {
    log('WebSocket error:', event);
    setConnectionStatus('error');
    setError('WebSocket connection error');
  }, [log]);

  const handleClose = useCallback((event: CloseEvent) => {
    log('WebSocket closed:', event.code, event.reason);
    setConnectionStatus('disconnected');
    
    // Clear ping interval
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }
    
    // Attempt reconnection if enabled and not manually closed
    if (autoReconnect && event.code !== 1000 && reconnectAttemptsRef.current < maxReconnectAttempts) {
      reconnectAttemptsRef.current += 1;
      log(`Attempting reconnection ${reconnectAttemptsRef.current}/${maxReconnectAttempts} in ${reconnectDelay}ms`);
      
      reconnectTimeoutRef.current = setTimeout(() => {
        connect();
      }, reconnectDelay);
    } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
      setError(`Failed to reconnect after ${maxReconnectAttempts} attempts`);
    }
  }, [autoReconnect, maxReconnectAttempts, reconnectDelay, log]);

  const connect = useCallback(() => {
    // Close existing connection
    if (wsRef.current) {
      wsRef.current.close();
    }
    
    // Clear any pending reconnection
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    try {
      const wsUrl = buildWebSocketUrl();
      log('Connecting to:', wsUrl);
      
      setConnectionStatus('connecting');
      setError(null);
      
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;
      
      ws.onopen = () => {
        log('WebSocket opened');
        // Start ping interval
        pingIntervalRef.current = setInterval(() => {
          ping();
        }, 30000); // Ping every 30 seconds
      };
      
      ws.onmessage = handleMessage;
      ws.onerror = handleError;
      ws.onclose = handleClose;
      
    } catch (err) {
      log('Failed to create WebSocket:', err);
      setConnectionStatus('error');
      setError('Failed to create WebSocket connection');
    }
  }, [buildWebSocketUrl, handleMessage, handleError, handleClose, log]);

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }
    
    reconnectAttemptsRef.current = 0;
  }, []);

  const ping = useCallback(() => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      const pingMessage = {
        type: 'ping',
        timestamp: new Date().toISOString()
      };
      wsRef.current.send(JSON.stringify(pingMessage));
      log('Ping sent');
    }
  }, [log]);

  const clearUpdates = useCallback(() => {
    setUpdates([]);
    setLastUpdate(null);
  }, []);

  // Connect on mount and when dependencies change
  useEffect(() => {
    connect();
    
    return () => {
      disconnect();
    };
  }, [patientId, serverUrl, authToken]); // Reconnect when these change

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, []);

  return {
    connectionStatus,
    lastUpdate,
    updates,
    connectionStats,
    error,
    reconnect: connect,
    ping,
    clearUpdates
  };
};
