import { format, formatDistanceToNow, isToday, isYesterday } from 'date-fns';

/**
 * Format a date for display in the UI
 */
export function formatDate(date: Date, formatString = 'MMM d, yyyy'): string {
  return format(date, formatString);
}

/**
 * Format a date relative to now (e.g., "2 hours ago")
 */
export function formatRelativeDate(date: Date): string {
  return formatDistanceToNow(date, { addSuffix: true });
}

/**
 * Format a date for timeline display
 */
export function formatTimelineDate(date: Date): string {
  if (isToday(date)) {
    return format(date, 'h:mm a');
  } else if (isYesterday(date)) {
    return `Yesterday, ${format(date, 'h:mm a')}`;
  } else {
    return format(date, 'MMM d, h:mm a');
  }
}

/**
 * Format a date for medical records
 */
export function formatMedicalDate(date: Date): string {
  return format(date, 'yyyy-MM-dd HH:mm:ss');
}
