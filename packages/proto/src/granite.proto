syntax = "proto3";

package symptomos.granite.v1;

import "google/protobuf/timestamp.proto";

// Audio format specifications
enum AudioFormat {
  AUDIO_FORMAT_UNSPECIFIED = 0;
  AUDIO_FORMAT_WAV = 1;
  AUDIO_FORMAT_MP3 = 2;
  AUDIO_FORMAT_FLAC = 3;
  AUDIO_FORMAT_OGG = 4;
}

// Text generation model types
enum ModelType {
  MODEL_TYPE_UNSPECIFIED = 0;
  MODEL_TYPE_GRANITE_3_3 = 1;
  MODEL_TYPE_GRANITE_MEDICAL = 2;
  MODEL_TYPE_GRANITE_CHAT = 3;
}

// Voice synthesis options
enum VoiceType {
  VOICE_TYPE_UNSPECIFIED = 0;
  VOICE_TYPE_NEUTRAL = 1;
  VOICE_TYPE_FEMALE = 2;
  VOICE_TYPE_MALE = 3;
  VOICE_TYPE_MEDICAL_PROFESSIONAL = 4;
}

// Speech-to-Text (ASR) Request
message ASRRequest {
  bytes audio_data = 1;
  AudioFormat format = 2;
  int32 sample_rate = 3;
  string language_code = 4; // e.g., "en-US"
  bool enable_medical_terms = 5;
  map<string, string> metadata = 6;
}

// Speech-to-Text (ASR) Response
message ASRResponse {
  string transcription = 1;
  double confidence = 2;
  repeated ASRAlternative alternatives = 3;
  repeated MedicalEntity extracted_entities = 4;
  google.protobuf.Timestamp processed_at = 5;
  map<string, string> metadata = 6;
}

// Alternative transcription results
message ASRAlternative {
  string text = 1;
  double confidence = 2;
}

// Extracted medical entities from speech
message MedicalEntity {
  string entity_type = 1; // symptom, medication, dosage, etc.
  string text = 2;
  double confidence = 3;
  int32 start_offset = 4;
  int32 end_offset = 5;
  map<string, string> attributes = 6;
}

// Text Generation Request
message GenerateRequest {
  string prompt = 1;
  ModelType model = 2;
  int32 max_tokens = 3;
  double temperature = 4;
  double top_p = 5;
  repeated string stop_sequences = 6;
  bool medical_context = 7;
  string patient_context = 8; // JSON string with patient info
  map<string, string> metadata = 9;
}

// Text Generation Response
message GenerateResponse {
  string generated_text = 1;
  double confidence = 2;
  int32 tokens_used = 3;
  string model_version = 4;
  repeated MedicalInsight insights = 5;
  google.protobuf.Timestamp generated_at = 6;
  map<string, string> metadata = 7;
}

// Medical insights from AI analysis
message MedicalInsight {
  string insight_type = 1; // pattern, risk, recommendation, etc.
  string description = 2;
  double confidence = 3;
  repeated string supporting_evidence = 4;
  map<string, string> metadata = 5;
}

// Text-to-Speech (TTS) Request
message TTSRequest {
  string text = 1;
  VoiceType voice = 2;
  AudioFormat output_format = 3;
  int32 sample_rate = 4;
  double speaking_rate = 5; // 0.5 to 2.0
  double pitch = 6; // -20.0 to 20.0
  string language_code = 7;
  map<string, string> metadata = 8;
}

// Text-to-Speech (TTS) Response
message TTSResponse {
  bytes audio_data = 1;
  AudioFormat format = 2;
  int32 sample_rate = 3;
  int64 duration_ms = 4;
  google.protobuf.Timestamp generated_at = 5;
  map<string, string> metadata = 6;
}

// Medical entity extraction request
message ExtractEntitiesRequest {
  string text = 1;
  repeated string entity_types = 2; // filter for specific types
  bool include_relationships = 3;
  string context = 4; // additional context for better extraction
  map<string, string> metadata = 5;
}

// Medical entity extraction response
message ExtractEntitiesResponse {
  repeated MedicalEntity entities = 1;
  repeated EntityRelationship relationships = 2;
  double overall_confidence = 3;
  google.protobuf.Timestamp processed_at = 4;
  map<string, string> metadata = 5;
}

// Relationship between medical entities
message EntityRelationship {
  string source_entity_id = 1;
  string target_entity_id = 2;
  string relationship_type = 3; // causes, treats, indicates, etc.
  double confidence = 4;
  map<string, string> metadata = 5;
}

// Batch processing request for multiple audio files
message BatchASRRequest {
  repeated ASRRequest requests = 1;
  bool parallel_processing = 2;
  map<string, string> metadata = 3;
}

// Batch processing response
message BatchASRResponse {
  repeated ASRResponse responses = 1;
  int32 successful_count = 2;
  int32 failed_count = 3;
  google.protobuf.Timestamp completed_at = 4;
  map<string, string> metadata = 5;
}

// Granite AI service definition
service GraniteService {
  // Speech-to-Text
  rpc ProcessSpeech(ASRRequest) returns (ASRResponse);
  rpc BatchProcessSpeech(BatchASRRequest) returns (BatchASRResponse);
  
  // Text Generation
  rpc GenerateText(GenerateRequest) returns (GenerateResponse);
  
  // Text-to-Speech
  rpc SynthesizeSpeech(TTSRequest) returns (TTSResponse);
  
  // Medical Entity Extraction
  rpc ExtractMedicalEntities(ExtractEntitiesRequest) returns (ExtractEntitiesResponse);
}
