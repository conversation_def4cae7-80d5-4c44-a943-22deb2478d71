syntax = "proto3";

package symptomos.medical.v1;

import "google/protobuf/timestamp.proto";

// Medical entity types
enum EntityType {
  ENTITY_TYPE_UNSPECIFIED = 0;
  ENTITY_TYPE_PATIENT = 1;
  ENTITY_TYPE_SYMPTOM = 2;
  ENTITY_TYPE_MEDICATION = 3;
  ENTITY_TYPE_DIAGNOSIS = 4;
  ENTITY_TYPE_VITAL_SIGN = 5;
  ENTITY_TYPE_TREATMENT = 6;
}

// Severity levels for symptoms
enum SeverityLevel {
  SEVERITY_LEVEL_UNSPECIFIED = 0;
  SEVERITY_LEVEL_MILD = 1;
  SEVERITY_LEVEL_MODERATE = 2;
  SEVERITY_LEVEL_SEVERE = 3;
  SEVERITY_LEVEL_CRITICAL = 4;
}

// Base medical entity
message MedicalEntity {
  string id = 1;
  EntityType entity_type = 2;
  google.protobuf.Timestamp timestamp = 3;
  map<string, string> metadata = 4;
}

// Patient information
message Patient {
  string id = 1;
  string name = 2;
  int32 age = 3;
  string gender = 4;
  repeated string medical_history = 5;
  google.protobuf.Timestamp created_at = 6;
  map<string, string> metadata = 7;
}

// Symptom information
message Symptom {
  string id = 1;
  string patient_id = 2;
  string name = 3;
  int32 severity = 4; // 1-10 scale
  SeverityLevel severity_level = 5;
  string description = 6;
  string location = 7;
  string duration = 8;
  repeated string triggers = 9;
  google.protobuf.Timestamp timestamp = 10;
  map<string, string> metadata = 11;
}

// Medication information
message Medication {
  string id = 1;
  string patient_id = 2;
  string name = 3;
  string dosage = 4;
  string frequency = 5;
  string route = 6; // oral, injection, etc.
  string indication = 7; // what it's for
  repeated string side_effects = 8;
  google.protobuf.Timestamp prescribed_at = 9;
  google.protobuf.Timestamp taken_at = 10;
  map<string, string> metadata = 11;
}

// Vital sign measurement
message VitalSign {
  string id = 1;
  string patient_id = 2;
  string measurement_type = 3; // temperature, blood_pressure, heart_rate, etc.
  double value = 4;
  string unit = 5;
  NormalRange normal_range = 6;
  bool is_abnormal = 7;
  google.protobuf.Timestamp timestamp = 8;
  map<string, string> metadata = 9;
}

// Normal range for vital signs
message NormalRange {
  double min_value = 1;
  double max_value = 2;
}

// Voice recording metadata
message VoiceRecording {
  string id = 1;
  string patient_id = 2;
  string file_path = 3;
  int64 duration_ms = 4;
  string format = 5; // wav, mp3, etc.
  int32 sample_rate = 6;
  google.protobuf.Timestamp recorded_at = 7;
  bool processed = 8;
  string transcription = 9;
  repeated string extracted_entities = 10;
  map<string, string> metadata = 11;
}

// AI analysis result
message AIAnalysis {
  string id = 1;
  string patient_id = 2;
  string analysis_type = 3; // pattern_detection, risk_assessment, etc.
  string model_version = 4;
  double confidence_score = 5;
  string result = 6;
  repeated string insights = 7;
  google.protobuf.Timestamp analyzed_at = 8;
  map<string, string> metadata = 9;
}
