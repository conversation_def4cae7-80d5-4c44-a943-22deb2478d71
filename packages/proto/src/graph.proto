syntax = "proto3";

package symptomos.graph.v1;

import "google/protobuf/timestamp.proto";
import "medical.proto";

// Graph node representation
message GraphNode {
  string id = 1;
  string node_type = 2;
  map<string, string> properties = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
  double curvature = 6;
}

// Graph edge representation
message GraphEdge {
  string id = 1;
  string source_id = 2;
  string target_id = 3;
  string relationship_type = 4;
  map<string, string> properties = 5;
  double weight = 6;
  google.protobuf.Timestamp created_at = 7;
}

// Query filters for nodes and edges
message QueryFilter {
  map<string, string> properties = 1;
  repeated string node_types = 2;
  repeated string relationship_types = 3;
  google.protobuf.Timestamp start_time = 4;
  google.protobuf.Timestamp end_time = 5;
  double min_curvature = 6;
  double max_curvature = 7;
}

// Temporal layer information
message TemporalLayer {
  string layer_id = 1;
  google.protobuf.Timestamp timestamp = 2;
  repeated GraphNode nodes = 3;
  repeated GraphEdge edges = 4;
  map<string, string> metadata = 5;
}

// Curvature analysis result
message CurvatureAnalysis {
  string node_id = 1;
  double ricci_curvature = 2;
  double anomaly_score = 3;
  string analysis_type = 4;
  google.protobuf.Timestamp analyzed_at = 5;
  map<string, string> metadata = 6;
}

// Anomaly detection result
message Anomaly {
  string id = 1;
  string node_id = 2;
  string anomaly_type = 3;
  double severity = 4;
  double confidence = 5;
  string description = 6;
  google.protobuf.Timestamp detected_at = 7;
  map<string, string> metadata = 8;
}

// Request/Response messages for graph operations

message AddNodeRequest {
  string node_id = 1;
  string node_type = 2;
  map<string, string> properties = 3;
  google.protobuf.Timestamp timestamp = 4;
}

message AddNodeResponse {
  bool success = 1;
  string message = 2;
  GraphNode node = 3;
}

message AddEdgeRequest {
  string source_id = 1;
  string target_id = 2;
  string relationship_type = 3;
  map<string, string> properties = 4;
  double weight = 5;
  google.protobuf.Timestamp timestamp = 6;
}

message AddEdgeResponse {
  bool success = 1;
  string message = 2;
  GraphEdge edge = 3;
}

message QueryNodesRequest {
  QueryFilter filter = 1;
  int32 limit = 2;
  int32 offset = 3;
}

message QueryNodesResponse {
  repeated GraphNode nodes = 1;
  int32 total_count = 2;
  bool has_more = 3;
}

message QueryEdgesRequest {
  QueryFilter filter = 1;
  int32 limit = 2;
  int32 offset = 3;
}

message QueryEdgesResponse {
  repeated GraphEdge edges = 1;
  int32 total_count = 2;
  bool has_more = 3;
}

message CalculateCurvatureRequest {
  string node_id = 1;
  string analysis_type = 2; // ricci, scalar, etc.
}

message CalculateCurvatureResponse {
  CurvatureAnalysis analysis = 1;
}

message DetectAnomaliesRequest {
  double threshold = 1;
  QueryFilter filter = 2;
  int32 limit = 3;
}

message DetectAnomaliesResponse {
  repeated Anomaly anomalies = 1;
  int32 total_count = 2;
}

message GetTemporalLayerRequest {
  google.protobuf.Timestamp timestamp = 1;
  int64 tolerance_ms = 2; // tolerance in milliseconds
}

message GetTemporalLayerResponse {
  TemporalLayer layer = 1;
}

// Graph service definition
service GraphService {
  // Node operations
  rpc AddNode(AddNodeRequest) returns (AddNodeResponse);
  rpc QueryNodes(QueryNodesRequest) returns (QueryNodesResponse);
  
  // Edge operations
  rpc AddEdge(AddEdgeRequest) returns (AddEdgeResponse);
  rpc QueryEdges(QueryEdgesRequest) returns (QueryEdgesResponse);
  
  // Curvature analysis
  rpc CalculateCurvature(CalculateCurvatureRequest) returns (CalculateCurvatureResponse);
  rpc DetectAnomalies(DetectAnomaliesRequest) returns (DetectAnomaliesResponse);
  
  // Temporal operations
  rpc GetTemporalLayer(GetTemporalLayerRequest) returns (GetTemporalLayerResponse);
}
