{"name": "proto", "version": "0.1.0", "description": "SymptomOS Protocol Buffers and gRPC definitions", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "buf generate", "lint": "buf lint", "format": "buf format -w", "clean": "rm -rf dist", "generate": "buf generate"}, "keywords": ["grpc", "protobuf", "symptomos", "medical"], "author": "SymptomOS Team", "license": "MIT", "devDependencies": {"@bufbuild/buf": "^1.28.0", "@bufbuild/protoc-gen-es": "^1.6.0", "@bufbuild/protoc-gen-connect-es": "^1.2.0", "@grpc/grpc-js": "^1.9.0", "@grpc/proto-loader": "^0.7.0", "typescript": "^5.3.0"}, "dependencies": {"@bufbuild/protobuf": "^1.6.0", "@connectrpc/connect": "^1.2.0"}}