"""
Unit tests for OnionGraph core functionality.

Tests the core OnionGraph data structure, node placement, edge creation,
and basic graph operations for the SymptomOS medical domain.

Author: SymptomOS Team
Version: 0.1
"""

import pytest
import time
import math
from typing import Dict, Any

from ogcontextengine import OnionGraph, Node, Edge, EventType, GOLDEN_ANGLE


class TestOnionGraph:
    """Test suite for OnionGraph core functionality."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.graph = OnionGraph()
        self.test_timestamp = time.time()
    
    def test_initialization(self):
        """Test OnionGraph initialization with nucleus node."""
        # Check initial state
        assert self.graph.get_node_count() == 1  # Nucleus node
        assert self.graph.get_layer_count() == 1  # Layer 0
        assert self.graph.get_edge_count() == 0   # No edges initially
        
        # Check nucleus node
        nucleus = self.graph.get_node("nucleus")
        assert nucleus is not None
        assert nucleus.layer == 0
        assert nucleus.event_type == EventType.NOTE
        assert nucleus.metadata["type"] == "nucleus"
    
    def test_add_event_creates_node(self):
        """Test that adding an event creates a node in the correct layer."""
        # Add a symptom event
        event_id = self.graph.add_event(
            "symptom_001",
            EventType.SYMPTOM,
            {"name": "nausea", "intensity": 5},
            self.test_timestamp
        )
        
        assert event_id == "symptom_001"
        assert self.graph.get_node_count() == 2  # Nucleus + new node
        
        # Check the created node
        node = self.graph.get_node("symptom_001")
        assert node is not None
        assert node.event_type == EventType.SYMPTOM
        assert node.metadata["name"] == "nausea"
        assert node.metadata["intensity"] == 5
        assert node.timestamp == self.test_timestamp
        
        # Check layer placement (symptom should go to layer 2, parent layer is 1)
        assert node.layer == 2
    
    def test_layer_counts(self):
        """Test layer creation and node distribution."""
        # Add events of different types
        events = [
            ("symptom_001", EventType.SYMPTOM, {"name": "headache"}),
            ("meal_001", EventType.MEAL, {"food": "apple"}),
            ("med_001", EventType.MEDICATION, {"name": "aspirin"}),
            ("vital_001", EventType.VITAL_SIGN, {"bp": "120/80"}),
        ]
        
        for event_id, event_type, metadata in events:
            self.graph.add_event(event_id, event_type, metadata, self.test_timestamp)
        
        # Check total counts
        assert self.graph.get_node_count() == 5  # Nucleus + 4 events
        assert self.graph.get_layer_count() >= 2  # At least layers 0 and 2
        
        # Check layer 2 has symptom and medication (both have parent layer 1)
        layer_2_nodes = self.graph.get_layer_nodes(2)
        layer_2_types = [node.event_type for node in layer_2_nodes]
        assert EventType.SYMPTOM in layer_2_types
        assert EventType.MEDICATION in layer_2_types
        
        # Check layer 3 has meal and vital_sign (both have parent layer 2)
        layer_3_nodes = self.graph.get_layer_nodes(3)
        layer_3_types = [node.event_type for node in layer_3_nodes]
        assert EventType.MEAL in layer_3_types
        assert EventType.VITAL_SIGN in layer_3_types
    
    def test_golden_angle_placement(self):
        """Test that nodes are placed using golden angle distribution."""
        # Add multiple nodes to the same layer
        for i in range(5):
            self.graph.add_event(
                f"symptom_{i:03d}",
                EventType.SYMPTOM,
                {"name": f"symptom_{i}"},
                self.test_timestamp + i
            )
        
        # Get all nodes in layer 2 (where symptoms go)
        layer_2_nodes = self.graph.get_layer_nodes(2)
        symptom_nodes = [node for node in layer_2_nodes if node.event_type == EventType.SYMPTOM]
        
        # Check that angles follow golden angle pattern
        expected_angles = [(GOLDEN_ANGLE * i) % (2 * math.pi) for i in range(len(symptom_nodes))]
        actual_angles = sorted([node.angle for node in symptom_nodes])
        expected_angles.sort()
        
        # Allow small floating point differences
        for actual, expected in zip(actual_angles, expected_angles):
            assert abs(actual - expected) < 1e-10
    
    def test_neighbor_retrieval(self):
        """Test neighbor finding functionality."""
        # Add several nodes to create a connected graph
        events = [
            ("symptom_001", EventType.SYMPTOM, {"name": "nausea"}),
            ("symptom_002", EventType.SYMPTOM, {"name": "headache"}),
            ("med_001", EventType.MEDICATION, {"name": "aspirin"}),
        ]
        
        for event_id, event_type, metadata in events:
            self.graph.add_event(event_id, event_type, metadata, self.test_timestamp)
        
        # Test radial relationships
        symptom_node = self.graph.get_node("symptom_001")
        assert symptom_node is not None
        
        # Should have a radial parent (nucleus or another node)
        radial_parent = self.graph.get_radial_parent("symptom_001")
        assert radial_parent is not None
        
        # Test neighbor retrieval
        neighbors = self.graph.get_neighbors("symptom_001")
        assert len(neighbors) >= 1  # At least the radial parent
    
    def test_custom_layer_policy(self):
        """Test custom layer policy configuration."""
        # Create graph with custom layer policy
        custom_policy = {
            EventType.SYMPTOM: 0,  # Symptoms go to layer 1 (parent 0)
            EventType.MEDICATION: 2,  # Medications go to layer 3 (parent 2)
        }
        
        custom_graph = OnionGraph(layer_policy=custom_policy)
        
        # Add events
        custom_graph.add_event("symptom_001", EventType.SYMPTOM, {"name": "fever"}, self.test_timestamp)
        custom_graph.add_event("med_001", EventType.MEDICATION, {"name": "tylenol"}, self.test_timestamp)
        
        # Check layer placement follows custom policy
        symptom_node = custom_graph.get_node("symptom_001")
        med_node = custom_graph.get_node("med_001")
        
        assert symptom_node.layer == 1  # Parent layer 0 + 1
        assert med_node.layer == 3      # Parent layer 2 + 1
    
    def test_node_validation(self):
        """Test node validation during creation."""
        # Test that invalid nodes raise appropriate errors
        with pytest.raises(ValueError, match="Layer must be non-negative"):
            Node(
                node_id="invalid",
                layer=-1,  # Invalid negative layer
                angle=0.0,
                radius=1.0,
                x=0.0, y=0.0, z=0.0,
                event_type=EventType.NOTE,
                metadata={},
                timestamp=self.test_timestamp
            )
        
        with pytest.raises(ValueError, match="Angle must be in"):
            Node(
                node_id="invalid",
                layer=0,
                angle=7.0,  # Invalid angle > 2π
                radius=1.0,
                x=0.0, y=0.0, z=0.0,
                event_type=EventType.NOTE,
                metadata={},
                timestamp=self.test_timestamp
            )
        
        with pytest.raises(ValueError, match="Radius must be positive"):
            Node(
                node_id="invalid",
                layer=0,
                angle=0.0,
                radius=-1.0,  # Invalid negative radius
                x=0.0, y=0.0, z=0.0,
                event_type=EventType.NOTE,
                metadata={},
                timestamp=self.test_timestamp
            )
    
    def test_graph_representation(self):
        """Test string representation of the graph."""
        # Add some nodes
        self.graph.add_event("test_001", EventType.SYMPTOM, {"name": "test"}, self.test_timestamp)
        
        repr_str = repr(self.graph)
        assert "OnionGraph" in repr_str
        assert "layers=" in repr_str
        assert "nodes=" in repr_str
        assert "edges=" in repr_str


class TestEventTypes:
    """Test suite for medical EventType functionality."""
    
    def test_all_medical_event_types(self):
        """Test that all medical event types are properly defined."""
        expected_types = {
            EventType.SYMPTOM,
            EventType.MEAL,
            EventType.MEDICATION,
            EventType.VITAL_SIGN,
            EventType.NOTE,
            EventType.IMAGE
        }
        
        # Check all types exist and have correct values
        assert EventType.SYMPTOM.value == "symptom"
        assert EventType.MEAL.value == "meal"
        assert EventType.MEDICATION.value == "medication"
        assert EventType.VITAL_SIGN.value == "vital_sign"
        assert EventType.NOTE.value == "note"
        assert EventType.IMAGE.value == "image"
        
        # Check we have all expected types
        all_types = set(EventType)
        assert all_types == expected_types


if __name__ == "__main__":
    pytest.main([__file__])
