"""
OnionGraphContextEngine - Temporal Graph Database for Medical Data

This is a placeholder package for the OnionGraphContextEngine.
The user will paste their existing OGContextEngine code here.

Key Features:
- Temporal graph storage with onion-layer architecture
- Ricci curvature calculations for anomaly detection
- Medical entity relationship modeling
- Time-based graph queries and analysis
- CRDT-based synchronization support

TODO: User needs to paste existing OGContextEngine implementation here.
"""

__version__ = "0.1.0"
__author__ = "SymptomOS Team"

# Placeholder imports - replace with actual OGContextEngine classes
from .curvature_analyzer import CurvatureAnalyzer
from .graph_engine import GraphEngine
from .medical_entities import MedicalEntity, Medication, Patient, Symptom
from .temporal_layer import TemporalLayer

__all__ = [
    "GraphEngine",
    "TemporalLayer",
    "CurvatureAnalyzer",
    "MedicalEntity",
    "Symptom",
    "Medication",
    "Patient",
]
