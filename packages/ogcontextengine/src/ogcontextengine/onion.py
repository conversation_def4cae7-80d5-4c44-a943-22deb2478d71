"""
Onion Graph - Core Data Structure

A biologically-inspired graph framework where nodes are organized in concentric layers
following onion growth patterns. Uses polar coordinates and golden angle placement
for optimal node distribution.

Mathematical Foundation:
- Polar coordinates: (ℓ, θ, r) where ℓ is layer, θ is angle, r is radius
- Golden angle placement: θₙ = φ·n (mod 2π) where φ = π·(3−√5)
- Radial growth: r(ℓ) = r₀ + ℓ·Δr

Author: <PERSON><PERSON> (for Invigilo Demo)
Version: 0.1
Adapted for SymptomOS medical domain
"""

import math
from dataclasses import dataclass
from enum import Enum
from typing import Any

# Type aliases for clarity
NodeID = str
Timestamp = float

# Mathematical constants (PEP8 compliant)
GOLDEN_ANGLE = math.pi * (3 - math.sqrt(5))  # φ = π·(3−√5) ≈ 2.399...
DEFAULT_R0 = 1.0  # Base radius
DEFAULT_DELTA_R = 1.0  # Layer thickness
DEFAULT_DELTA_Z = 0.1  # Vertical drift for spiral effect


class EventType(Enum):
    """Types of medical events that can be added to the graph"""
    SYMPTOM = "symptom"
    MEAL = "meal"
    MEDICATION = "medication"
    VITAL_SIGN = "vital_sign"
    NOTE = "note"
    IMAGE = "image"


@dataclass
class Node:
    """
    A node in the Onion Graph with polar coordinates and metadata.

    Attributes:
        node_id: Unique identifier
        layer: Shell index ℓ ∈ ℕ₀
        angle: Angular address θ ∈ [0,2π)
        radius: Radial distance r(ℓ)
        x, y, z: Cartesian coordinates for embedding
        event_type: Type of event this node represents
        metadata: Additional data associated with the node
        timestamp: When this node was created
    """
    node_id: NodeID
    layer: int
    angle: float
    radius: float
    x: float
    y: float
    z: float
    event_type: EventType
    metadata: dict[str, Any]
    timestamp: Timestamp

    def __post_init__(self):
        """Validate node coordinates"""
        if self.layer < 0:
            raise ValueError("Layer must be non-negative")
        if not (0 <= self.angle < 2 * math.pi):
            raise ValueError("Angle must be in [0, 2π)")
        if self.radius <= 0:
            raise ValueError("Radius must be positive")


@dataclass
class Edge:
    """
    An edge in the Onion Graph with weight and curvature properties.

    Attributes:
        source: Source node ID
        target: Target node ID
        weight: Edge weight w(e) = λ₁/d_ij + λ₂·s_ij
        edge_type: Type of edge (radial, tangential)
        euclidean_distance: Physical distance in embedding
        semantic_score: Semantic similarity score [0,1]
        curvature: Ricci curvature approximation
    """
    source: NodeID
    target: NodeID
    weight: float
    edge_type: str  # "radial" or "tangential"
    euclidean_distance: float
    semantic_score: float
    curvature: float | None = None


class Layer:
    """
    A single layer (shell) in the Onion Graph.

    Manages nodes at a specific layer index with golden angle placement.
    """

    def __init__(self, layer_index: int, radius: float):
        self.layer_index = layer_index
        self.radius = radius
        self.nodes: dict[NodeID, Node] = {}
        self.node_count = 0  # For golden angle calculation

    def add_node(self, node: Node) -> None:
        """Add a node to this layer"""
        if node.layer != self.layer_index:
            raise ValueError(f"Node layer {node.layer} doesn't match layer index {self.layer_index}")
        self.nodes[node.node_id] = node

    def get_next_angle(self) -> float:
        """Calculate the next golden angle position for a new node"""
        angle = (GOLDEN_ANGLE * self.node_count) % (2 * math.pi)
        self.node_count += 1
        return angle

    def get_angular_neighbors(self, node_id: NodeID) -> list[NodeID]:
        """Get the angular neighbors of a node in this layer"""
        if node_id not in self.nodes:
            return []

        # Sort nodes by angle
        sorted_nodes = sorted(self.nodes.items(), key=lambda x: x[1].angle)
        node_ids = [nid for nid, _ in sorted_nodes]

        try:
            idx = node_ids.index(node_id)
            neighbors = []

            # Previous neighbor (circular)
            if len(node_ids) > 1:
                prev_idx = (idx - 1) % len(node_ids)
                neighbors.append(node_ids[prev_idx])

            # Next neighbor (circular)
            if len(node_ids) > 1:
                next_idx = (idx + 1) % len(node_ids)
                neighbors.append(node_ids[next_idx])

            return neighbors
        except ValueError:
            return []


class OnionGraph:
    """
    The main Onion Graph data structure.

    Manages layers, nodes, and edges with biologically-inspired growth patterns.
    Provides O(1) insertion and efficient traversal algorithms.
    """

    def __init__(self, r0: float = DEFAULT_R0, delta_r: float = DEFAULT_DELTA_R,
                 delta_z: float = DEFAULT_DELTA_Z, layer_policy: dict[EventType, int] | None = None):
        self.r0 = r0
        self.delta_r = delta_r
        self.delta_z = delta_z

        # Parameterized layer policy for project-specific layering
        self.layer_policy = layer_policy or {
            EventType.SYMPTOM: 1,
            EventType.MEAL: 2,
            EventType.MEDICATION: 1,
            EventType.VITAL_SIGN: 2,
            EventType.NOTE: 0,
            EventType.IMAGE: 1
        }

        # Core data structures
        self.layers: dict[int, Layer] = {}
        self.nodes: dict[NodeID, Node] = {}
        self.edges: dict[tuple[NodeID, NodeID], Edge] = {}

        # Initialize seed layer (layer 0) with nucleus node
        self._initialize_seed()

    def _initialize_seed(self) -> None:
        """Initialize the seed layer with a single nucleus node"""
        seed_layer = Layer(0, self.r0)
        self.layers[0] = seed_layer

        # Create nucleus node at center
        nucleus = Node(
            node_id="nucleus",
            layer=0,
            angle=0.0,
            radius=self.r0,
            x=self.r0,  # Slightly offset from origin
            y=0.0,
            z=0.0,
            event_type=EventType.NOTE,
            metadata={"type": "nucleus", "description": "Graph center"},
            timestamp=0.0
        )

        self.nodes["nucleus"] = nucleus
        seed_layer.add_node(nucleus)

    def _calculate_radius(self, layer: int) -> float:
        """Calculate radius for a given layer: r(ℓ) = r₀ + ℓ·Δr"""
        return self.r0 + layer * self.delta_r

    def _polar_to_cartesian(self, layer: int, angle: float, radius: float) -> tuple[float, float, float]:
        """Convert polar coordinates to Cartesian with optional spiral effect"""
        x = radius * math.cos(angle)
        y = radius * math.sin(angle)
        z = layer * self.delta_z  # Vertical drift for spiral
        return x, y, z

    def _determine_parent_layer(self, event_type: EventType) -> int:
        """Determine parent layer based on event type using configurable policy"""
        return self.layer_policy.get(event_type, 1)

    def add_event(self, event_id: NodeID, event_type: EventType,
                  metadata: dict[str, Any], timestamp: Timestamp) -> NodeID:
        """
        Add a new event to the graph following the growth kernel algorithm.

        Returns the ID of the created node.
        """
        # Step 1: Identify parent layer
        parent_layer_idx = self._determine_parent_layer(event_type)
        target_layer_idx = parent_layer_idx + 1

        # Step 2: Ensure target layer exists
        if target_layer_idx not in self.layers:
            radius = self._calculate_radius(target_layer_idx)
            self.layers[target_layer_idx] = Layer(target_layer_idx, radius)

        target_layer = self.layers[target_layer_idx]

        # Step 3: Compute angular slot using golden angle
        angle = target_layer.get_next_angle()
        radius = target_layer.radius

        # Step 4: Create node with Cartesian coordinates
        x, y, z = self._polar_to_cartesian(target_layer_idx, angle, radius)

        node = Node(
            node_id=event_id,
            layer=target_layer_idx,
            angle=angle,
            radius=radius,
            x=x, y=y, z=z,
            event_type=event_type,
            metadata=metadata,
            timestamp=timestamp
        )

        # Step 5: Add node to structures
        self.nodes[event_id] = node
        target_layer.add_node(node)

        # Step 6: Create edges (will be implemented in next phase)
        self._create_edges_for_node(event_id)

        return event_id

    def _create_edges_for_node(self, node_id: NodeID) -> None:
        """Create radial and tangential edges for a new node"""
        from ogcontextengine.curvature import EdgeManager

        if not hasattr(self, '_edge_manager'):
            self._edge_manager = EdgeManager()

        node = self.nodes[node_id]

        # Create radial edge to parent layer
        if node.layer > 0:
            parent_id = None

            # Find appropriate parent node by searching backwards through layers
            for search_layer in range(node.layer - 1, -1, -1):
                if search_layer in self.layers and self.layers[search_layer].nodes:
                    # Found a layer with nodes, connect to the first one
                    parent_id = list(self.layers[search_layer].nodes.keys())[0]
                    break

            # If no parent found, connect to nucleus as fallback
            if not parent_id:
                parent_id = "nucleus"

            # Create the edge
            if parent_id and parent_id in self.nodes:
                parent_node = self.nodes[parent_id]
                edge = self._edge_manager.create_radial_edge(parent_node, node)
                self.edges[(parent_id, node_id)] = edge

        # Create tangential edges to angular neighbors in same layer
        current_layer = self.layers[node.layer]
        neighbors = current_layer.get_angular_neighbors(node_id)

        for neighbor_id in neighbors:
            if neighbor_id in self.nodes:
                neighbor_node = self.nodes[neighbor_id]
                if self._edge_manager.should_create_tangential_edge(node, neighbor_node):
                    edge = self._edge_manager.create_tangential_edge(node, neighbor_node)
                    # Create bidirectional tangential edges
                    self.edges[(node_id, neighbor_id)] = edge
                    # Also create reverse edge for easier traversal
                    reverse_edge = self._edge_manager.create_tangential_edge(neighbor_node, node)
                    self.edges[(neighbor_id, node_id)] = reverse_edge

    def get_layer_nodes(self, layer: int) -> list[Node]:
        """Get all nodes in a specific layer"""
        if layer not in self.layers:
            return []
        return list(self.layers[layer].nodes.values())

    def get_node(self, node_id: NodeID) -> Node | None:
        """Get a node by ID"""
        return self.nodes.get(node_id)

    def get_layer_count(self) -> int:
        """Get the number of layers in the graph"""
        return len(self.layers)

    def get_node_count(self) -> int:
        """Get the total number of nodes in the graph"""
        return len(self.nodes)

    def get_edge_count(self) -> int:
        """Get the total number of edges in the graph"""
        return len(self.edges)

    def update_curvatures(self) -> None:
        """Update Ricci curvature values for all edges"""
        if hasattr(self, '_edge_manager'):
            self._edge_manager.update_edge_curvatures(self.edges, self.nodes)

    def get_neighbors(self, node_id: NodeID) -> list[NodeID]:
        """Get all neighboring nodes of a given node"""
        neighbors = []
        for (source, target), edge in self.edges.items():
            if source == node_id:
                neighbors.append(target)
            elif target == node_id and edge.edge_type == "tangential":
                neighbors.append(source)
        return neighbors

    def get_radial_children(self, node_id: NodeID) -> list[NodeID]:
        """Get all radial children of a node (nodes in the next layer connected to this node)"""
        children = []
        for (source, target), edge in self.edges.items():
            if source == node_id and edge.edge_type == "radial":
                children.append(target)
        return children

    def get_radial_parent(self, node_id: NodeID) -> NodeID | None:
        """Get the radial parent of a node (node in the previous layer)"""
        for (source, target), edge in self.edges.items():
            if target == node_id and edge.edge_type == "radial":
                return source
        return None

    def __repr__(self) -> str:
        return f"OnionGraph(layers={len(self.layers)}, nodes={len(self.nodes)}, edges={len(self.edges)})"
