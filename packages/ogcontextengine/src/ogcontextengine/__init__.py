"""
OnionGraphContextEngine - Medical Temporal Graph Database

A biologically-inspired graph framework for medical data with temporal layers,
Ricci curvature analysis, and anomaly detection capabilities.

This package provides the core OnionGraph implementation adapted for
SymptomOS medical domain with EventTypes for symptoms, medications,
vital signs, meals, notes, and images.

Author: SymptomOS Team
Version: 0.1
"""

# Core OnionGraph classes
# Edge management and curvature analysis
from .curvature import CurvatureAnalyzer, EdgeManager
from .onion import (
    DEFAULT_DELTA_R,
    DEFAULT_DELTA_Z,
    DEFAULT_R0,
    GOLDEN_ANGLE,
    Edge,
    EventType,
    Layer,
    Node,
    NodeID,
    OnionGraph,
    Timestamp,
)

# Import existing classes for backward compatibility with graph_manager.py
# These will be implemented as adapters/wrappers around OnionGraph
try:
    from .graph_engine import GraphEngine
    from .medical_entities import MedicalEntity, Medication, Patient, Symptom
    from .temporal_layer import TemporalLayer
except ImportError:
    # Create placeholder classes if the modules don't exist yet
    class GraphEngine:
        """Placeholder GraphEngine - will be implemented as OnionGraph adapter"""
        def __init__(self, config=None):
            self.onion_graph = OnionGraph()

        def add_node(self, node_id, properties, timestamp):
            # Convert to OnionGraph format
            event_type = EventType.NOTE  # Default
            if 'event_type' in properties:
                event_type = EventType(properties['event_type'])
            return self.onion_graph.add_event(node_id, event_type, properties, timestamp)

        def query_nodes(self, filters):
            # Placeholder implementation
            return []

        def add_edge(self, source_id, target_id, relationship_type, properties, timestamp):
            # Placeholder implementation
            return True

        def query_edges(self, filters):
            # Placeholder implementation
            return []

        def get_temporal_layer(self, timestamp):
            # Placeholder implementation
            return None

    class TemporalLayer:
        """Placeholder TemporalLayer"""
        pass

    class CurvatureAnalyzer:
        """Placeholder CurvatureAnalyzer"""
        def __init__(self, graph_engine):
            self.graph_engine = graph_engine

        def calculate_ricci_curvature(self, node_id):
            return 0.0

        def detect_anomalies(self, threshold):
            return []

        def analyze_temporal_curvature(self, start_time, end_time):
            return {"curvature_trend": "stable"}

    class MedicalEntity:
        """Placeholder MedicalEntity"""
        pass

    class Symptom(MedicalEntity):
        """Placeholder Symptom"""
        pass

    class Medication(MedicalEntity):
        """Placeholder Medication"""
        pass

    class Patient(MedicalEntity):
        """Placeholder Patient"""
        pass

# Package metadata
__version__ = "0.1.0"
__author__ = "SymptomOS Team"
__email__ = "<EMAIL>"

# Public API
__all__ = [
    # Core OnionGraph
    "OnionGraph",
    "Node",
    "Edge",
    "Layer",
    "EventType",
    "NodeID",
    "Timestamp",

    # Constants
    "GOLDEN_ANGLE",
    "DEFAULT_R0",
    "DEFAULT_DELTA_R",
    "DEFAULT_DELTA_Z",

    # Edge management
    "EdgeManager",
    "CurvatureAnalyzer",

    # Graph engine compatibility
    "GraphEngine",
    "TemporalLayer",

    # Medical entities
    "MedicalEntity",
    "Symptom",
    "Medication",
    "Patient"
]
