.PHONY: help install dev build test lint clean docker-dev docker-down ibm-deploy

# Default target
help: ## Show this help message
	@echo "SymptomOS Development Commands"
	@echo "=============================="
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

install: ## Install all dependencies
	@echo "Installing Node.js dependencies..."
	pnpm install
	@echo "Installing Python dependencies..."
	poetry install
	@echo "Setting up pre-commit hooks..."
	poetry run pre-commit install

dev: ## Start development environment
	@echo "Starting development environment..."
	turbo run dev

build: ## Build all packages and apps
	@echo "Building all packages and apps..."
	turbo run build

test: ## Run all tests
	@echo "Running tests..."
	turbo run test
	poetry run pytest

lint: ## Run linting and formatting
	@echo "Running linters..."
	turbo run lint
	poetry run ruff check .
	poetry run black --check .
	poetry run mypy .

format: ## Format code
	@echo "Formatting code..."
	pnpm run format
	poetry run black .
	poetry run ruff --fix .

type-check: ## Run type checking
	@echo "Running type checks..."
	turbo run type-check
	poetry run mypy .

clean: ## Clean build artifacts
	@echo "Cleaning build artifacts..."
	turbo run clean
	rm -rf node_modules
	rm -rf .turbo
	find . -name "__pycache__" -type d -exec rm -rf {} +
	find . -name "*.pyc" -delete
	find . -name ".pytest_cache" -type d -exec rm -rf {} +

docker-dev: ## Start development environment with Docker
	@echo "Starting Docker development environment..."
	docker compose -f infra/docker/dev.compose.yml up --build

docker-down: ## Stop Docker development environment
	@echo "Stopping Docker development environment..."
	docker compose -f infra/docker/dev.compose.yml down

docker-clean: ## Clean Docker resources
	@echo "Cleaning Docker resources..."
	docker compose -f infra/docker/dev.compose.yml down -v
	docker system prune -f

ibm-deploy: ## Deploy to IBM Cloud Engine
	@echo "Deploying to IBM Cloud Engine..."
	@echo "TODO: Implement IBM Cloud deployment"
	# cd infra/terraform-ibm && terraform apply
	# ./infra/docker/build_all.sh
	# ./scripts/deploy-ibm.sh

setup-env: ## Set up environment files
	@echo "Setting up environment files..."
	@if [ ! -f .env ]; then cp .env.sample .env; echo "Created .env from .env.sample"; fi

health-check: ## Check health of all services
	@echo "Checking service health..."
	@echo "TODO: Implement health checks"
	# curl -f http://localhost:8000/healthz || echo "granite-gateway: DOWN"
	# curl -f http://localhost:8001/healthz || echo "parser-service: DOWN"
	# curl -f http://localhost:8002/healthz || echo "ogce-graph: DOWN"

logs: ## Show logs from Docker services
	docker compose -f infra/docker/dev.compose.yml logs -f

ps: ## Show running Docker services
	docker compose -f infra/docker/dev.compose.yml ps
