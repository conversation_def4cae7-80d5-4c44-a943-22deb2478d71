[tool:pytest]
markers =
    unit: Unit tests for individual components
    cloud: Tests requiring cloud Granite models
    integration: Integration tests with external services
    performance: Performance and load tests
    safety: Security and safety validation tests
    slow: Slow running tests

testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings

filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
