# OnionGraphContextEngine Documentation

## Overview

The OnionGraphContextEngine (OGCE) is a biologically-inspired graph framework designed for medical temporal data storage and analysis. It organizes nodes in concentric layers following onion growth patterns, using polar coordinates and golden angle placement for optimal node distribution.

## Key Features

- **Temporal Graph Storage**: Nodes organized in time-based layers
- **Medical Domain EventTypes**: Specialized for symptoms, medications, vital signs, meals, notes, and images
- **Ricci Curvature Analysis**: Advanced mathematical analysis for anomaly detection
- **Golden Angle Placement**: Optimal node distribution using mathematical constants
- **Pluggable Edge Logic**: Configurable edge creation policies

## Installation

```bash
# Install from source
cd packages/ogcontextengine
pip install -e .

# Or install with development dependencies
pip install -e ".[dev]"
```

## Quick Start

### Basic Usage

```python
from ogcontextengine import OnionGraph, EventType
import time

# Create a new OnionGraph
g = OnionGraph()

# Add medical events
g.add_event("evt1", EventType.SYMPTOM, {"name": "nausea", "intensity": 5}, timestamp=time.time())
g.add_event("evt2", EventType.MEDICATION, {"name": "aspirin", "dosage": "500mg"}, timestamp=time.time())
g.add_event("evt3", EventType.VITAL_SIGN, {"bp": "120/80", "hr": 72}, timestamp=time.time())

# Query the graph
print(f"Graph has {g.get_node_count()} nodes in {g.get_layer_count()} layers")

# Get nodes by layer
layer_2_nodes = g.get_layer_nodes(2)
for node in layer_2_nodes:
    print(f"Node {node.node_id}: {node.event_type.value}")
```

### Custom Layer Policy

```python
from ogcontextengine import OnionGraph, EventType

# Define custom layer mapping
custom_policy = {
    EventType.SYMPTOM: 0,      # Symptoms go to layer 1 (parent 0)
    EventType.MEDICATION: 2,   # Medications go to layer 3 (parent 2)
    EventType.VITAL_SIGN: 1,   # Vital signs go to layer 2 (parent 1)
}

# Create graph with custom policy
g = OnionGraph(layer_policy=custom_policy)

# Add events - they'll be placed according to custom policy
g.add_event("symptom_001", EventType.SYMPTOM, {"name": "fever"}, time.time())
g.add_event("med_001", EventType.MEDICATION, {"name": "tylenol"}, time.time())
```

### Edge Analysis

```python
from ogcontextengine import OnionGraph, EventType

g = OnionGraph()

# Add several events
events = [
    ("symptom_001", EventType.SYMPTOM, {"name": "headache"}),
    ("symptom_002", EventType.SYMPTOM, {"name": "nausea"}),
    ("med_001", EventType.MEDICATION, {"name": "aspirin"}),
]

for event_id, event_type, metadata in events:
    g.add_event(event_id, event_type, metadata, time.time())

# Analyze relationships
node = g.get_node("symptom_001")
neighbors = g.get_neighbors("symptom_001")
parent = g.get_radial_parent("symptom_001")

print(f"Node {node.node_id} has {len(neighbors)} neighbors")
print(f"Radial parent: {parent}")

# Update curvature values
g.update_curvatures()
```

## API Reference

### Core Classes

#### OnionGraph

The main graph data structure.

**Constructor:**
```python
OnionGraph(r0=1.0, delta_r=1.0, delta_z=0.1, layer_policy=None)
```

**Parameters:**
- `r0`: Base radius for layer 0
- `delta_r`: Radius increment between layers
- `delta_z`: Vertical offset for spiral effect
- `layer_policy`: Dict mapping EventType to parent layer index

**Key Methods:**

- `add_event(event_id, event_type, metadata, timestamp)`: Add a new event node
- `get_node(node_id)`: Retrieve a node by ID
- `get_layer_nodes(layer)`: Get all nodes in a specific layer
- `get_neighbors(node_id)`: Get neighboring nodes
- `get_radial_parent(node_id)`: Get parent node in previous layer
- `get_radial_children(node_id)`: Get child nodes in next layer
- `update_curvatures()`: Update Ricci curvature values

#### Node

Represents a single node in the graph.

**Attributes:**
- `node_id`: Unique identifier
- `layer`: Layer index (shell number)
- `angle`: Angular position in polar coordinates
- `radius`: Radial distance from center
- `x, y, z`: Cartesian coordinates
- `event_type`: Medical event type
- `metadata`: Additional data dictionary
- `timestamp`: Creation timestamp

#### Edge

Represents a connection between two nodes.

**Attributes:**
- `source`: Source node ID
- `target`: Target node ID
- `weight`: Edge weight (distance + semantic)
- `edge_type`: "radial" or "tangential"
- `euclidean_distance`: Physical distance in embedding
- `semantic_score`: Semantic similarity [0,1]
- `curvature`: Ricci curvature value

#### EventType

Medical domain event types.

**Values:**
- `SYMPTOM`: Patient symptoms
- `MEAL`: Food intake events
- `MEDICATION`: Medication administration
- `VITAL_SIGN`: Vital sign measurements
- `NOTE`: Text notes and observations
- `IMAGE`: Medical images and attachments

### Edge Management

#### EdgeManager

Handles edge creation and weight calculation.

**Key Methods:**
- `create_radial_edge(parent, child)`: Create parent-child edge
- `create_tangential_edge(node_a, node_b)`: Create same-layer edge
- `should_create_tangential_edge(node_a, node_b)`: Edge creation policy
- `update_edge_curvatures(edges, nodes)`: Update curvature values

#### CurvatureAnalyzer

Analyzes graph curvature for medical insights.

**Key Methods:**
- `calculate_ricci_curvature(node_id)`: Node curvature calculation
- `detect_anomalies(threshold)`: Anomaly detection
- `analyze_temporal_curvature(start_time, end_time)`: Temporal analysis

## Mathematical Foundation

### Polar Coordinates

Nodes are positioned using polar coordinates (ℓ, θ, r):
- **ℓ**: Layer index (shell number)
- **θ**: Angular position using golden angle
- **r**: Radial distance = r₀ + ℓ·Δr

### Golden Angle Placement

Angular positions follow the golden angle sequence:
```
θₙ = φ·n (mod 2π)
where φ = π·(3−√5) ≈ 2.399...
```

This ensures optimal distribution without clustering.

### Edge Weights

Edge weights combine distance and semantic similarity:
```
w(e) = λ₁/d_ij + λ₂·s_ij
```
- **d_ij**: Euclidean distance between nodes
- **s_ij**: Semantic similarity score [0,1]
- **λ₁, λ₂**: Weight coefficients

## Integration with SymptomOS

The OnionGraphContextEngine integrates with SymptomOS services:

### Graph Manager Integration

```python
from ogcontextengine import GraphEngine, CurvatureAnalyzer

# Used by services/ogce-graph/app/graph_manager.py
graph_engine = GraphEngine(config)
curvature_analyzer = CurvatureAnalyzer(graph_engine)
```

### Medical Entity Support

```python
from ogcontextengine import MedicalEntity, Symptom, Medication, Patient

# Specialized medical entity classes (placeholder implementations)
symptom = Symptom(name="headache", intensity=7)
medication = Medication(name="aspirin", dosage="500mg")
```

## Development

### Running Tests

```bash
cd packages/ogcontextengine
PYTHONPATH=src python -m pytest tests/ -v
```

### Linting and Type Checking

```bash
ruff check --fix src/
mypy --strict src/
```

### Contributing

1. Follow PEP8 style guidelines
2. Add type hints to all functions
3. Write comprehensive tests
4. Update documentation for API changes

## Future Enhancements

### Planned Features

1. **Real Ricci Curvature**: Implement discrete Ricci curvature calculations
2. **Advanced Anomaly Detection**: Machine learning-based pattern recognition
3. **Temporal Queries**: Time-range graph queries and analysis
4. **Medical Ontology Integration**: Semantic similarity using medical vocabularies
5. **CRDT Synchronization**: Conflict-free replicated data types for distributed graphs

### Research Directions

- Ollivier-Ricci curvature for discrete spaces
- Forman-Ricci curvature for simplicial complexes
- Graph neural networks for medical prediction
- Temporal graph embedding techniques

## License

MIT License - see LICENSE file for details.

## Support

For questions and support:
- GitHub Issues: [SymptomOS_IBM Issues](https://github.com/prolificbrain/SymptomOS_IBM/issues)
- Documentation: [SymptomOS Docs](https://github.com/prolificbrain/SymptomOS_IBM/tree/main/docs)
