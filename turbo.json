{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "build/**", ".next/**", "out/**", "target/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"], "outputs": []}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "clean": {"cache": false}}}