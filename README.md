# SymptomOS

> Real-time, voice-first symptom tracking and medical memory system powered by IBM Granite and OnionGraphContextEngine

[![CI](https://github.com/symptomos/symptomos/workflows/CI/badge.svg)](https://github.com/symptomos/symptomos/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## 🎯 Vision

SymptomOS transforms how patients track symptoms and how doctors access medical histories. Using voice-first interaction and temporal graph storage, it creates a comprehensive medical memory system that grows smarter over time.

## 🏗️ Architecture

SymptomOS is built as a polyglot monorepo with:

- **Voice-first capture**: Record symptoms naturally through speech
- **Intelligent parsing**: Extract medical entities using IBM Granite models
- **Temporal graph storage**: OnionGraphContextEngine for structured medical data
- **Real-time sync**: Offline-first with cloud synchronization
- **Anomaly detection**: Curvature-based pattern recognition for health alerts

## 🚀 Quick Start

### Prerequisites

- Node.js 20+ and pnpm 8+
- Python 3.11+ and Poetry
- Docker and Docker Compose
- IBM Cloud account (for Granite models)

### Installation

```bash
# Clone and install dependencies
git clone https://github.com/symptomos/symptomos.git
cd symptomos
make install

# Set up environment
make setup-env
# Edit .env with your IBM watsonx.ai credentials

# Start development environment
make docker-dev
```

### Development

```bash
# Start all services in development mode
make dev

# Run tests
make test

# Lint and format code
make lint format

# Build for production
make build
```

## 📁 Project Structure

```
symptomos/
├── apps/                    # UI applications
│   ├── patient-mobile/      # Expo React Native app
│   └── doctor-desktop/      # Electron desktop app
├── packages/                # Shared libraries
│   ├── ogcontextengine/     # Temporal graph engine
│   ├── ui-kit/              # Shared React components
│   └── proto/               # gRPC definitions
├── services/                # Backend microservices
│   ├── granite-gateway/     # IBM Granite API proxy
│   ├── parser-service/      # Medical entity extraction
│   ├── ogce-graph/          # Graph database service
│   ├── sync-worker/         # Data synchronization
│   └── alert-worker/        # Anomaly detection
└── infra/                   # Infrastructure as code
    ├── docker/              # Container definitions
    ├── k8s/                 # Kubernetes manifests
    └── terraform-ibm/       # IBM Cloud resources
```

## 🔧 Services

### Granite Gateway
FastAPI service that proxies requests to IBM watsonx.ai:
- `/v1/asr` - Speech-to-text using Granite Speech
- `/v1/generate` - Text generation with Granite models
- `/v1/tts` - Text-to-speech synthesis

### Parser Service
Extracts medical entities from natural language:
- Regex-based extraction for common patterns
- LLM fallback for complex medical terminology
- Structured output for graph ingestion

### OGCE Graph
Manages the OnionGraphContextEngine:
- REST and gRPC APIs for graph operations
- Temporal layering for time-based queries
- Curvature calculations for anomaly detection

### Sync Worker
Handles data synchronization:
- CRDT-based conflict resolution
- Offline-first architecture
- Redis-backed event streaming

### Alert Worker
Monitors for health anomalies:
- Ricci curvature analysis
- Pattern recognition
- Push notifications

## 🎨 User Interfaces

### Patient Mobile App
- Voice recording and transcription
- Symptom timeline visualization
- Offline-first data capture
- Push notifications for reminders

### Doctor Desktop App
- Comprehensive patient dashboards
- Medical history analysis
- Report generation
- Integration with existing EMR systems

## 🔐 Security & Privacy

- HIPAA/GDPR compliant data handling
- End-to-end encryption for sensitive data
- Minimal PHI storage with hash-based identifiers
- Secure IBM Cloud integration

## 🧪 Testing

```bash
# Run all tests
make test

# Run specific test suites
pnpm test --filter=patient-mobile
poetry run pytest services/parser-service/tests/

# Run integration tests
make docker-dev
./scripts/integration-tests.sh
```

## 🚀 Deployment

### Local Development
```bash
make docker-dev
```

### IBM Cloud Engine
```bash
make ibm-deploy
```

## 📊 Monitoring

Health check endpoints are available at `/healthz` for all services:
- Granite Gateway: `http://localhost:8000/healthz`
- Parser Service: `http://localhost:8001/healthz`
- OGCE Graph: `http://localhost:8002/healthz`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Run the test suite: `make test`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

See [CONTRIBUTING.md](CONTRIBUTING.md) for detailed guidelines.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- IBM Granite team for the foundational AI models
- OnionGraphContextEngine for temporal graph capabilities
- The open-source community for the amazing tools and libraries

---

**Built with ❤️ for better healthcare outcomes**
