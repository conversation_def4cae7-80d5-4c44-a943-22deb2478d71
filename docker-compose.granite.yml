# Docker Compose for Granite Agent Pipeline
# Complete agent-per-task architecture with Redis message bus

version: '3.8'

services:
  # Redis Message Bus
  redis:
    image: redis:7-alpine
    container_name: symptomos-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Validator Service - Security Gateway
  validator-service:
    build:
      context: ./services/validator-service
      dockerfile: Dockerfile
    container_name: symptomos-validator
    ports:
      - "8020:8020"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET=${JWT_SECRET:-your-secret-key-here}
      - MODEL_MAP_PATH=/app/config/model-map.yaml
      - LOG_LEVEL=INFO
    volumes:
      - ./config/model-map.yaml:/app/config/model-map.yaml:ro
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8020/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ASR Agent - Speech to Text
  asr-agent:
    build:
      context: ./services/asr-agent
      dockerfile: Dockerfile
    container_name: symptomos-asr
    ports:
      - "8010:8010"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - GRANITE_MODEL_ID=ibm/granite-speech-3-3-8b
      - WATSONX_API_KEY=${WATSONX_API_KEY}
      - WATSONX_PROJECT_ID=${WATSONX_PROJECT_ID}
      - CHUNK_WINDOW_SECONDS=15
      - SAMPLE_RATE=16000
    depends_on:
      - redis
      - validator-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8010/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Parser Agent - Entity Extraction
  parser-agent:
    build:
      context: ./services/parser-agent
      dockerfile: Dockerfile
    container_name: symptomos-parser
    ports:
      - "8011:8011"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - GRANITE_MODEL_ID=ibm/granite-3-3-8b-instruct
      - WATSONX_API_KEY=${WATSONX_API_KEY}
      - WATSONX_PROJECT_ID=${WATSONX_PROJECT_ID}
      - TEMPERATURE=0.0
      - TOP_P=0.1
    depends_on:
      - redis
      - validator-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8011/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Embedding Agent - Vector Search
  embed-agent:
    build:
      context: ./services/embed-agent
      dockerfile: Dockerfile
    container_name: symptomos-embed
    ports:
      - "8012:8012"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - GRANITE_MODEL_ID=ibm/granite-embedding-107m-multilingual
      - WATSONX_API_KEY=${WATSONX_API_KEY}
      - WATSONX_PROJECT_ID=${WATSONX_PROJECT_ID}
      - CACHE_TTL=3600
      - BATCH_SIZE=32
    depends_on:
      - redis
      - validator-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8012/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Summary Agent - Medical Summaries
  summary-agent:
    build:
      context: ./services/summary-agent
      dockerfile: Dockerfile
    container_name: symptomos-summary
    ports:
      - "8013:8013"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - GRANITE_MODEL_ID=ibm/granite-3-3-8b-instruct
      - WATSONX_API_KEY=${WATSONX_API_KEY}
      - WATSONX_PROJECT_ID=${WATSONX_PROJECT_ID}
      - TEMPERATURE=0.3
      - TOP_P=0.9
      - MAX_CONTEXT_TOKENS=131072
    depends_on:
      - redis
      - validator-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8013/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Guardian Agent - Safety Validation
  guardian-agent:
    build:
      context: ./services/guardian-agent
      dockerfile: Dockerfile
    container_name: symptomos-guardian
    ports:
      - "8014:8014"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - GRANITE_MODEL_ID=ibm/granite-guardian-3-8b
      - WATSONX_API_KEY=${WATSONX_API_KEY}
      - WATSONX_PROJECT_ID=${WATSONX_PROJECT_ID}
      - TEMPERATURE=0.0
      - SAFETY_THRESHOLD=0.8
    depends_on:
      - redis
      - validator-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8014/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Vision Agent - Image Analysis
  vision-agent:
    build:
      context: ./services/vision-agent
      dockerfile: Dockerfile
    container_name: symptomos-vision
    ports:
      - "8015:8015"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - GRANITE_MODEL_ID=ibm/granite-vision-3.2-2b
      - WATSONX_API_KEY=${WATSONX_API_KEY}
      - WATSONX_PROJECT_ID=${WATSONX_PROJECT_ID}
      - MAX_IMAGE_SIZE=2048x2048
    depends_on:
      - redis
      - validator-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8015/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # OGCE Graph Service - OnionGraph Integration
  ogce-graph:
    build:
      context: ./services/ogce-graph
      dockerfile: Dockerfile
    container_name: symptomos-ogce
    ports:
      - "8030:8030"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=${DATABASE_URL:-********************************************/symptomos}
    depends_on:
      - redis
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8030/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: symptomos-postgres
    environment:
      - POSTGRES_DB=symptomos
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: symptomos-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - validator-service
      - asr-agent
      - parser-agent
      - embed-agent
      - summary-agent
      - guardian-agent
      - vision-agent
      - ogce-graph
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    container_name: symptomos-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  grafana:
    image: grafana/grafana:latest
    container_name: symptomos-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources:ro

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  default:
    name: symptomos-network
    driver: bridge
